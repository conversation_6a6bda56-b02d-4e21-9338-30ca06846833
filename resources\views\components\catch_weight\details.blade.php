<!-- Modal -->
<div class="modal fade text-xs-left" id="CW_detail" tabindex="-1" role="dialog" aria-labelledby="CW_detailLabel1"
    aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title modalheader"><b>{{ __('mobile.title.weight_detail') }}</b></h4>
            </div>
            <div class="modal-body">
                <div class="container">
                    <div class="row">
                        <span class="col-xs-12 col-md-12 col-lg-12"><strong>{{ $itemnum }}</strong></span>
                    </div>
                    <div class="row">
                        <span class="col-xs-4 col-md-4 col-lg-4">{{ __('mobile.label.total_weight') }}</span>
                        <span class="col-xs-8 col-md-8 col-lg-8">0.00 KG</span>
                    </div>
                    <div class="row pb-1">
                        <span class="col-xs-4 col-md-4 col-lg-4">{{ __('mobile.label.count') }}</span>
                        <span class="col-xs-8 col-md-8 col-lg-8">0</span>
                    </div>
                </div>
                <table id="detailtable" class="table table-xs table-bordered" style="width:100%;">
                    <thead>
                        <tr>
                            <th>{{ __('mobile.placeholder.lot_num') }}</th>
                            <th>{{ __('mobile.label.expiry_date') }}</th>
                            <th class="text-right">{{ __('mobile.label.box') }}</th>
                            <th class="text-right">{{ __('mobile.label.weight') }}</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
            <div class="modal-footer text-center">
                <button type="button" class="btn btn-warning" data-dismiss="modal">{{ __('mobile.button.cancel') }}</button>
                <button type="button" class="btn btn-danger" onclick="deleteRow()">{{ __('mobile.button.delete') }}</button>
            </div>
        </div>
    </div>
</div>

<style>
    input#search {
        padding: 0.4rem 0.75rem;
    }

    button.close>span {
        font-size: 20pt;
    }

    h4.modal-title.modalheader>b {
        font-size: 12pt;
    }

    #pagtable_paginate {
        margin-top: 15px;
    }

    #pagtable tbody {
        width: 100%;
        display: table;
    }

    #pagtable tr {
        width: 100%;
        padding: 0px !important;
    }

    #pagtable td {
        width: 100% !important;
        padding: 0px !important;
    }

    #pagtable_wrapper div.dataTables_paginate {
        float: none !important;
    }

    #pagtable .list-group-item {
        padding: 0.1rem 0.7rem;
        margin: 0px;
        border: 1px solid #ddd;
        width: auto;
        margin-bottom: -3px !important;
    }

    div.modal-body {
        padding-left: 0px;
        padding-right: 0px;
        padding-bottom: 0px;
    }

    .active {
        background-color: #548ab9 !important;
        color: #fff !important;
    }

    #detailtable tbody tr {
        cursor: pointer;
        transition: background-color 0.2s ease;
    }

    #detailtable tbody tr:hover {
        background-color: #f5f5f5;
    }

    #detailtable tbody tr.active:hover {
        background-color: #4a7ba7 !important;
    }
</style>

<script type="text/javascript">

    $(document).ready(function() {
        $("#CW_detail").on('hidden.bs.modal', function() {
        });
    });

    function openDetails() {
        // Check if there are any added lots
        if ($('#addedLot tbody tr').length === 0) {
            // Show warning if no lots have been added
            Swal.fire({
                title: 'No Lots Added',
                text: 'Please add at least one lot before viewing details.',
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            return false;
        }

        $('#CW_detail').modal('show');

        // get data from addedLot table's tbody and append to detailtable
        var rows = $('#addedLot tbody tr');
        $('#detailtable tbody').empty(); // clear the table body

        var modalTotalWeight = 0;
        var modalTotalCount = 0;

        rows.each(function(index, row) {
            var inputs = $(row).find('input');
            var lot_num = inputs.filter('[name="arr_lot_num[]"]').val() || '';
            var expiry_date = inputs.filter('[name="arr_expiry_date[]"]').val() || '-';
            var qty = inputs.filter('[name="arr_qty[]"]').val() || '0';

            // Parse quantity for totals
            var qtyValue = parseFloat(qty.replace(/,/g, '')) || 0;
            modalTotalWeight += qtyValue;
            modalTotalCount++;

            // create a new table row and populate the cells with data-index for deletion
            var newRow = $('<tr data-index="' + index + '" style="cursor: pointer;">');
            newRow.append($('<td>').text(lot_num));
            newRow.append($('<td>').text(expiry_date));
            newRow.append($('<td>').text(modalTotalCount)); // box number is sequential
            newRow.append($('<td>').text(qty));
            $('#detailtable tbody').append(newRow);
        });

        // Update modal totals if the function exists
        if (typeof updateModalTotals === 'function') {
            updateModalTotals(modalTotalWeight, modalTotalCount);
        }
    }

    function deleteRow() {
        // get the selected row and remove it from the table
        var selectedRows = $('#detailtable tbody tr.active');

        if (selectedRows.length == 0) {
            Swal.fire({
                title: 'No Selection',
                text: 'Please select at least one row to delete.',
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            return false;
        }

        // show confirmation dialog
        Swal.fire({
            title: 'Confirm Deletion',
            text: 'delete the selected lot(s)?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                var deletedLots = [];
                var indicesToRemove = [];

                selectedRows.each(function() {
                    var rowIndex = $(this).data('index');
                    indicesToRemove.push(rowIndex);
                });

                // Sort indices in descending order to remove from end to beginning
                indicesToRemove.sort(function(a, b) { return b - a; });

                indicesToRemove.forEach(function(rowIndex) {
                    var addedLotRow = $('#addedLot tbody tr').eq(rowIndex);

                    // Get lot number for tracking
                    var lot_num = addedLotRow.find('input[name="arr_lot_num[]"]').val();
                    var qty = addedLotRow.find('input[name="arr_qty[]"]').val();

                    if (lot_num) {
                        deletedLots.push(lot_num);

                        // Remove from tracking array (if it exists in the parent window)
                        if (typeof addedLots !== 'undefined') {
                            var index = addedLots.indexOf(lot_num);
                            if (index > -1) {
                                addedLots.splice(index, 1);
                            }
                        }

                        // Update UI counts (if function exists in parent window)
                        if (typeof updateUICount === 'function' && qty) {
                            updateUICount(-parseFloat(qty.replace(/,/g, '')), -1);
                        }
                    }

                    // Remove the row from addedLot table
                    addedLotRow.remove();
                });

                // Show success message (if function exists in parent window)
                if (typeof showSuccessMessage === 'function' && deletedLots.length > 0) {
                    showSuccessMessage('Deleted lots: ' + deletedLots.join(', '));
                }

                // Refresh the modal content or close if no lots remain
                if ($('#addedLot tbody tr').length == 0) {
                    $('#CW_detail').modal('hide');
                } else {
                    // Refresh the detail table by calling openDetails again
                    if (typeof openDetails === 'function') {
                        openDetails();
                    }
                }
            }
        });
    }

    // add class active to the selected row - using event delegation
    $(document).on('click', '#detailtable tbody tr', function (e) {
        e.preventDefault();
        e.stopPropagation();

        console.log('Row clicked:', $(this).data('index')); // Debug log
        $(this).toggleClass('active');

        // Visual feedback
        if ($(this).hasClass('active')) {
            console.log('Row selected');
        } else {
            console.log('Row deselected');
        }
    });

    // Alternative click handler for troubleshooting
    $(document).ready(function() {
        // Ensure the click handler is properly attached when modal is shown
        $('#CW_detail').on('shown.bs.modal', function () {
            console.log('Modal shown, attaching click handlers');

            // Remove any existing handlers to prevent duplicates
            $('#detailtable tbody tr').off('click.rowselect');

            // Attach click handler with namespace
            $('#detailtable tbody tr').on('click.rowselect', function(e) {
                e.preventDefault();
                e.stopPropagation();

                console.log('Direct click handler - Row clicked:', $(this).data('index'));
                $(this).toggleClass('active');
            });
        });
    });

</script>
