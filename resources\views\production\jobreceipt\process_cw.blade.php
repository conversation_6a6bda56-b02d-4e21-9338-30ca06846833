@extends('layout.mobile.app')
@section('content')
@section('title', __('Job Receipt - CW'))
<style>
    .card {
        box-shadow: 0px 0px 0px transparent;
        border: 1px solid transparent;
    }

    div.col-xs-2.col-md-1.col-lg-1,
    div.col-xs-2.col-md-1.col-lg-2 {
        margin-top: 5px;
    }

    form .form-group {
        margin-bottom: 0.2rem;
    }
</style>

<div class="card-body collapse in">
    <div class="card-block-custom">
        <x-catch-weight-form
            :batch-id="$batch_id"
            :whsenum="$job_order->whse_num"
            :itemnum="$ItemList->item_num"
            :itemdesc="$ItemList->item_desc"
            :refnum="$job_order->job_num"
            :refline="$job_order->suffix"
            :tolerance="$job_order->qty_receivable"
            :toleranceuom="$job_order->uom"
            :submiturl="route('JobReceiptCWProcess')"
            :catch-weight-tolerance="$ItemList->catch_weight_tolerance"
            :disable-create-new-item-loc="$disable_create_new_item_location"
            :allow-over="$allow_over_receive"
            :line-uom="$job_order->uom"
            :print-label="$printLabel"
            trans-type="JobReceipt"
            transtype="job">

            <input type="hidden" name="qty_completed" id="qty_completed" value="{{ $job_order->qty_completed }}">
            <input type="hidden" name="suffix" id="suffix" value="{{ $job_order->suffix }}">

            <div class="form-group row pt-1">
                <label class="col-xs-3 col-md-3 col-lg-3 label-control-custom required" for="loc_num">{{ __('mobile.label.loc_num') }}</label>
                <div class="col-xs-8 col-md-8 col-lg-7">
                    <div class="input-group">
                        <input type="text" id="loc_num" class="form-control border-primary" placeholder="{{__('mobile.placeholder.loc_num')}}" name="loc_num" value="{{old('loc_num',$def_loc) }}" maxlength="30">
                        <span id="locnumnotexist"></span>
                        <span id="checkLoc"></span>
                    </div>
                </div>
                <div class="col-xs-1 col-md-1 col-lg-1" style="margin-left:-0.8em; padding:0px;">
                    <button type="button" name="{{__('mobile.list.locations')}}" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal" onclick="selectionNull('/getLocNoPicking','whse_num,loc_num','loc_num','loc_num');modalheader(this.id,this.name)"><i class="icon-search"></i></button>
                </div>
            </div>

            <x-slot name="additionalFields">
                <div class="form-group row">
                    <div class="input-group">
                        <p class="col-xs-12 col-md-12 col-lg-12 label-control-custom required" style="word-break: break-all; word-wrap: break-word">
                            {{ __('mobile.label.job_num') }} : {{ $job_num }} | {{ $suffix }}
                    </div>
                </div>
            </x-slot>
            
        </x-catch-weight-form>
    </div>
</div>

<script>
    function historyBack()
    {
        var url = window.location.href.split('?')[0];

        window.location.href = url;
    }
</script>

@endsection()
