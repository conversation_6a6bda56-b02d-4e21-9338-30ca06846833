<?php

namespace App\Http\Controllers\Incoming;

use App\Services\SapCallService;
use App\SAPb1\SAPClient;
use App\SAPb1\Filters\Equal;
use App\SAPb1\Service;
use App\SAPb1\Config;
use App\Services\CallHttpService;
use Illuminate\Support\Facades\Crypt;
use App\Http\Controllers\BarcodeController;
use App\Services\LotLocationService;
use App\Services\UOMService;
use Illuminate\Http\Request;
use App\PurchaseOrderItem;
use App\Item;
use App\Warehouse;
use App\PurchaseOrder;
use App\Loc;
use App\po_bln;
use App\UomConv;
use App\Services\GeneralService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\POExport;
use App\Services\POService;
use App\View\POItemView;
use App\ItemLoc;
use Alert;
use App\View\TparmView;
use Illuminate\Support\Carbon;
use App\Http\Controllers\Controller;
use App\Services\LotService;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use App\GRN;
use App\GRNItem;
use App\Container;
use App\LicensePlateNumberDefinition;
use App\Services\PalletService;
use App\Services\LPNDefinitionService;
use Camroncade\Timezone\Facades\Timezone;
use App\SiteSetting;
use App\ContainerItem;
use App\Services\SiteConnectionService;
use App\Http\Controllers\ValidationController;
use App\Services\MatltransService;
use App\OrderNumberDefinition;
use App\Services\OrderNumberDefinitionService;
use App\Vendor;
use App\Services\SapApiCallService;
use App\Services\PreassignLotsService;
use App\Services\CatchWeightService;

class POReceiveController extends Controller
{

    use \App\Traits\HasDefaultLoc;
    protected $timezone;

    public function __construct()
    {
        $this->middleware('auth');
        //$this->middleware('can:hasCoReturn');
    }

    /**
     * Show the PO receipt index page.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if (!\Gate::allows('hasPoReceipt')) {
            return view('errors.404')->with('page', 'error');
        }

        $tparm = new TparmView();
        $enable_warehouse = $tparm->getTparmValue('POReceipt', 'enable_warehouse');
        $def_receipt_location = $tparm->getTparmValue('POReceipt', 'def_receipt_location');
        $def_receipt_type = $tparm->getTparmValue('POReceipt', 'def_receipt_type');
        $enable_new_grn_creation  = $tparm->getTparmValue('POReceipt', 'enable_new_grn_creation');
        $def_whse = auth()->user()->getCurrWhse();
        if ($def_receipt_location) {
            $def_receipt_location = json_decode($def_receipt_location);
            // if ($def_receipt_location->whse_num) {
            //     $def_whse = $def_receipt_location->whse_num;
            // }
            foreach ($def_receipt_location as $key) {
                if ($key->whse_num == $def_whse) {
                    $def_whse = $key->whse_num;
                }
            }
        }

        $def_receipt_type = $def_receipt_type ? $def_receipt_type : 'po';

        return view('Receiving.poreceipt.index', compact('enable_warehouse', 'def_whse', 'def_receipt_type', 'enable_new_grn_creation'));
    }

    public function showPoLine(Request $request)
    {
        // dump($request->all());
        // redirect to GRN list
        if (isset($request->receipt_type) && ($request->receipt_type == "GRN" || $request->receipt_type == "NewGRN")) {
            //dd('aaaa');
            return $this->showGrnLine($request);
        }

        $request['po_num'] = $po_num = ($request->po_no == null) ? $request->po_num : $request->po_no;
        $request['po_line'] = $po_line = ($request->po_line == null) ? $request->po_line : $request->po_line;

        $request['whse_num'] = $whse_num = (isset($request->search_by) && $request->search_by == 'itemnvendor') ? $request->whse_no : $request->whse_num;
        $request['item_num'] = $item_num = ($request->item_no == null) ? $request->item_num : $request->item_no;
        $request['vend_num'] = $vend_num = ($request->vend_num1 == null) ? $request->vend_num : $request->vend_num1;

        // if ($request->po_no == null || $request->whse_no == null || $request->item_no == null) {
        //     $po_num = $request->po_num;
        //     $whse_num = $request->whse_num;
        //     $item_num = $request->item_num;
        //     $vend_num = $request->vend_num;
        // } else {
        //     $po_num = $request->po_no;
        //     $whse_num = $request->whse_num;
        //     $item_num = $request->item_no;
        //     $vend_num = $request->vend_num1;
        // }
        // dd($po_num, $whse_num, $item_num, $vend_num);
        $vend_do = $request->vend_do;

        Session::put('request_data_poline', $request->all());

        $request = validateSansentiveValue($request);
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        if ($item_num == "NON-INV") {
            $getCheckNONINV = 1;
        } else {
            $getCheckNONINV = 0;
        }
        if ($item_num != "NON-INV") {
            $request->validate([
                'item_num' => 'nullable|exists:items,item_num,item_status,1,site_id,' . auth()->user()->site_id,
                // 'whse_num' => 'required|exists:warehouses,whse_num,whse_status,1,site_id,' . auth()->user()->site_id,
            ], [
                'item_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.items')]),
                'whse_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.warehouses')]),
            ]);
        }
        $url = '/home/<USER>/po-receipt';
        // if not filtered by po
        if ($po_num == null) {
            $itemList = PurchaseOrder::where('po_status', "O");
            $po_item = PurchaseOrderItem::where('whse_num', $whse_num)->where('rel_status', "O");

            if ($item_num != null) {
                $po_item = $po_item->where('item_num', $item_num);
            }
            // if ($po_line != null) {
            //     $po_item = $po_item->where('po_line', $po_line);
            // }


            $po_item = $po_item->distinct()->get()->pluck('po_num')->toArray();
            $itemList = $itemList->whereIn('po_num', $po_item);

            if ($vend_num != null) {
                $itemList = $itemList->where('vend_num', $vend_num);
            }

            if ($request->vend_do != null) {
                $itemList = $itemList->where('vend_do', $request->vend_do);
            }

            $itemList = $itemList->orderBy('po_num')->get();
            //dd('ssss');
            $vend_name = "";

            return view('Receiving.poreceipt.polist')
                ->with('itemList', $itemList)
                ->with('po_num', $po_num)
                ->with('grn_num', $request->grn_num)
                ->with('item_num', $item_num)
                ->with('po_line', $po_line)
                ->with('vend_num', $vend_num)
                ->with('vend_name', $vend_name)
                ->with('vend_do', $request->vend_do)
                ->with('whse', $whse_num)
                ->with('url', $url)
                ->with('receipt_type', 'PO');
        }

        // Send error if po_num is not within the whse
        $checkPoNum = PurchaseOrderItem::where('po_num', $po_num)->where('whse_num', $whse_num)->exists();
        $checkWhse = PurchaseOrderItem::where('po_num', $po_num)->first();
        if (!$checkPoNum) {
            if ($checkWhse->whse_num != $whse_num) {
                //throw ValidationException::withMessages(['co_num' => 'CO-' . $request->co_num. ' not matching with ' . $request->whse_num]);
                throw ValidationException::withMessages(['po_num' => 'PO [' . $po_num . '] does not match Whse [' . $whse_num . ']']);
            } else {
                throw ValidationException::withMessages(['po_num' => 'PO-' . $po_num . ' does not exist in ' . $whse_num . ' warehouse']);
            }
        }

        // if SAP ON need to check sap_po_batch_sync table

        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

        if ($sap_trans_order_integration == 1) {
            // Query Data from sap_po_batch_sync based on : Site_id , Po_type = 1 , Po_num , Po_line
            $site_id = auth()->user()->site_id;
            $getPOBatchDetails =  $po_details = DB::table('sap_po_batch_sync')->where('PO_type', 1)->where('po_num', $po_num)->where('site_id', $site_id)->whereIn('sync_status', [1])->orderBy('po_line', 'ASC')->get()->toArray();

            //dd($getPOBatchDetails);
            $arrDataSAPBatchData = array();
            $count = 0;
            foreach ($getPOBatchDetails as $key) {
                $arrDataSAPBatchData[$key->po_line] = $count + $key->qty_received;
            }
        }
        // $ItemList->qty_ordered - $ItemList->qty_received + $ItemList->qty_returned
        // dd($arrDataSAPBatchData,$getPOBatchDetails);

        // Send error if po_num's status is not openwhse_num
        $checkPoNum = PurchaseOrderItem::where('po_num', $po_num)->where('whse_num', $whse_num)->where('rel_status', '!=', "C")->exists();
        if (!$checkPoNum) {
            throw ValidationException::withMessages(['po_num' => 'PO-' . $po_num . ' cannot be proceed due to status is completed/closed']);
        }

        Session::put('vend_do', $request->vend_do);

        $po_list = PurchaseOrderItem::where('po_num', $po_num)->where('whse_num', $whse_num);
        // $po_item = PurchaseOrderItem::where('item_num', $item_num)->where('whse_num', $whse_num)->first();

        if ($item_num != "" || $item_num != null) {
            $po_list = $po_list->where('item_num', $item_num);
        }

        if ($vend_num != "" || $vend_num != null) {
            $po_list = $po_list->where('vend_num', $vend_num);
        }

        $po_list = $po_list->get();

        // no record found
        if ($po_list->count() == 0) {
            Alert::error('Error', __('error.mobile.norecord'));
            throw ValidationException::withMessages([]);
        }

        // Redirect to the Process screen.
        if ($po_list->count() == 1) {
            foreach ($po_list as $po_list) {
                $request['po_rel'] = $po_list->po_rel;
                $request['po_line'] = $po_list->po_line;
                $request['item_num'] = $po_list->item_num;
            }

            $request['indicate_single'] = 0;
            return redirect()->route('showPoProcess', $request->all(), 303);
        }


        return view('Receiving.poreceipt.ItemList')
            ->with('po_num', $po_num)
            ->with('grn_num', $request->grn_num)
            ->with('whse', $whse_num)
            ->with('vend_num', $vend_num)
            ->with('po_line', $po_line)
            ->with('vend_do', $vend_do)
            ->with('item_num', $item_num)
            ->with('url', $url)
            ->with('unit_quantity_format', $unit_quantity_format);
    }


    public function backshowGRnLine(Request $request)
    {
        //$request->receipt_type == "GRN";

        return $this->showGrnLine($request);
    }



    public function backshowPoReceiptLine(Request $request)
    {
        if ($request->po_no == null || $request->whse_no == null || $request->item_no == null) {

            $po_num = $request->po_num;
            $whse_num = $request->whse_num;
            $item_num = $request->item_num;
        } else {

            $po_num = $request->po_num;
            $whse_num = $request->whse_num;
            $item_num = $request->item_num;
        }


        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        if ($item_num == "NON-INV") {
            $getCheckNONINV = 1;
        } else {
            $getCheckNONINV = 0;
        }
        if ($request->item_num != "NON-INV") {
            $request->validate([
                'item_num' => 'nullable|exists:items,item_num,item_status,1,site_id,' . auth()->user()->site_id,
                // 'whse_num' => 'required|exists:warehouses,whse_num,whse_status,1,site_id,' . auth()->user()->site_id,
            ], [
                'item_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.items')]),
                'whse_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.warehouses')]),
            ]);
        }

        // Send error if po_num is not within the whse
        $checkPoNum = PurchaseOrderItem::where('po_num', $po_num)->where('whse_num', $whse_num)->exists();
        $checkWhse = PurchaseOrderItem::where('po_num', $po_num)->first();
        if (!$checkPoNum) {
            if ($checkWhse->whse_num != $request->whse_num) {
                //throw ValidationException::withMessages(['co_num' => 'CO-' . $request->co_num. ' not matching with ' . $request->whse_num]);
                throw ValidationException::withMessages(['po_num' => 'PO [' . $po_num . '] does not match Whse [' . $whse_num . ']']);
            } else {
                throw ValidationException::withMessages(['po_num' => 'PO-' . $po_num . ' does not exist in ' . $whse_num . ' warehouse']);
            }
        }

        // if SAP ON need to check sap_po_batch_sync table

        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

        // if ($sap_trans_order_integration == 1) {
        //     // Query Data from sap_po_batch_sync based on : Site_id , Po_type = 1 , Po_num , Po_line
        //     $site_id = auth()->user()->site_id;
        //     $getPOBatchDetails =  $po_details = DB::table('sap_po_batch_sync')->where('PO_type', 1)->where('po_num', $po_num)->where('site_id', $site_id)->whereIn('sync_status', [1])->orderBy('po_line', 'ASC')->get()->toArray();

        //     //dd($getPOBatchDetails);
        //     $arrDataSAPBatchData = array();
        //     $count = 0;
        //     foreach ($getPOBatchDetails as $key) {
        //         $arrDataSAPBatchData[$key->po_line] = $count + $key->qty_received;
        //     }
        // }
        // $ItemList->qty_ordered - $ItemList->qty_received + $ItemList->qty_returned
        // dd($arrDataSAPBatchData,$getPOBatchDetails);

        // Send error if po_num's status is not openwhse_num
        $checkPoNum = PurchaseOrderItem::where('po_num', $po_num)->where('whse_num', $whse_num)->where('rel_status', '!=', "C")->exists();
        if (!$checkPoNum) {
            throw ValidationException::withMessages(['po_num' => 'PO-' . $po_num . ' cannot be proceed due to status is completed/closed']);
        }

        Session::put('vend_do', $request->vend_do);

        $po_rel = PurchaseOrderItem::where('po_num', $po_num)->where('whse_num', $whse_num)->get();
        $po_item = PurchaseOrderItem::where('item_num', $item_num)->where('whse_num', $whse_num)->first();

        // Redirect to the Process screen.
        // if ($item_num != null && ($po_item)) {
        //     $po_list = PurchaseOrderItem::where('po_num', $po_num)->where('whse_num', $whse_num)->where('item_num', $item_num)->get();

        //     if ($po_list->count() == 1) {
        //         foreach ($po_list as $po_list) {
        //             $request['po_rel'] = $po_list->po_rel;
        //             $request['po_line'] = $po_list->po_line;
        //         }
        //         return redirect()->route('showPoProcess', $request->all(), 303);
        //     }

        // }

        $po_rel = $po_rel->first();
        $url = '/home/<USER>/po-receipt';

        return view('Receiving.poreceipt.ItemList')
            ->with('po_num', $po_num)
            ->with('po_line', "") // Put empty line to show all PO Line item
            ->with('whse', $po_rel->whse_num)
            ->with('vend_num', $po_rel->vend_num)
            ->with('item_num', $item_num)
            ->with('url', $url)
            ->with('unit_quantity_format', $unit_quantity_format);
    }



    public function showGrnLine(Request $request)
    {



        $grn_num = $request->grn_num;
        $whse_num = $request->whse_num;
        $item_num = $request->item_num;
        $vend_num = $request->vend_num;
        $vend_do = $request->vend_do;
        Session::put('receipt_type', $request->receipt_type);

        $request = validateSansentiveValue($request);
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        if ($item_num == "NON-INV") {
            $getCheckNONINV = 1;
        } else {
            $getCheckNONINV = 0;
        }
        if ($request->item_num != "NON-INV") {
            $request->validate([
                'item_num' => 'nullable|exists:items,item_num,item_status,1,site_id,' . auth()->user()->site_id,
                // 'whse_num' => 'required|exists:warehouses,whse_num,whse_status,1,site_id,' . auth()->user()->site_id,
            ], [
                'item_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.items')]),
                'whse_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.warehouses')]),
            ]);
        }

        if ($grn_num)
            $checkGrnNum = GRN::where('grn_num', $grn_num)->where('whse_num', $whse_num)->first();
        else
            $checkGrnNum = true; // GRN::where('whse_num', $whse_num)->where('grn_status', '!=', "C")->exists();

        // Send error if grn_num's status is not open
        if ($checkGrnNum && @$checkGrnNum->grn_status == 'C') {
            // throw ValidationException::withMessages(['grn_num' => 'GRN-' . $grn_num . ' cannot be proceed due to status is completed/closed']);
            throw ValidationException::withMessages(['grn_num' => __('error.mobile.resource_completed', ['resource' => __('mobile.label.grn')])]);
        }

        Session::put('vend_do', $request->vend_do);

        if ($request->receipt_type == "NewGRN") {
            $grn_number_readonly = $tparm->getTparmValue('POReceipt', 'use_grn_auto_number') == 1 ? 'readonly' : '';
            $grn_line = 1;

            if ($grn_num == "" || $grn_number_readonly == 'readonly') {
                // $site_id = auth()->user()->site_id;
                // $order_type = 'GRN';
                // $grn_num = OrderNumberDefinitionService::getGenerateCode($order_type,$site_id);
            } else {
                $grn = GRN::where('grn_num', $grn_num)->where('site_id', auth()->user()->site_id)->first();

                if ($grn) {
                    $grn_line = GRNItem::select('grn_line')->where('grn_num', $grn_num)->where('site_id', auth()->user()->site_id)->first();
                    $grn_line  = $grn_line + 1;
                }
            }

            //     $date = Carbon::now()->toDateTimeString();
            //    //dd($date);
            //     GRN::create([
            //         "grn_num" => $grn_num,
            //         "grn_status" => "O",
            //         "grn_date" => $date,
            //         "vend_num" => $request->vend_num,
            //         "vend_name" => $request->vend_name,
            //         "vend_do" => $request->vend_do,
            //         "whse_num" => $request->whse_num,
            //         // "ship_via" => null,
            //         // "shipped_date" => null,
            //         // "container_num" => null,
            //         // "tracking_num" => null,
            //         // "notes" => null,
            //         // "site_id" => auth()->user()->site_id,
            //         // 'created_by' => auth()->user()->name,
            //         // 'modified_by' => auth()->user()->name,
            //         // 'created_date' => now(),
            //         // 'modified_date' => now(),
            //     ]);

            $request['grn_num'] = $grn_num;

            // $order_type = 'GRN';
            // $prefix = OrderNumberDefinition::select('prefix_define')->where('status','Y')->where('order_type', $order_type)->first();

            // // Fix prefix , GEN Model need to check

            // if($prefix==NULL) {
            //     $prefix['prefix_define']="";
            // } else {
            //     @$prefix = $prefix->toArray();
            // }
            // // dd($order_type,auth()->user()->site_id,$prefix,$grn_num);
            // $prefix = $prefix['prefix_define'];
            // // increase the order number
            // $grn_num_generate = OrderNumberDefinitionService::getGenerateCodeSucess($order_type,auth()->user()->site_id,$prefix,$grn_num);
            // dd($grn_num_generate);
            if ($request->po_num != null) {
                if ($request->po_line) {
                    $request->merge([
                        // 'grn_rel' => $grn_list->grn_rel,
                        'grn_line' => $grn_line,
                        'po_num' => $request->po_num,
                        'po_line' => $request->po_line
                    ]);

                    $po = DB::query()->from('po_items')
                        ->select(
                            // 'id',
                            // DB::raw('"'.$request->grn_num.'"' as status'),
                            'po_num',
                            'po_line',
                            'item_num',
                            'item_desc',
                            // DB::raw('IFNULL(qty_ordered,0) AS qty_ordered'),
                            DB::raw('IFNULL(qty_received,0) AS qty_received'),
                            DB::raw('IFNULL(qty_returned,0) AS qty_returned'),
                            DB::raw('IFNULL(qty_balance,0) AS qty_shipped'),
                            'uom',
                            // DB::raw('"O" as status')
                            // DB::raw('null as received_date')
                        )
                        // ->where('rel_status','=','O')
                        // ->where('qty_balance','>','0')
                        ->where('po_num', $request->po_num)
                        ->where('po_line', $request->po_line)
                        // ->where('vend_num','=', $request->vend_num)
                        // ->where('whse_num','=', $request->whse_num)
                        ->where('site_id', auth()->user()->site_id)
                        ->first();

                    // if non found
                    if ($po == null) {
                        Alert::error('Error', __('error.mobile.norecord'));
                        throw ValidationException::withMessages([]);
                    }

                    $request['item_num'] = $po->item_num;


                    // GRNItem::create([
                    //     'grn_num' => $grn_num,
                    //     'grn_line' => $grn_line,
                    //     'po_num' => $request->po_num,
                    //     'po_line' => $request->po_line,
                    //     'item_num' => $request->item_num,
                    //     'item_desc' => $po->item_desc,
                    //     'qty_shipped' => $po->qty_shipped,
                    //     'qty_received' => $po->qty_received,
                    //     'qty_returned' => $po->qty_returned,
                    //     'uom' => $po->uom,
                    //     "status" => 'O',
                    //     "site_id" => auth()->user()->site_id,
                    //     // 'created_by' => auth()->user()->name,
                    //     // 'modified_by' => auth()->user()->name,
                    //     // 'created_date' => now(),
                    //     // 'modified_date' => now(),
                    // ]);

                    return redirect()->route('showGrnProcess', $request->all(), 303);
                } else {
                    // dd($request->all());

                    Session::put('grn_num', $grn_num);
                    // dd('1');


                    $input['po_num'] = $request->po_numm ?? null;
                    $input['whse_num'] = $request->whse_num;
                    $input['po_line'] = $request->po_line ?? null;
                    $input['po_rel'] = $request->ref_release ?? null;
                    $input['item_num'] = $request->item_num ?? null;
                    $input['uom'] = $request->uom ?? null;
                    $input['qty_required'] = $request->qty_required ?? null;
                    $input['item_desc'] = $request->item_desc ?? null;
                    $input['receipt_type'] = $request->receipt_type ?? null;
                    $input['grn_num'] = $grn_num;
                    $input['vend_num'] = $request->vend_num;
                    $input['vend_name'] = $request->vend_name;
                    $input['vend_do'] = $request->vend_do;
                    $input['indicate'] = 2;
                    //$input['count'] = $grn_list->count();
                    //dd('sssxxxs11',$request,$input,$request->receipt_type);
                    $url = generateRedirectUrl('GRNReceiptAdd', $input);

                    return view('Receiving.poreceipt.ItemList')
                        ->with('receipt_type', $request->receipt_type)
                        ->with('grn_num', $grn_num)
                        ->with('po_num', $request->po_num)
                        ->with('po_line', $request->po_line)
                        ->with('whse', $whse_num)
                        ->with('url', $url->getTargetUrl())
                        ->with('vend_num', $vend_num)
                        ->with('vend_do', $request->filtered_vend_do)
                        ->with('item_num', $item_num)
                        ->with('unit_quantity_format', $unit_quantity_format);
                }
            } else {
                $itemList = PurchaseOrder::where('po_status', 'O');
                $po_item = PurchaseOrderItem::where('whse_num', $whse_num)->where('rel_status', "O");

                if ($item_num != null) {
                    $po_item = $po_item->where('item_num', $item_num);
                }

                if ($request->po_line != null) {
                    $po_item = $po_item->where('po_line', $po_line);
                }

                $po_item = $po_item->distinct()->get()->pluck('po_num')->toArray();

                $itemList = $itemList->whereIn('po_num', $po_item);

                if ($vend_num != null) {
                    $itemList = $itemList->where('vend_num', $vend_num);
                }

                $itemList = $itemList->orderBy('po_num')->get();

                // back to grn-add
                $input['po_num'] = $request->po_numm ?? null;
                $input['whse_num'] = $request->whse_num;
                $input['po_line'] = $request->po_line ?? null;
                $input['po_rel'] = $request->ref_release ?? null;
                $input['item_num'] = $request->item_num ?? null;
                $input['uom'] = $request->uom ?? null;
                $input['qty_required'] = $request->qty_required ?? null;
                $input['item_desc'] = $request->item_desc ?? null;
                $input['receipt_type'] = $request->receipt_type ?? null;
                $input['grn_num'] = $request->grn_num ?? null;
                $input['vend_num'] = $request->vend_num ?? null;
                $input['vend_name'] = $request->vend_name ?? null;
                $input['vend_do'] = $request->vend_do ?? null;
                $input['indicate'] = 1;
                // dd($request,$input);
                $url = generateRedirectUrl('GRNReceiptAdd', $input);

                return view('Receiving.poreceipt.polist')
                    ->with('itemList', $itemList)
                    ->with('po_num', '')
                    ->with('grn_num', $grn_num)
                    ->with('item_num', $item_num)
                    ->with('vend_num', $vend_num)
                    ->with('vend_do', $request->vend_do)
                    ->with('whse', $whse_num)
                    ->with('url', $url->getTargetUrl())
                    ->with('indicate', 1)
                    ->with('vend_name', $request->vend_name)
                    ->with('receipt_type', 'NewGRN');
            }
        } else {
            if ($grn_num != null) {
                $getGrn = GRN::where('grn_num', $grn_num)->where('whse_num', $whse_num)->where('grn_status', "O");

                // filter by vend number
                if ($vend_num != null) {
                    $getGrn = $getGrn->where('vend_num', $vend_num);
                }
                // filter by vend do
                if ($vend_do != null) {
                    $getGrn = $getGrn->where('vend_do', $vend_do);
                }

                // get data
                $getGrn = $getGrn->get();

                // if non found
                if ($getGrn->count() == 0) {
                    Alert::error('Error', __('error.mobile.norecord'));
                    throw ValidationException::withMessages([]);
                }

                // get the grn num
                $arrGrnNum = $getGrn->pluck('grn_num')->toArray();

                // get the grn item
                $grn_list = GRNItem::where('grn_num', $grn_num)->where('status', "O")->whereIn('grn_num', $arrGrnNum);

                // filter by item number
                if ($item_num != null) {
                    $grn_list = $grn_list->where('item_num', $item_num);
                }

                // get data
                $grn_list = $grn_list->get();

                // if non found
                if ($grn_list->count() == 0) {
                    Alert::error('Error', __('error.mobile.norecord'));
                    return $this->index();
                    throw ValidationException::withMessages([]);
                }

                // if found only 1 data, redirect to the Process form.
                if ($grn_list->count() == 1) {
                    foreach ($grn_list as $grn_list) {
                        $request->merge([
                            'grn_num' => $grn_list->grn_num,
                            'grn_line' => $grn_list->grn_line,
                            'po_num' => $grn_list->po_num,
                            'po_line' => $grn_list->po_line,
                            'item_num' => $grn_list->item_num,
                            'indicator' => 2,
                            'frm_listing' => 'GRN'
                        ]);
                    }
                    //dd($request);
                    return redirect()->route('showGrnProcess', $request->all(), 303);
                }

                // Cancel Button

                if (@$request->indicator_listing == null) {
                    // Specify GRN
                    $indicator = 2;
                } else {
                    // Listing All
                    $indicator = 1;
                }

                $input['po_num'] = $request->po_numm ?? null;
                $input['whse_num'] = $request->whse_num;
                $input['po_line'] = $request->po_line ?? null;
                $input['po_rel'] = $request->ref_release ?? null;
                $input['item_num'] = $request->item_num ?? null;
                $input['uom'] = $request->uom ?? null;
                $input['qty_required'] = $request->qty_required ?? null;
                $input['item_desc'] = $request->item_desc ?? null;
                $input['receipt_type'] = $request->receipt_type ?? null;
                $input['grn_num'] = $grn_num;
                $input['vend_num'] = $request->vend_num;
                $input['vend_do'] = $request->vend_do;
                $input['indicate'] = $indicator;
                $input['count'] = $grn_list->count();
                // dd('lalala');
                $url = generateRedirectUrl('GRNReceipt', $input);

                return view('Receiving.poreceipt.grnItemList')
                    ->with('grn_list', $grn_list)
                    ->with('grn_num', $grn_num)
                    ->with('whse', $whse_num)
                    ->with('indicator', $indicator)
                    ->with('url', $url->getTargetUrl())
                    ->with('vend_num', $vend_num)
                    ->with('unit_quantity_format', $unit_quantity_format);
            } else { // Route to GRN List

                $grn_list = GRN::where('whse_num', $whse_num)->where('grn_status', '!=', "C");
                $grn_item = GRNItem::where('status', "O");

                // filter by item number
                if ($item_num != null) {
                    $grn_item = $grn_item->where('item_num', $item_num);
                }

                // get grn number
                $grn_item = $grn_item->distinct()->get()->pluck('grn_num')->toArray();

                // filter grn with line
                $grn_list = $grn_list->whereIn('grn_num', $grn_item);

                // filter by vend number
                if ($vend_num != null) {
                    $grn_list = $grn_list->where('vend_num', $vend_num);
                }
                // filter by vend do
                if ($vend_do != null) {
                    $grn_list = $grn_list->where('vend_do', $vend_do);
                }

                // get data
                $grn_list = $grn_list->orderBy('grn_num')->get();

                return view('Receiving.poreceipt.grnList')
                    ->with('grn_list', $grn_list)
                    ->with('item_num', $item_num)
                    ->with('whse', $whse_num);
            }
        }
    }

    public function addGrn()
    {
        $tparm = new TparmView();
        $enable_warehouse = $tparm->getTparmValue('POReceipt', 'enable_warehouse');
        $def_receipt_location = $tparm->getTparmValue('POReceipt', 'def_receipt_location');
        $def_receipt_type = $tparm->getTparmValue('POReceipt', 'def_receipt_type');
        $grn_number_readonly = ($tparm->getTparmValue('POReceipt', 'use_grn_auto_number') == 1) ? 'readonly' : '';

        $site_id = auth()->user()->site_id;
        $order_type = 'GRN';

        if ($tparm->getTparmValue('POReceipt', 'use_grn_auto_number') == 1) {
            $grn_num_generate = OrderNumberDefinitionService::getGenerateCode($order_type, $site_id);
        } else {
            $grn_num_generate = "";
        }

        $def_whse = auth()->user()->getCurrWhse();
        if ($def_receipt_location) {
            $def_receipt_location = json_decode($def_receipt_location);
            // if ($def_receipt_location->whse_num) {
            //     $def_whse = $def_receipt_location->whse_num;
            // }
            foreach ($def_receipt_location as $key) {
                if ($key->whse_num == $def_whse) {
                    $def_whse = $key->whse_num;
                }
            }
        }

        return view('Receiving.poreceipt.grnAdd', compact('enable_warehouse', 'def_whse', 'def_receipt_type', 'grn_number_readonly', 'grn_num_generate'));
    }

    public function showPoProcess(Request $request)
    {

        if (!\Gate::allows('hasPoReceipt')) {
            return view('errors.404')->with('page', 'error');;
        }
        // if($request->po_no || $request->po_num){
        if ($request->po_no == null || $request->whse_no == null || $request->item_no == null) {

            $po_num = $request->po_num;
            $whse_num = $request->whse_num;
            $item_num = $request->item_num;
        } else {
            $po_num = $request->po_num;
            $whse_num = $request->whse_num;
            $item_num = $request->item_num;
        }

        $last_doc_num = DB::table('sap_po_batch_sync')
            ->where('po_num', $po_num)
            ->where('sync_status', 1)
            ->where('PO_type', 1)
            ->orderBy('id', 'desc')
            ->where('site_id', auth()->user()->site_id)
            ->pluck('doc_num')
            ->first();

        $tparm = new TparmView();
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $allow_over_receive = $tparm->getTparmValue('POReceipt', 'allow_over_receive');
        $disable_create_new_item_location = $tparm->getTparmValue('POReceipt', 'disable_create_new_item_location');
        $printLabel = $tparm->getTparmValue('POReceipt', 'print_label');
        $batch_id = generateBatchId("POReceipt");

        $item = new Item();
        $po_rel = new PurchaseOrderItem();
        $ItemList = $po_rel->where('po_num', $po_num)->where('po_line', $request->po_line);

        if ($request->po_rel) {
            $ItemList->where('po_rel', $request->po_rel)->first();
        }

        $vendor_do = PurchaseOrder::select('vend_do')
            ->where('po_num', $po_num)
            ->where('vend_do', '!=', null)
            ->where('site_id', auth()->user()->site_id)
            ->value('vend_do');

        $ItemList = $ItemList->first();

        $ItemList['qty_balance'] = $ItemList->qty_ordered - $ItemList->qty_received + $ItemList->qty_returned; // $request->qty_required;


        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

        if ($sap_trans_order_integration == 1) {
            // Query Data from sap_po_batch_sync based on : Site_id , Po_type = 1 , Po_num , Po_line
            $site_id = auth()->user()->site_id;
            $getPOBatchDetails =  $po_details = DB::table('sap_po_batch_sync')->where('po_line', $request->po_line)->where('PO_type', 1)->where('po_num', $po_num)->where('site_id', $site_id)->whereIn('sync_status', [1])->orderBy('po_line', 'ASC')->get()->toArray();

            //dd($getPOBatchDetails);
            $arrDataSAPBatchData = array();
            $count = 0;
            foreach ($getPOBatchDetails as $key) {
                $ItemList['qty_balance'] = $ItemList['qty_balance'] - $key->qty_received;
            }
        }



        $ItemList['item_desc'] = $ItemList->item_desc;  // $request->item_desc;
        $item = $item->select('lot_tracked', 'catch_weight')->where('item_num', $ItemList->item_num)->first();

        $ItemList['lot_tracked'] = $item->lot_tracked ?? 0;

        // Set to 1 if item not exists due to it is non-inventory
        $non_inv = $item ? 0 : 1;

        $defaults = $this->getLocByRankReceipt($ItemList->whse_num, $ItemList->item_num);

        $def_receipt_location = $tparm->getTparmValue('POReceipt', 'def_receipt_location');
        $def_loc = "";
        if ($def_receipt_location) {
            $def_receipt_location = $tparm->getTparmValueDefLocation('POReceipt', $whse_num);

            if ($def_receipt_location != null) {
                $defaults['loc_num'] = $def_receipt_location->loc_num;
                $def_loc = $def_receipt_location->loc_num;
            } else {
                $defaults['loc_num'];
                $def_loc = "";
            }
        }
        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

        $lpnDef = PalletService::getDefaultLpnTransaction('PO Receipt');

        // check if lpn_num_definition exist
        $lpnDefNum = 1; // 1 = exist
        $checkRunningNumber = LicensePlateNumberDefinition::where('status', 'Y')->where('site_id', auth()->user()->site_id)->first();
        if (!empty($checkRunningNumber)) {
            $maxRunningNum = str_repeat(9, $checkRunningNumber->running_number_digits); // Add 9 to get the max value of set running number. ex: running number = 2, max value will be 99.
            if ($checkRunningNumber->last_running_number_display == $maxRunningNum) {
                $lpnDefNum = 0; // 0 = not exist
            }
        } else {
            $lpnDefNum = 0; // 0 = not exist
        }


        $input['po_num'] = $po_num;
        $input['whse_num'] = $whse_num;
        $input['po_line'] = $request->po_line;
        $input['po_rel'] = $request->ref_release;
        $input['item_num'] = $item_num;
        $input['uom'] = $request->uom;
        $input['qty_required'] = $request->qty_required;
        $input['item_desc'] = $request->item_desc;
        // $input['vend_do'] = $request->vend_do;
        $input['indicate'] = 1;


        // return redirect()->route('showPoLineReturn', ['whse_num' => $request->whse_num, 'po_num' => $request->ref_num, 'item_num' => $request->item_num, 'vend_do' => $request->vend_do]);
        $url = generateRedirectUrl('PoReceipt', $input);

        $view =  $item->catch_weight ? 'Receiving.poreceipt.process_cw' :'Receiving.poreceipt.process';
        // dd($printer_options);

        return view($view)->with('ItemList', $ItemList)
            ->with('vend_do', session('vend_do'))
            ->with('defaults', $defaults)
            ->with('allow_over_receive', $allow_over_receive)
            ->with('disable_create_new_item_location', $disable_create_new_item_location)
            ->with('sap_trans_order_integration', $sap_trans_order_integration)
            ->with('unit_quantity_format', $unit_quantity_format)
            ->with('non_inv', $non_inv)
            ->with('def_lpn', $lpnDef)
            ->with('vendor_do', $vendor_do)
            ->with('last_doc_num', $last_doc_num)
            ->with('lpnDefNum', $lpnDefNum)
            ->with('url', $url->getTargetUrl())
            ->with('def_loc', $def_loc)
            ->with('printLabel', $printLabel)
            ->with('batch_id', $batch_id);

        // }else if($request->grn_num){
        //     // dd($request);
        //     $grn_num = $request->grn_num;
        //     $whse_num = $request->whse_num;
        //     $item_num = $request->item_num;

        //     $last_doc_num = DB::table('sap_po_batch_sync')
        //             ->where('po_num', $po_num)
        //             ->where('sync_status', 1)
        //             ->where('PO_type', 1)
        //             ->orderBy('id', 'desc')
        //             ->where('site_id', auth()->user()->site_id)
        //             ->pluck('doc_num')
        //             ->first();

        //     $tparm = new TparmView();
        //     $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        //     $allow_over_receive = $tparm->getTparmValue('POReceipt', 'allow_over_receive');
        //     $disable_create_new_item_location = $tparm->getTparmValue('POReceipt', 'disable_create_new_item_location');

        //     $item = new Item();
        //     $po_rel = new po_item();
        //     $ItemList = $po_rel->where('po_num', $po_num)->where('po_line', $request->po_line);

        //     if ($request->po_rel) {
        //         $ItemList->where('po_rel', $request->po_rel)->first();
        //     }

        //     $vendor_do = PurchaseOrder::select('vend_do')
        //             ->where('po_num', $po_num)
        //             ->where('vend_do', '!=', null)
        //             ->where('site_id', auth()->user()->site_id)
        //             ->value('vend_do');

        //     $ItemList = $ItemList->first();

        //     $ItemList['qty_balance'] = $ItemList->qty_ordered - $ItemList->qty_received + $ItemList->qty_returned; // $request->qty_required;
        //     $ItemList['item_desc'] = $ItemList->item_desc;  // $request->item_desc;
        //     $item = $item->select('lot_tracked')->where('item_num', $ItemList->item_num)->first();

        //     $ItemList['lot_tracked'] = $item->lot_tracked ?? 0;

        //     // Set to 1 if item not exists due to it is non-inventory
        //     $non_inv = $item ? 0 : 1;

        //     $defaults = $this->getLocByRankReceipt($ItemList->whse_num, $ItemList->item_num);

        //     $def_receipt_location = $tparm->getTparmValue('POReceipt', 'def_receipt_location');

        //     if ($def_receipt_location) {
        //         $def_receipt_location = $tparm->getTparmValueDefLocation('POReceipt', $whse_num);

        //         if ($def_receipt_location != null) {
        //             $defaults['loc_num'] = $def_receipt_location->loc_num;
        //         } else {
        //             $defaults['loc_num'];
        //         }
        //     }
        //     $tparm = new TparmView;
        //     $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

        //     return view('Receiving.poreceipt.process')->with('ItemList', $ItemList)
        //                     ->with('vend_do', session('vend_do'))
        //                     ->with('defaults', $defaults)
        //                     ->with('allow_over_receive', $allow_over_receive)
        //                     ->with('disable_create_new_item_location', $disable_create_new_item_location)
        //                     ->with('sap_trans_order_integration', $sap_trans_order_integration)
        //                     ->with('unit_quantity_format', $unit_quantity_format)
        //                     ->with('non_inv', $non_inv)
        //                     ->with('vendor_do', $vendor_do)
        //                     ->with('last_doc_num', $last_doc_num)
        //     ;
        // }
    }

    public function showGrnProcess(Request $request)
    {

        // dd($request);
        // Indicator = 2 GRN not add new
        if ($request->indicator == 2 || $request->indicator == 1) {
            $grn_list = GRNItem::where('grn_num', $request->grn_num)->where('status', "O")->get();

            $input['po_num'] = $request->po_num ?? null;
            $input['whse_num'] = $request->whse_num;
            $input['po_line'] = $request->po_line ?? null;
            $input['po_rel'] = $request->ref_release ?? null;
            $input['item_num'] = $request->item_num ?? null;
            $input['uom'] = $request->uom ?? null;
            $input['qty_required'] = $request->qty_required ?? null;
            $input['item_desc'] = $request->item_desc ?? null;
            $input['receipt_type'] = $request->receipt_type ?? null;
            $input['grn_num'] = $request->grn_num;
            $input['vend_num'] = $request->vend_num;
            $input['vend_do'] = $request->vend_do;
            $input['indicate'] = 3;
            $input['indicator_from'] =  $request->indicator;
            $input['count'] = $grn_list->count();
            // dd($input);
            $url = generateRedirectUrl('GRNReceipt', $input);
        } else {
            // Add New GRN
            $po = DB::query()->from('po_items')
                ->select(
                    'po_num',
                    'po_line',
                    'item_num',
                    'item_desc',
                    // DB::raw('IFNULL(qty_ordered,0) AS qty_ordered'),
                    DB::raw('IFNULL(qty_received,0) AS qty_received'),
                    DB::raw('IFNULL(qty_returned,0) AS qty_returned'),
                    DB::raw('IFNULL(qty_balance,0) AS qty_shipped'),
                    'uom',
                )
                ->where('po_num', $request->po_num)
                ->where('site_id', auth()->user()->site_id)
                ->get();
            $input['po_num'] = $request->po_num ?? null;
            $input['whse_num'] = $request->whse_num;
            $input['po_line'] = $request->po_line ?? null;
            $input['po_rel'] = $request->ref_release ?? null;
            $input['item_num'] = $request->item_num ?? null;
            $input['uom'] = $request->uom ?? null;
            $input['qty_required'] = $request->qty_required ?? null;
            $input['item_desc'] = $request->item_desc ?? null;
            $input['receipt_type'] = $request->receipt_type ?? null;
            $input['grn_num'] = $request->grn_num;
            $input['vend_num'] = $request->vend_num;
            $input['vend_do'] = $request->vend_do;
            $input['indicate'] = 3;
            $input['indicator_from'] =  $request->indicator;
            $input['count'] = $po->count();
            $input['frm_listing'] = $request->frm_listing;
            //dd($input,'ssssss');
            $url = generateRedirectUrl('GRNReceipt', $input);
            // dd($url);
        }

        // $grn_list = GRNItem::where('grn_num', $request->grn_num)->where('status', "O")->get();


        // dd($request->all());
        // get the poitem for new grn
        if ($request->receipt_type == "NewGRN") {
            $ItemList = PurchaseOrderItem::where('whse_num', $request->whse_num)
                ->where('rel_status', "O")
                // ->where('item_num', $request->item)
                ->where('po_line', $request->po_line)
                ->where('po_num', $request->po_num)
                ->first();

            $ItemList->qty_shipped = 0;
            $ItemList->net_received = 0;
            $ItemList->grn_num = $request->grn_num;
            $ItemList->vend_do = $request->vend_do;
        } else {
            $ItemList = GRNItem::where('grn_num', $request->grn_num)
                ->where('grn_line', $request->grn_line)
                ->where('site_id', auth()->user()->site_id)
                ->first();

            $poItem = PurchaseOrderItem::where('po_num', $ItemList->po_num)->where('po_line', $ItemList->po_line)->where('item_num', $ItemList->item_num)->first();

            if ($poItem) {
                $ItemList->vend_num = $poItem->vend_num;
            }
        }

        $ItemList->whse_num = $request->whse_num;
        $ItemList->vend_num = $ItemList->vend_num ?? $request->vend_num;

        $non_inv = $ItemList ? 0 : 1;

        $tparm = new TparmView();
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $allow_over_receive = $tparm->getTparmValue('POReceipt', 'allow_over_receive');
        $disable_create_new_item_location = $tparm->getTparmValue('POReceipt', 'disable_create_new_item_location');
        $printLabel = $tparm->getTparmValue('POReceipt', 'print_label');
        $batch_id = generateBatchId("POReceipt");

        $defaults = $this->getLocByRankReceipt($ItemList->whse_num, $ItemList->item_num);

        $def_receipt_location = $tparm->getTparmValue('POReceipt', 'def_receipt_location');
        $def_loc = "";
        if ($def_receipt_location) {
            $def_receipt_location = $tparm->getTparmValueDefLocation('POReceipt', $ItemList->whse_num);

            if ($def_receipt_location != null) {
                $defaults['loc_num'] = $def_receipt_location->loc_num;
                $def_loc = $def_receipt_location->loc_num;
            } else {
                $defaults['loc_num'];
                $def_loc = "";
            }
        }
        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

        $item = Item::select('lot_tracked', 'catch_weight')->where('item_num', $ItemList->item_num)->first();

        $ItemList['lot_tracked'] = $item->lot_tracked ?? 0;

        // Set to 1 if item not exists due to it is non-inventory
        $non_inv = $item ? 0 : 1;

        $ItemList['qty_balance'] = $ItemList->qty_shipped - $ItemList->net_received;

        // get the qty balance of the PO Line
        $po_balance = PurchaseOrderItem::where('po_num', $request->po_num)
            ->where('po_line', $ItemList->po_line)
            ->get()->pluck('qty_balance')
            ->first();

        // // get sum of the GRN net received for the same PO Line
        // $total_net_received = GRNItem::select(DB::raw('SUM(IFNULL(net_received,0)) as total_net_received'))
        //             ->where('po_num',$request->po_num)
        //             ->where('po_line',$ItemList->po_line)
        //             ->get()->pluck('total_net_received')
        //             ->first();
        $ItemList['max_qty_input'] = $po_balance; // - $total_net_received;
        $vendor_do = $request->vend_do;
        // GRN::select('vend_do')
        //     ->where('grn_num', $request->grn_num)
        //     ->where('vend_do', '!=', null)
        //     ->where('site_id', auth()->user()->site_id)
        //     ->value('vend_do');
        // dd($ItemList,$request->all(),$po_balance,$total_net_received,$ItemList['max_qty_input']);
        $view =  $item->catch_weight ? 'Receiving.poreceipt.grnProcess_cw' :'Receiving.poreceipt.grnProcess';
        $printer_options = [];
        return view($view)
            ->with('ItemList', $ItemList)
            ->with('vend_do', '')
            ->with('receipt_type', $request->receipt_type)
            ->with('defaults', $defaults)
            ->with('allow_over_receive', $allow_over_receive)
            ->with('disable_create_new_item_location', $disable_create_new_item_location)
            ->with('sap_trans_order_integration', $sap_trans_order_integration)
            ->with('unit_quantity_format', $unit_quantity_format)
            ->with('non_inv', $non_inv)
            ->with('vendor_do', $vendor_do)
            ->with('url', $url->getTargetUrl())
            ->with('def_loc', $def_loc)
            ->with('batch_id', $batch_id)
            ->with('poItem', @$poItem)
            ->with('printLabel', $printLabel)
        ;
    }

    public function checkLPNPoReceipt(Request $request)
    {
        $po = new PurchaseOrderItem();
        if ($request->ref_release == null) {
            $request['ref_release'] = '';
        }

        $PoItemList = $po->where('po_num', $request->ref_num)->where('po_rel', $request->ref_release)->where('po_line', $request->ref_line)->first();
        //dd($PoItemList,$request);
        // Verifying PO exist
        if (!$PoItemList) {
            throw ValidationException::withMessages([__('error.mobile.notexist', ['resource' => '[' . $request->ref_num . '-' . $request->ref_line . ']'])]);
        }

        // Verify PO status
        if ($PoItemList->rel_status == 'C') {
            throw ValidationException::withMessages([__('error.mobile.status_is_completed', ['resource' => __('admin.label.po') . '-' . $request->ref_num])]);
        }

        // check item loc freeze
        $itemLoc = new ItemLoc();
        $itemLoc = $itemLoc->where('whse_num', $PoItemList->whse_num)->where('loc_num', $request->loc_num)->where('item_num', $PoItemList->item_num)->first();
        if ($itemLoc && $itemLoc->freeze == 'Y') {
            throw ValidationException::withMessages([__('error.mobile.loc_freeze', ['resource' => $PoItemList->item_num, 'resource2' => $request->loc_num])]);
        }

        // Rewrite the ReadOnly fields
        $request['ref_num'] = $PoItemList->po_num;
        $request['ref_line'] = $PoItemList->po_line;
        $request['ref_release'] = $PoItemList->po_rel;
        $request['whse_num'] = $PoItemList->whse_num;
        $request['item_num'] = $PoItemList->item_num;
        $request['base_uom'] = $PoItemList->uom;
        $request['vend_do'] = $PoItemList->vend_do;

        // dd($request->no_of_lpn_exist);
        if ($request->no_of_lpn_exist != 0) {
            $po_date = PurchaseOrder::where('po_num', $request->ref_num)->value('due_date');
            $lpnNum = '';
            for ($i = 0; $i < $request->no_of_lpn; $i++) {
                // do adding header based on the lpn definition.
                $checkRunningNumber = LicensePlateNumberDefinition::first();

                //dd($checkRunningNumber);
                if ($checkRunningNumber != "" && @$checkRunningNumber->status == 'Y') {
                    $maxRunningNum = str_repeat(9, $checkRunningNumber->running_number_digits); // Add 9 to get the max value of set running number. ex: running number = 2, max value will be 99.
                    if ($checkRunningNumber->last_running_number_display == $maxRunningNum) {
                        throw ValidationException::withMessages([__('error.admin.max_lpn_num', ['resource' => $maxRunningNum])]);
                    }
                } else {

                    if (@$checkRunningNumber->status == 'N') {
                        throw ValidationException::withMessages([__('error.mobile.lpn_inactive')]);
                    } else {
                        throw ValidationException::withMessages([__('error.mobile.lpn_not_defined')]);
                    }


                    // throw ValidationException::withMessages([__('error.mobile.no_active_lpn')]);
                }
                $generateLPN = LPNDefinitionService::generateLPN();
                // Store LPN Definition last number
                $updateLPNDef = LPNDefinitionService::updateLPNDef($generateLPN);
                $datetime = now()->setTimezone(auth()->user()->timezone)->format(SiteSetting::getOutputDateFormat() . ' H:i:s');
                // dd($checkRunningNumber);
                // Store LPN into Pallet
                $result = Container::create([
                    'lpn_num' => $generateLPN,
                    'whse_num' => $request->whse_num,
                    'loc_num' => $request->loc_num,
                    'single_item' => 1,
                    'usage_restriction' => 0,
                    'creation_date' => $datetime,
                    'source' => 'PO Receipt',
                    'ref_num' => $request->ref_num,
                    'ref_line' => $request->ref_line,
                    'status' => 'Open',
                    'site_id' => auth()->user()->site_id,
                    'created_by' => auth()->user()->name,
                ]);

                if ($lpnNum != '') {
                    $lpnNum = $lpnNum . ',' . $generateLPN;
                } else {
                    $lpnNum = $generateLPN;
                }
            }

            // $lpn_num = $lpnNum;
            $totalLpnNum = explode(',', $lpnNum);
            $request['lpnNum'] = $lpnNum;
            $request['totalLpnNum'] = $totalLpnNum;
            // dd($request);
            $record = json_encode($request->all());
            // route to generate LPN page.
            // return $this->displayGenerateLPNPage($lpnNum, $request);
            // return redirect()->route('LPNList', [$lpnNum,$record]);
            // return redirect()->route('LPNList', [$lpn_num, $record]);
            return view('Receiving.poreceipt.lpnList')->with('request', $request)->with('record', $record);
        } else {

            return POReceiveController::runPoProcess($request);
        }
    }


    public function runPoProcess(Request $request)
    {
        $batch_id = $request->batch_id;

        if ($batch_id && checkBatchIdExists($batch_id)) {
            throw ValidationException::withMessages([__('error.admin.batch_id_exists')]);
        }

        $errors = [];
        $request = validateSansentiveValue($request);
        // $validateErrors = self::poReceiveValidation($request);
        $siteSettings = new SiteSetting();
        $this->timezone = $siteSettings->getTimezone();
        $now =  Timezone::convertFromUTC(now(), $this->timezone, SiteSetting::getOutputDateFormat() .' H:i:s');

        //  dd($request);

        $tparm = new TparmView;
        $sap_require_check_online = $tparm->getTparmValue('System', 'sap_check_online_connection');

        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

        if ($sap_require_check_online == 1 && $sap_trans_order_integration == 1) {
            // Checking SAP Server
            $site_id = auth()->user()->site_id;
            $checkConnection = SapApiCallService::getSQPServerConnection($site_id, null, 'PO Receipt - Batch Sync', 1);

            if ($checkConnection > 2) {
                Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_server_down'))->persistent('Dismiss');
                return redirect()->back();
            }
        }



        $po = new PurchaseOrderItem();
        $PoItemList = $po->where('po_num', $request->ref_num)->where('po_rel', $request->ref_release)->where('po_line', $request->ref_line)->where('site_id', auth()->user()->site_id)->first();

        // Verifying PO exist
        if (!$PoItemList) {
            throw ValidationException::withMessages([__('error.mobile.notexist', ['resource' => '[' . $request->ref_num . '-' . $request->ref_line . ']'])]);
        }

        // check item loc freeze
        $itemLoc = new ItemLoc();
        $itemLoc = $itemLoc->where('whse_num', $PoItemList->whse_num)->where('loc_num', $request->loc_num)->where('item_num', $PoItemList->item_num)->where('site_id', auth()->user()->site_id)->first();
        if ($itemLoc && $itemLoc->freeze == 'Y') {
            throw ValidationException::withMessages([__('error.mobile.loc_freeze', ['resource' => $PoItemList->item_num, 'resource2' => $request->loc_num])]);
        }


        // Rewrite the ReadOnly fields
        $request['ref_num'] = $PoItemList->po_num;
        $request['ref_line'] = $PoItemList->po_line;
        $request['ref_release'] = $PoItemList->po_rel;
        $request['whse_num'] = $PoItemList->whse_num;
        $request['item_num'] = $PoItemList->item_num;
        $request['base_uom'] = $PoItemList->uom;
        $request['vend_do'] = $PoItemList->vend_num;

        //dd($PoItemList,$request);

        $no_of_lpn = $request->no_of_lpn;

        Session::put('no_of_lpn', $no_of_lpn);

        // if got lpn num put into array.
        $getRecordData = $request->all();
        if ($request->lpn_num != "") {
            if (isset($request->record)) {
                $getRecordData = json_decode($request->record, true);
                $request['lpnNumList'] = $getRecordData['totalLpnNum'];
            } else {
                $getRecordData = $request->all();
                $request['lpnNumList'] = [$request->lpn_num];
            }
        }

        // if($request->no_of_lpn_exist != 0){
        //     $po_date = PurchaseOrder::where('po_num', $request->ref_num)->value('due_date');

        //     for($i = 0; $i < $request->no_of_lpn; $i++){
        //         // do adding header based on the lpn definition.
        //         $checkRunningNumber = LicensePlateNumberDefinition::where('status','Y')->first();
        //         if($checkRunningNumber != ""){
        //             $maxRunningNum = str_repeat(9, $checkRunningNumber->running_number_digits); // Add 9 to get the max value of set running number. ex: running number = 2, max value will be 99.
        //             if($checkRunningNumber->last_running_number_display == $maxRunningNum){
        //                 throw ValidationException::withMessages([__('error.admin.max_lpn_num',['resource' => $maxRunningNum])]);
        //             }
        //         }
        //         $generateLPN = LPNDefinitionService::generateLPN();
        //         // Store LPN Definition last number
        //         $updateLPNDef = LPNDefinitionService::updateLPNDef($generateLPN);
        //         // Store LPN into Pallet
        //         $result = Container::create([
        //             'lpn_num' => $generateLPN,
        //             'whse_num' => $request->whse_num,
        //             'loc_num' => $request->loc_num,
        //             'single_item' => 1,
        //             'usage_restriction' => 0,
        //             'creation_date' => $po_date,
        //             'source' => 'PO Receipt',
        //             'ref_num' => $request->ref_num,
        //             'ref_line' => $request->ref_line,
        //             'status' => 'Open',
        //             'site_id' => auth()->user()->site_id,
        //             'created_by' => auth()->user()->name,
        //         ]);
        //     }


        //     // route to generate LPN page.

        // }
        // dd('qwe');
        // if($request->sohai=="")
        // {
        //     dd('here');
        // }
        //  dd($request);
        $pass_uom = $request->uom;
        // dd($pass_uom);
        $this->middleware('auth');
        $request = validateSansentiveValue($request);
        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
        $sap_single_bin = $tparm->getTparmValue('System', 'sap_single_bin');
        if ($request->lot_num == "") {
            $request->merge([
                'lot_num' => null
            ]);
        }

        if ($sap_trans_order_integration == 1) {

            $arrJsonEncodeParameters = json_encode($request->except('_token'));
            // Checking the SAME Doc only for Same Vendor
            $checkpo_details = DB::table('sap_po_batch_sync')->where('PO_type', 1)->where('doc_num', $request->document_num)->where('site_id', auth()->user()->site_id)->where('sync_status', 1)->orderBy('po_line', 'ASC')->get()->toArray();
            if (isset($checkpo_details)) {
                foreach ($checkpo_details as $key => $value) {
                    // dd($value->vend_num,$request->vend_num);
                    if ($value->vend_num != $request->vend_num) {
                        // This Doc Number exists and belongs to another Vendor. You cannot use the same Doc Number for different Vendor.
                        throw ValidationException::withMessages([__('error.mobile.vendor_diff')]);
                    }
                }
            }
        }



        // $arrJsonDeEncodeparameters = json_decode( $arrJsonEncodeParameters,true);

        // $request->merge([]);
        //$arr = $request->merge([$arrJsonDeEncodeparameters]);
        // $request = $request->merge($arrJsonDeEncodeparameters);

        //dd($arrJsonEncodeParameters,$request);

        DB::beginTransaction();
        try {
            $po_num = $request->ref_num;
            $po_line = $request->ref_line;
            $po_rel = $request->ref_release;
            // $request->original_uom = $request->base_uom;
            $request->merge([
                'original_uom' => $request->base_uom,
            ]);

            $item_num = $request->item_num;
            $site_id = auth()->user()->site_id;
            $info = DB::table('middleware_connections')->where('site_id', $site_id)->get() ?? NULL;

            @$countInfo = count($info);

            // Store in Location
            $checkLoc = Loc::where('loc_num', $request->loc_num)->where('whse_num', $request->whse_num)->where('site_id', $site_id)->first();
            // If not exist, store it
            if (!$checkLoc) {
                $loc = new Loc;
                $loc->whse_num = $request->whse_num;
                $loc->loc_num = $request->loc_num;
                $loc->loc_type = "S";
                $loc->loc_status = 1;
                $loc->save();
            } else {
                if ($checkLoc->loc_status == 0) {
                    throw ValidationException::withMessages([__('error.mobile.processinactive', ['resource' => __('mobile.list.locations')])]);
                }
            }




            // Store in sap_po_batch_sync table
            // if ($request->last_receive == "No") {
            Session::put('modulename', 'POReceipt');
            Session::put('po_num', $request->ref_num);
            Session::put('whse_num', $request->whse_num);
            $transDate = Carbon::now()->toDateTimeString();
            Session::put('timestamp', $transDate);
            $transType = 'PO Receipt';
            $pomodel = new PurchaseOrderItem();
            $qty = $request->qty;
            $uom = $request->uom;


            $vend = $request->vend_num ?? $request->vend_do;

            // $request->base_uom = Item::where('item_num', $request->item_num)->pluck('uom')->first();


            if (@$request->non_inv == 1 && @$request->item_num == "NON-INV") {
                $getBaseUom = $request->original_uom;
                // $request->merge([

                //     'loc_num' => ''

                // ]);
            } else {
                $getBaseUom = Item::where('item_num', $request->item_num)->where('site_id', auth()->user()->site_id)->pluck('uom')->first();
            }






            $request->merge([
                'base_uom' => $getBaseUom,
                'vend_num' => $PoItemList->vend_num ?? $request->vend_do
            ]);


            // Conversion for inventory and material trans
            // $request = UOMService::convertRequest($request);
            // dd($request);
            $convertUom = UomConv::convertUOM($request->base_uom, $uom, $request->original_uom, $qty, $item_num, '', $vend, __('mobile.nav.po_receipt'));

            $request['qty_conv'] = $convertUom['conv_qty_to_base']['qty'];
            $request['uom_conv'] = $convertUom['conv_qty_to_base']['uom'];
            $request->merge([
                'qty_conv' => $convertUom['conv_qty_to_base']['qty'] ?? 0,
                'uom_conv' => $request->base_uom ?? 0,

            ]);
            // dd($convertUom);
            $request['transtype'] = __('mobile.nav.po_receipt');
            $intNoOfLPN = 1;
            if ($request->lpnNumList != null) {
                $intNoOfLPN = count($request->lpnNumList) ?? 1;
            }

            $po_qty = $convertUom['conv_qty_to_line']['qty'] * $intNoOfLPN;
            // dd($request);
            // Conversion for PO item using Qty and UOM converted to item's base UOM
            // $po_qty = UOMService::convertPORequest($request);


            if (isset($request->lpnNumList) && count($request->lpnNumList) > 0) {
                // insert PO item into LPN
                // dd($request);
                foreach ($request->lpnNumList as $lpnList) {
                    $record = new Request($getRecordData);
                    $record->lpn_num = $lpnList;
                    $record->trans_type = $transType;

                    $getLPNline = ContainerItem::where('lpn_num', $request->lpn_num)->max('lpn_line');

                    // $record->qty_conv = $request['qty_conv'] ;
                    // $record->uom_conv = $request['uom_conv'] ;

                    $record->merge([
                        'qty_conv' => $convertUom['conv_qty_to_base']['qty'] ?? 0,
                        'uom_conv' => $request->base_uom ?? 0,
                        'lpn_num' => $lpnList,
                        'lpn_line' => $getLPNline + 1,
                        'base_uom' => $getBaseUom

                    ]);
                    // dd("hello",$record,$request,$convertUom['conv_qty_to_base']['qty']);

                    $toArr = [
                        'whse_num' => $request->whse_num,
                        'loc_num' => $request->loc_num,
                        'lot_num' => $request->lot_num ?? $record->lot_num,
                        'item_num' => $request->item_num,
                        'qty_conv' => $request->qty_conv,
                        'uom_conv' => $request->uom_conv,
                    ];

                    $fromArr = [];
                    // dd($record,$request);
                    $updateItemLocation = PalletService::updateItemLocLotQty($fromArr, $toArr, $request->non_inv, 'To PO Receipt');

                    $insertPalletData = PalletService::updatePalletItemQty($record);


                    $insertmatl = GeneralService::newMatlTrans($transType, $record);


                    //  $request->replace(['whse_num' => $record->whse_num, 'ref_num' => $record->ref_num, 'ref_release' => $record->ref_release, 'ref_line' => $record->ref_line, 'item_num' => $record->item_num, 'vend_do' => $record->vend_do,'mfg_date'=>$record->mfg_date,'expiry_date'=> $record->expiry_date]);
                    //dd("ssss",$request);
                    //  $lotServ =  LotService::updateLot("PO Receipt", $request);


                }
            } else {
                // Normall

                if ($sap_trans_order_integration != 1) {

                    if ($request->item_num != "NON-INV") {
                        $updateItemLocation = GeneralService::updateItemLocationQty($request->whse_num, $request->loc_num, $request->item_num, $request->qty_conv, $request->lot_num, $request->uom_conv, $request->non_inv);
                    }

                    //dd($request);
                    $insertmatl = GeneralService::newMatlTrans($transType, $request);
                }
            }

            if ($sap_trans_order_integration != 1) {
                $record = new Request($getRecordData);
                //dd($record,$request);
                $updatePOItem = POService::updatePoItemQty($request->ref_num, $request->ref_line, $request->ref_release, $po_qty);
                // $request->replace([
                //     'whse_num' => $record->whse_num,
                //     'ref_num' => $record->ref_num,
                //     'ref_release' => $record->ref_release,
                //     'ref_line' => $record->ref_line,
                //     'item_num' => $record->item_num,
                //     'mfg_date' => $record->mfg_date,
                //     'expiry_date' => $record->expiry_date,
                //     'vend_lot' => $record->vend_num,
                //     'lot_num' => $record->lot_num,
                //     'qty' => $request->qty,
                // ]);
                // dd($request->qty);
                if (!$updatePOItem) {
                    $request->replace(['whse_num' => $request->whse_num, 'po_num' => $request->ref_num, 'po_rel' => $request->ref_release, 'po_line' => $request->ref_line, 'item_num' => $request->item_num, 'vend_do' => $request->vend_do]);
                    Alert::error('Error', 'Qty to receive is more than Qty required');
                    return $this->showPoProcess($request);
                }
                //  dd( $request->uom);
                // if($request->lpn_num != ""){
                //     $toArr = [
                //         'whse_num' => $request->whse_num,
                //         'loc_num' => $request->loc_num,
                //         'lot_num' => $request->lot_num,
                //         'item_num' => $request->item_num,
                //         'qty_conv' => $request->qty_conv,
                //         'uom_conv' => $request->uom_conv,
                //     ];

                //     $fromArr = [];

                //     $updateItemLocation = PalletService::updateItemLocLotQty($fromArr, $toArr, $request->non_inv, 'To PO Receipt');

                //     $insertPalletData = PalletService::updatePalletItemQty($request);
                // }
                // else{
                //     $updateItemLocation = GeneralService::updateItemLocationQty($request->whse_num, $request->loc_num, $request->item_num, $request->qty_conv, $request->lot_num, $request->uom_conv, $request->non_inv);
                // }

                // $line = ContainerItem::select('lpn_line')->where('lpn_num', $request->lpn_num)->where('site_id',auth()->user()->site_id)->where('created_by',auth()->user()->name)->orderByRaw('CAST(lpn_line as UNSIGNED) DESC')->first();
                // $request->merge([
                //     'lpn_line' => $line->lpn_line
                // ]);

                // $insertmatl = GeneralService::newMatlTrans($transType, $request);
                //dd("zzz",$request);
                $lotServ =  LotService::updateLot("PO Receipt", $request);
            }

            // update preassign lots
            $uom_conv = UomConv::convert($request->uom, $po_qty, $request->item_num, null, null, null);
            PreassignLotsService::updatePreassignLot('po', $request->ref_num, $request->ref_line, $request->item_num, $request->lot_num, auth()->user()->site_id, $uom_conv['qty']);


            DB::commit();


            $req_rioginal_uom = $pass_uom;
            // dd($request->uom);
            $printLabelReq = $request;
            if ($printLabelReq->lot_num != null) {
                $check_expiry_date = LotService::getExpiryDate($printLabelReq);

                // Generate barcode
                // changed $request->qty to $qty because $request->qty was empty
                $print_input = app('App\Http\Controllers\BarcodeController')->GetPOReceiveLabelData($printLabelReq->ref_num, $printLabelReq->ref_line,  $request->qty_conv, $printLabelReq->whse_num, null, 'PoReceipt', $printLabelReq->loc_num, $printLabelReq->lot_num, $check_expiry_date, $printLabelReq->vend_lot, $printLabelReq->uom_conv);
            } else {
                // changed $request->qty to $qty because $request->qty was empty

                // Generate barcode
                $print_input = app('App\Http\Controllers\BarcodeController')->GetPOReceiveLabelData($printLabelReq->ref_num, $printLabelReq->ref_line,  $request->qty_conv, $printLabelReq->whse_num, null, 'PoReceipt', $printLabelReq->loc_num, $printLabelReq->lot_num, null, $printLabelReq->vend_lot, $printLabelReq->uom_conv);
            }
            if ($sap_trans_order_integration == 1) {
                $baseuom = Item::where('item_num', $item_num)->where('site_id', auth()->user()->site_id)->value('uom');
                // $selectuom = '', $lineuom = ''
                $convertUom = UomConv::convertUOM($baseuom, $request->uom, $request->original_uom, $request->qty, $item_num, '', '', __('mobile.nav.po_receive'));

                $qtyafterconvert = $convertUom['conv_qty_to_line']['qty'];
                DB::table('sap_po_batch_sync')->insert([
                    'po_num' => $po_num,
                    'po_line' => $po_line,
                    'po_rel' => $po_rel ?? 0,
                    'erp_ID' => PurchaseOrderItem::select('erp_ID')->where('po_num', $po_num)
                        ->where('po_line', $po_line)
                        //->where('po_rel', $po_rel)
                        ->where('site_id', auth()->user()->site_id)
                        ->value('erp_ID'),
                    'whse_num' => $request->whse_num,
                    'po_type' => 1,
                    'loc_num' => $request->loc_num,
                    'lot_num' => $request->lot_num,
                    'expiry_date' => $request->expiry_date,
                    'mfg_date' => $request->mfg_date,
                    'item_num' => $item_num,
                    'vend_do' => $request->vend_do,
                    'vend_lot' => $request->vend_lot,
                    'vend_num' => $request->vend_num,
                    'qty_received' => $qtyafterconvert,
                    'qty_received_uom' => $request->uom,
                    'qty_required' => $request->qty_required,
                    'qty_required_uom' => $request->original_uom,
                    'qty_conv' => $request->qty_conv,
                    'doc_num' => $request->document_num,
                    'sync_status' => 1,
                    // "sync_initiated" => $request->last_receive == "Yes" ? 1 : 0,
                    'site_id' => auth()->user()->site_id,
                    'created_by' => auth()->user()->name,
                    'modified_by' => auth()->user()->name,
                    'created_date' => $now,
                    'modified_date' => $now,
                    'json_parameters' => $arrJsonEncodeParameters
                ]);


                if ($request->last_receive == "Yes") {


                    if (@$request->document_num != "" || @$request->document_num != null) {
                        // Query all relate PO based on the document no.

                        $po_details = DB::table('sap_po_batch_sync')->where('PO_type', 1)->where('vend_num', $request->vend_num)->where('doc_num', $request->document_num)->where('site_id', auth()->user()->site_id)->whereIn('sync_status', [1, 2])->orderBy('po_line', 'ASC')->get()->toArray();

                        if (isset($po_details)) {
                            //dd($request->qty);
                            $record = new Request($getRecordData);
                            $request->replace(['whse_num' => $record->whse_num, 'ref_num' => $record->ref_num, 'ref_release' => $record->ref_release, 'ref_line' => $record->ref_line, 'item_num' => $record->item_num, 'mfg_date' => $record->mfg_date, 'expiry_date' => $record->expiry_date, 'vend_lot' => $record->vend_num, 'lot_num' => $record->lot_num]);
                            foreach ($po_details as $key => $value) {
                                $arrJsonDeEncodeparameters = @json_decode($value->json_parameters, true);

                                //dd($arrJsonDeEncodeparameters);

                                $request->merge([]);
                                //$arr = $request->merge([$arrJsonDeEncodeparameters]);
                                $request = $request->merge($arrJsonDeEncodeparameters);

                                $transType = 'PO Receipt';
                                $pomodel = new PurchaseOrderItem();
                                $qty = $request->qty;

                                $uom = $request->uom;
                                $request->base_uom = Item::where('item_num', $request->item_num)->pluck('uom')->first();

                                // Conversion for inventory and material trans
                                // $request = UOMService::convertRequest($request);

                                $convertUom = UomConv::convertUOM($request->base_uom, $uom, $request->original_uom, $qty, $item_num, '', $request->vend_num, __('mobile.nav.po_receipt'));

                                $request['qty_conv'] = $convertUom['conv_qty_to_base']['qty'];
                                $request['uom_conv'] = $convertUom['conv_qty_to_base']['uom'];

                                // Conversion for PO item using Qty and UOM converted to item's base UOM

                                if (isset($request->lpnNumList) && count($request->lpnNumList) > 0) {
                                    // This for LPN purposes
                                    $poqty = $po_qty;
                                } else {
                                    $poqty = UOMService::convertPORequest($request);
                                }

                                $updatePOItem = POService::updatePoItemQty($request->ref_num, $request->ref_line, $request->ref_release, $poqty);




                                if (!$updatePOItem) {
                                    $request->replace(['whse_num' => $request->whse_num, 'po_num' => $request->ref_num, 'po_rel' => $request->ref_release, 'po_line' => $request->ref_line, 'item_num' => $request->item_num, 'vend_do' => $request->vend_do]);
                                    Alert::error('Error', 'Qty Receive more than Qty Ordered');
                                    return $this->showPoProcess($request);
                                }

                                $updateItemLocation = GeneralService::updateItemLocationQty($request->whse_num, $request->loc_num, $request->item_num, $request->qty_conv, $request->lot_num, $request->uom_conv, $request->non_inv);



                                $insertmatl = GeneralService::newMatlTrans($transType, $request);

                                LotService::updateLot("PO Receipt", $request);
                            }
                        }

                       /* $env_services = config('icapt.enable_sap_poreceive_to_drafts');
                        $conn_key = "Post PO Receipt";
                        $params = [$request, null];
                        if ($sap_single_bin == 1) {

                            $result = SiteConnectionService::postIntergrationTransPO("PO Receipt", $request, null);
                        } else {

                            if (config('icapt.enable_sap_resync')) {
                                //dd($request);
                                $result = SapCallService::postPOReceiveResync($request, null, $env_services);
                            } else {

                                if (config('icapt.enable_sap_ap_readfrom_maltrans')) {
                                    // Later Change to Maltran
                                    $result = SapCallService::postPOReceive($request, null, $env_services);
                                } else {
                                    $result = SapCallService::postPOReceive($request, null, $env_services);
                                }
                            }
                        }*/
                    }
                }
            }

            // dd(rand(1,393939));

           /* if (@$request->radiostacked1 != 'other') {



                // if ($sap_trans_order_integration == 1){
                //     if (@$result != 200) {
                //         Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result)->persistent('Dismiss');
                //         //return redirect()->back();
                //     }else{
                //     Alert::success('Success', __('success.processed', ['process' => __('Purchase Order Receipt')]));


                //     if ($request->lpn_num != null) {
                //         Alert::success('Success', __('success.added', ['resource' => __('Pallet(s)')]));
                //         }
                //     }
                // }
                // else
                // {

                Alert::success('Success', __('success.processed', ['process' => __('Purchase Order Receipt')]));


                if ($request->lpn_num != null) {
                    Alert::success('Success', __('success.added', ['resource' => __('Pallet(s)')]));
                }


                // }

                $tparm = new TparmView;
                $print_label = $tparm->getTparmValue('POReceipt', 'print_label');
                if (!$pomodel->checkActivePOLine($printLabelReq->ref_num)) {
                    if ($print_label == 1) {
                        //dd('uuu');
                        return BarcodeController::showLabelDefinition($print_input);
                    } else {
                        return $this->index();
                    }
                } else {


                    // dd($no_of_lpn);

                    // dd($input);
                    $print_input['disable_qty'] = false;
                    if ($print_label == 1) {
                        return BarcodeController::showLabelDefinition($print_input);
                    } else {

                        $input['po_num'] = $request->po_num;
                        $input['whse_num'] = $request->whse_num;
                        $input['po_line'] = $request->po_line;
                        $input['po_rel'] = $request->ref_release;
                        $input['item_num'] = $request->item_num;
                        $input['uom'] = $request->uom;
                        $input['qty_required'] = $request->qty_required;
                        $input['item_desc'] = $request->item_desc;
                        $input['indicate'] = 0;
                        // $input['vend_do'] = $request->vend_do;
                        // return redirect()->route('showPoLineReturn', ['whse_num' => $request->whse_num, 'po_num' => $request->ref_num, 'item_num' => $request->item_num, 'vend_do' => $request->vend_do]);
                        $url = generateRedirectUrl('PoReceipt', $input);
                        //$cancel_href = $url;
                        //return $url;


                        return app('App\Http\Controllers\RouteController')->BackButton();
                    }

                    //return BarcodeController::showLabelDefinition($print_input);
                }
            }*/
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }

        // dd(rand(1,393939));

        if (@$request->radiostacked1 != 'other') {



            // if ($sap_trans_order_integration == 1){
            //     if (@$result != 200) {
            //         Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result)->persistent('Dismiss');
            //         //return redirect()->back();
            //     }else{
            //     Alert::success('Success', __('success.processed', ['process' => __('Purchase Order Receipt')]));


            //     if ($request->lpn_num != null) {
            //         Alert::success('Success', __('success.added', ['resource' => __('Pallet(s)')]));
            //         }
            //     }
            // }
            // else
            // {

            if ($request->last_receive == "Yes" && $sap_trans_order_integration == 1) {


                $env_services = config('icapt.enable_sap_poreceive_to_drafts');
                $conn_key = "Post PO Receipt";
                $params = [$request, null];
                if ($sap_single_bin == 1) {

                    $result = SiteConnectionService::postIntergrationTransPO("PO Receipt", $request, null);
                } else {

                    if (config('icapt.enable_sap_resync')) {
                        //dd($request);
                        $result = SapCallService::postPOReceiveResync($request, null, $env_services);
                    } else {

                        if (config('icapt.enable_sap_ap_readfrom_maltrans')) {
                            // Later Change to Maltran
                            $result = SapCallService::postPOReceive($request, null, $env_services);
                        } else {
                            $result = SapCallService::postPOReceive($request, null, $env_services);
                        }
                    }
                }
            }

            Alert::success('Success', __('success.processed', ['process' => __('Purchase Order Receipt')]));


            if ($request->lpn_num != null) {
                Alert::success('Success', __('success.added', ['resource' => __('Pallet(s)')]));
            }


            // }

            $tparm = new TparmView;
            $print_label = $tparm->getTparmValue('POReceipt', 'print_label');
            if (!$pomodel->checkActivePOLine($printLabelReq->ref_num)) {
                if ($print_label == 1) {
                    //dd('uuu');
                    return BarcodeController::showLabelDefinition($print_input);
                } else {
                    return $this->index();
                }
            } else {


                // dd($no_of_lpn);

                // dd($input);
                $print_input['disable_qty'] = false;
                if ($print_label == 1) {
                    return BarcodeController::showLabelDefinition($print_input);
                } else {

                    $input['po_num'] = $request->po_num;
                    $input['whse_num'] = $request->whse_num;
                    $input['po_line'] = $request->po_line;
                    $input['po_rel'] = $request->ref_release;
                    $input['item_num'] = $request->item_num;
                    $input['uom'] = $request->uom;
                    $input['qty_required'] = $request->qty_required;
                    $input['item_desc'] = $request->item_desc;
                    $input['indicate'] = 0;
                    // $input['vend_do'] = $request->vend_do;
                    // return redirect()->route('showPoLineReturn', ['whse_num' => $request->whse_num, 'po_num' => $request->ref_num, 'item_num' => $request->item_num, 'vend_do' => $request->vend_do]);
                    $url = generateRedirectUrl('PoReceipt', $input);
                    //$cancel_href = $url;
                    //return $url;


                    return app('App\Http\Controllers\RouteController')->BackButton();
                }

                //return BarcodeController::showLabelDefinition($print_input);
            }
        }

    }

    public function runPoCWProcess(Request $request)
    {
        $batch_id = $request->batch_id;

        if ($batch_id && checkBatchIdExists($batch_id)) {
            throw ValidationException::withMessages([__('error.admin.batch_id_exists')]);
        }

        // Validate PO
        $po_num = $request->ref_num;
        $po_line = $request->ref_line;
        if (empty($po_num) || empty($po_line))
        {
            throw ValidationException::withMessages([ __('error.mobile.does_not_exists', ['resource' => __('admin.label.purchase_order') ]) ]);
        }

        $po_item = PurchaseOrderItem::where('po_num', $po_num)->where('po_line', $po_line)->first();
        if (!$po_item)
        {
            throw ValidationException::withMessages([ __('error.mobile.does_not_exists', ['resource' => __('admin.label.purchase_order') ]) ]);
        }

        // Get Tolerance and UOM
        $tolerance = $po_item->qty_ordered - $po_item->qty_received + $po_item->qty_returned;
        $tolerance_uom = $po_item->uom;

        // Validate Catch Weight Data
        CatchWeightService::validateCatchWeightData($request, $tolerance, $tolerance_uom);

        $siteSettings = new SiteSetting();
        $this->timezone = $siteSettings->getTimezone();
        $now =  Timezone::convertFromUTC(now(), $this->timezone, SiteSetting::getOutputDateFormat() .' H:i:s');

        $tparm = new TparmView;
        $sap_require_check_online = $tparm->getTparmValue('System', 'sap_check_online_connection');
        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

        if ($sap_require_check_online == 1 && $sap_trans_order_integration == 1) {
            // Checking SAP Server
            $site_id = auth()->user()->site_id;
            $checkConnection = SapApiCallService::getSQPServerConnection($site_id, null, 'PO Receipt - Batch Sync', 1);

            if ($checkConnection > 2) {
                Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_server_down'))->persistent('Dismiss');
                return redirect()->back();
            }
        }

        // check item loc freeze
        $itemLoc = new ItemLoc();
        $itemLoc = $itemLoc->where('whse_num', $po_item->whse_num)->where('loc_num', $request->loc_num)->where('item_num', $po_item->item_num)->where('site_id', auth()->user()->site_id)->first();
        if ($itemLoc && $itemLoc->freeze == 'Y') {
            throw ValidationException::withMessages([__('error.mobile.loc_freeze', ['resource' => $po_item->item_num, 'resource2' => $request->loc_num])]);
        }

        // if got lpn num put into array.
        $getRecordData = $request->all();

        $pass_uom = $tolerance_uom;
        $this->middleware('auth');

        $sap_single_bin = $tparm->getTparmValue('System', 'sap_single_bin');

        if ($sap_trans_order_integration == 1) {

            $arrJsonEncodeParameters = json_encode($request->except('_token'));
            // Checking the SAME Doc only for Same Vendor
            $checkpo_details = DB::table('sap_po_batch_sync')->where('PO_type', 1)->where('doc_num', $request->document_num)->where('site_id', auth()->user()->site_id)->where('sync_status', 1)->orderBy('po_line', 'ASC')->get()->toArray();
            if (isset($checkpo_details)) {
                foreach ($checkpo_details as $key => $value) {
                    // dd($value->vend_num,$request->vend_num);
                    if ($value->vend_num != $request->vend_num) {
                        // This Doc Number exists and belongs to another Vendor. You cannot use the same Doc Number for different Vendor.
                        throw ValidationException::withMessages([__('error.mobile.vendor_diff')]);
                    }
                }
            }
        }


        DB::beginTransaction();
        try {
            $po_num = $request->ref_num;
            $po_line = $request->ref_line;
            $po_rel = $request->ref_release;

            $request->merge([
                'original_uom' => $tolerance_uom,
            ]);

            $item_num = $request->item_num;
            $site_id = auth()->user()->site_id;
            $info = DB::table('middleware_connections')->where('site_id', $site_id)->get() ?? NULL;

            @$countInfo = count($info);

            // Store in Location
            $checkLoc = Loc::where('loc_num', $request->loc_num)->where('whse_num', $request->whse_num)->where('site_id', $site_id)->first();
            // If not exist, store it
            if (!$checkLoc) {
                $loc = new Loc;
                $loc->whse_num = $request->whse_num;
                $loc->loc_num = $request->loc_num;
                $loc->loc_type = "S";
                $loc->loc_status = 1;
                $loc->save();
            } else {
                if ($checkLoc->loc_status == 0) {
                    throw ValidationException::withMessages([__('error.mobile.processinactive', ['resource' => __('mobile.list.locations')])]);
                }
            }




            // Store in sap_po_batch_sync table
            // if ($request->last_receive == "No") {
            Session::put('modulename', 'POReceipt');
            Session::put('po_num', $request->ref_num);
            Session::put('whse_num', $request->whse_num);
            $transDate = Carbon::now()->toDateTimeString();
            Session::put('timestamp', $transDate);
            Session::put('print_status', 'No');
            $transType = 'PO Receipt';
            $pomodel = new PurchaseOrderItem();
            $qty = array_sum($request->arr_qty ?? []);
            $uom = $tolerance_uom;


            $vend = $request->vend_num ?? $request->vend_do;
            $getBaseUom = Item::where('item_num', $request->item_num)->where('site_id', auth()->user()->site_id)->pluck('uom')->first();

            $request->merge([
                'base_uom' => $getBaseUom,
                'vend_num' => $po_item->vend_num ?? $request->vend_do
            ]);


            // Conversion for inventory and material trans
            // $request = UOMService::convertRequest($request);
            // dd($request);
            $convertUom = UomConv::convertUOM($request->base_uom, $uom, $request->original_uom, $qty, $item_num, '', $vend, __('mobile.nav.po_receipt'));

            $request['qty_conv'] = $convertUom['conv_qty_to_base']['qty'];
            $request['uom_conv'] = $convertUom['conv_qty_to_base']['uom'];
            $request->merge([
                'qty_conv' => $convertUom['conv_qty_to_base']['qty'] ?? 0,
                'uom_conv' => $request->base_uom ?? 0,
                'transtype' => __('mobile.nav.po_receipt'),
                'trans_type' => __('mobile.nav.po_receipt'),
            ]);

            $request['transtype'] = __('mobile.nav.po_receipt');
            $po_qty = $convertUom['conv_qty_to_line']['qty'];

            if ($sap_trans_order_integration != 1) {
                $updateItemLocation = CatchWeightService::updateItemLocLotNMatlTrans($request, $tolerance_uom, $transType, "PO Receipt");

                $record = new Request($getRecordData);
                $updatePOItem = POService::updatePoItemQty($request->ref_num, $request->ref_line, $request->ref_release, $po_qty);

                if (!$updatePOItem) {
                    $request->replace(['whse_num' => $request->whse_num, 'po_num' => $request->ref_num, 'po_rel' => $request->ref_release, 'po_line' => $request->ref_line, 'item_num' => $request->item_num, 'vend_do' => $request->vend_do]);
                    Alert::error('Error', 'Qty to receive is more than Qty required');
                    return $this->showPoProcess($request);
                }
            }

            DB::commit();


            // Print Barcode Pages
            $req_rioginal_uom = $pass_uom;
            $printLabelReq = $request;
            if ($printLabelReq->lot_num != null) {
                $check_expiry_date = LotService::getExpiryDate($printLabelReq);

                // Generate barcode
                // changed $request->qty to $qty because $request->qty was empty
                $print_input = app('App\Http\Controllers\BarcodeController')->GetPOReceiveLabelData($printLabelReq->ref_num, $printLabelReq->ref_line,  $request->qty_conv, $printLabelReq->whse_num, null, 'PoReceipt', $printLabelReq->loc_num, $printLabelReq->lot_num, $check_expiry_date, $printLabelReq->vend_lot, $printLabelReq->uom_conv);
            } else {
                // changed $request->qty to $qty because $request->qty was empty

                // Generate barcode
                $print_input = app('App\Http\Controllers\BarcodeController')->GetPOReceiveLabelData($printLabelReq->ref_num, $printLabelReq->ref_line,  $request->qty_conv, $printLabelReq->whse_num, null, 'PoReceipt', $printLabelReq->loc_num, $printLabelReq->lot_num, null, $printLabelReq->vend_lot, $printLabelReq->uom_conv);
            }

            // SAP Integration
            // if ($sap_trans_order_integration == 1) {
            //     $baseuom = Item::where('item_num', $item_num)->where('site_id', auth()->user()->site_id)->value('uom');
            //     // $selectuom = '', $lineuom = ''
            //     $convertUom = UomConv::convertUOM($baseuom, $request->uom, $request->original_uom, $request->qty, $item_num, '', '', __('mobile.nav.po_receive'));

            //     $qtyafterconvert = $convertUom['conv_qty_to_line']['qty'];
            //     DB::table('sap_po_batch_sync')->insert([
            //         'po_num' => $po_num,
            //         'po_line' => $po_line,
            //         'po_rel' => $po_rel ?? 0,
            //         'erp_ID' => PurchaseOrderItem::select('erp_ID')->where('po_num', $po_num)
            //             ->where('po_line', $po_line)
            //             //->where('po_rel', $po_rel)
            //             ->where('site_id', auth()->user()->site_id)
            //             ->value('erp_ID'),
            //         'whse_num' => $request->whse_num,
            //         'po_type' => 1,
            //         'loc_num' => $request->loc_num,
            //         'lot_num' => $request->lot_num,
            //         'expiry_date' => $request->expiry_date,
            //         'mfg_date' => $request->mfg_date,
            //         'item_num' => $item_num,
            //         'vend_do' => $request->vend_do,
            //         'vend_lot' => $request->vend_lot,
            //         'vend_num' => $request->vend_num,
            //         'qty_received' => $qtyafterconvert,
            //         'qty_received_uom' => $request->uom,
            //         'qty_required' => $request->qty_required,
            //         'qty_required_uom' => $request->original_uom,
            //         'qty_conv' => $request->qty_conv,
            //         'doc_num' => $request->document_num,
            //         'sync_status' => 1,
            //         // "sync_initiated" => $request->last_receive == "Yes" ? 1 : 0,
            //         'site_id' => auth()->user()->site_id,
            //         'created_by' => auth()->user()->name,
            //         'modified_by' => auth()->user()->name,
            //         'created_date' => $now,
            //         'modified_date' => $now,
            //         'json_parameters' => $arrJsonEncodeParameters
            //     ]);


            //     if ($request->last_receive == "Yes") {


            //         if (@$request->document_num != "" || @$request->document_num != null) {
            //             // Query all relate PO based on the document no.

            //             $po_details = DB::table('sap_po_batch_sync')->where('PO_type', 1)->where('vend_num', $request->vend_num)->where('doc_num', $request->document_num)->where('site_id', auth()->user()->site_id)->whereIn('sync_status', [1, 2])->orderBy('po_line', 'ASC')->get()->toArray();

            //             if (isset($po_details)) {
            //                 //dd($request->qty);
            //                 $record = new Request($getRecordData);
            //                 $request->replace(['whse_num' => $record->whse_num, 'ref_num' => $record->ref_num, 'ref_release' => $record->ref_release, 'ref_line' => $record->ref_line, 'item_num' => $record->item_num, 'mfg_date' => $record->mfg_date, 'expiry_date' => $record->expiry_date, 'vend_lot' => $record->vend_num, 'lot_num' => $record->lot_num]);
            //                 foreach ($po_details as $key => $value) {
            //                     $arrJsonDeEncodeparameters = @json_decode($value->json_parameters, true);

            //                     //dd($arrJsonDeEncodeparameters);

            //                     $request->merge([]);
            //                     //$arr = $request->merge([$arrJsonDeEncodeparameters]);
            //                     $request = $request->merge($arrJsonDeEncodeparameters);

            //                     $transType = 'PO Receipt';
            //                     $pomodel = new PurchaseOrderItem();
            //                     $qty = $request->qty;

            //                     $uom = $request->uom;
            //                     $request->base_uom = Item::where('item_num', $request->item_num)->pluck('uom')->first();

            //                     // Conversion for inventory and material trans
            //                     // $request = UOMService::convertRequest($request);

            //                     $convertUom = UomConv::convertUOM($request->base_uom, $uom, $request->original_uom, $qty, $item_num, '', $request->vend_num, __('mobile.nav.po_receipt'));

            //                     $request['qty_conv'] = $convertUom['conv_qty_to_base']['qty'];
            //                     $request['uom_conv'] = $convertUom['conv_qty_to_base']['uom'];

            //                     // Conversion for PO item using Qty and UOM converted to item's base UOM

            //                     if (isset($request->lpnNumList) && count($request->lpnNumList) > 0) {
            //                         // This for LPN purposes
            //                         $poqty = $po_qty;
            //                     } else {
            //                         $poqty = UOMService::convertPORequest($request);
            //                     }

            //                     $updatePOItem = POService::updatePoItemQty($request->ref_num, $request->ref_line, $request->ref_release, $poqty);




            //                     if (!$updatePOItem) {
            //                         $request->replace(['whse_num' => $request->whse_num, 'po_num' => $request->ref_num, 'po_rel' => $request->ref_release, 'po_line' => $request->ref_line, 'item_num' => $request->item_num, 'vend_do' => $request->vend_do]);
            //                         Alert::error('Error', 'Qty Receive more than Qty Ordered');
            //                         return $this->showPoProcess($request);
            //                     }

            //                     $updateItemLocation = GeneralService::updateItemLocationQty($request->whse_num, $request->loc_num, $request->item_num, $request->qty_conv, $request->lot_num, $request->uom_conv, $request->non_inv);



            //                     $insertmatl = GeneralService::newMatlTrans($transType, $request);

            //                     LotService::updateLot("PO Receipt", $request);
            //                 }
            //             }

            //         }
            //     }
            // }

        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }

        // dd(rand(1,393939));

        Alert::success('Success', __('success.processed', ['process' => __('Purchase Order Receipt')]));

        if (@$request->radiostacked1 != 'other') {



            if ($request->last_receive == "Yes" && $sap_trans_order_integration == 1) {


                $env_services = config('icapt.enable_sap_poreceive_to_drafts');
                $conn_key = "Post PO Receipt";
                $params = [$request, null];
                // if ($sap_single_bin == 1) {

                //     $result = SiteConnectionService::postIntergrationTransPO("PO Receipt", $request, null);
                // } else {

                //     if (config('icapt.enable_sap_resync')) {
                //         //dd($request);
                //         $result = SapCallService::postPOReceiveResync($request, null, $env_services);
                //     } else {

                //         if (config('icapt.enable_sap_ap_readfrom_maltrans')) {
                //             // Later Change to Maltran
                //             $result = SapCallService::postPOReceive($request, null, $env_services);
                //         } else {
                //             $result = SapCallService::postPOReceive($request, null, $env_services);
                //         }
                //     }
                // }
            }

            $tparm = new TparmView;
            $print_label = $tparm->getTparmValue('POReceipt', 'print_label');
            if (!$pomodel->checkActivePOLine($printLabelReq->ref_num)) {
                return $this->index();
            } else {
                $input['po_num'] = $request->po_num;
                $input['whse_num'] = $request->whse_num;
                $input['po_line'] = $request->po_line;
                $input['po_rel'] = $request->ref_release;
                $input['item_num'] = $request->item_num;
                $input['uom'] = $request->uom;
                $input['qty_required'] = $request->qty_required;
                $input['item_desc'] = $request->item_desc;
                $input['indicate'] = 0;
                // $input['vend_do'] = $request->vend_do;
                // return redirect()->route('showPoLineReturn', ['whse_num' => $request->whse_num, 'po_num' => $request->ref_num, 'item_num' => $request->item_num, 'vend_do' => $request->vend_do]);
                $url = generateRedirectUrl('PoReceipt', $input);
                //$cancel_href = $url;
                //return $url;


                return app('App\Http\Controllers\RouteController')->BackButton();
            }
        }

    }

    public function runGrnProcess(Request $request)
    {

        $batch_id = $request->batch_id;
        $cancelURL = $request->CancelURL;
        // ready pass inside print_input
        //dd($cancelURL,"cancle",$request);
        if ($batch_id && checkBatchIdExists($batch_id)) {
            throw ValidationException::withMessages([__('error.admin.batch_id_exists')]);
        }

        $errors = [];
        $request = validateSansentiveValue($request);
        $validateErrors = self::poReceiveValidation($request);

        if (count($validateErrors[0]) > 0 ?? []) {
            $errors = $validateErrors[0];
            return back()->withErrors($errors)->withInput();
        }
        // dd($validateErrors);
        /* move to validation function
        $po = new PurchaseOrderItem();
        $PoItemList = $po->where('po_num', $request->ref_num)->where('po_rel', $request->ref_release)->where('po_line', $request->ref_line)->where('site_id', auth()->user()->site_id)->first();

        // Verifying PO exist
        if (!$PoItemList) {
            throw ValidationException::withMessages([__('error.mobile.notexist', ['resource' => '[' . $request->ref_num . '-' . $request->ref_line . ']'])]);
        }
        // check item loc freeze
        $itemLoc = new ItemLoc();
        $itemLoc = $itemLoc->where('whse_num', $PoItemList->whse_num)->where('loc_num', $request->loc_num)->where('item_num', $PoItemList->item_num)->where('site_id', auth()->user()->site_id)->first();
        if ($itemLoc && $itemLoc->freeze == 'Y') {
            throw ValidationException::withMessages([__('error.mobile.loc_freeze', ['resource' => $PoItemList->item_num, 'resource2' => $request->loc_num])]);
        }


        // Rewrite the ReadOnly fields
        $request['ref_num'] = $PoItemList->po_num;
        $request['ref_line'] = $PoItemList->po_line;
        $request['ref_release'] = $PoItemList->po_rel;
        $request['whse_num'] = $PoItemList->whse_num;
        $request['item_num'] = $PoItemList->item_num;
        $request['base_uom'] = $PoItemList->uom;
        $request['vend_do'] = $PoItemList->vend_num;

        //dd($PoItemList,$request);

        */
        $no_of_lpn = $request->no_of_lpn;

        Session::put('no_of_lpn', $no_of_lpn);

        // if got lpn num put into array.
        $getRecordData = $request->all();
        if ($request->lpn_num != "") {
            if (isset($request->record)) {
                $getRecordData = json_decode($request->record, true);
                $request['lpnNumList'] = $getRecordData['totalLpnNum'];
            } else {
                $getRecordData = $request->all();
                $request['lpnNumList'] = [$request->lpn_num];
            }
        }

        // if($request->no_of_lpn_exist != 0){
        //     $po_date = PurchaseOrder::where('po_num', $request->ref_num)->value('due_date');

        //     for($i = 0; $i < $request->no_of_lpn; $i++){
        //         // do adding header based on the lpn definition.
        //         $checkRunningNumber = LicensePlateNumberDefinition::where('status','Y')->first();
        //         if($checkRunningNumber != ""){
        //             $maxRunningNum = str_repeat(9, $checkRunningNumber->running_number_digits); // Add 9 to get the max value of set running number. ex: running number = 2, max value will be 99.
        //             if($checkRunningNumber->last_running_number_display == $maxRunningNum){
        //                 throw ValidationException::withMessages([__('error.admin.max_lpn_num',['resource' => $maxRunningNum])]);
        //             }
        //         }
        //         $generateLPN = LPNDefinitionService::generateLPN();
        //         // Store LPN Definition last number
        //         $updateLPNDef = LPNDefinitionService::updateLPNDef($generateLPN);
        //         // Store LPN into Pallet
        //         $result = Container::create([
        //             'lpn_num' => $generateLPN,
        //             'whse_num' => $request->whse_num,
        //             'loc_num' => $request->loc_num,
        //             'single_item' => 1,
        //             'usage_restriction' => 0,
        //             'creation_date' => $po_date,
        //             'source' => 'PO Receipt',
        //             'ref_num' => $request->ref_num,
        //             'ref_line' => $request->ref_line,
        //             'status' => 'Open',
        //             'site_id' => auth()->user()->site_id,
        //             'created_by' => auth()->user()->name,
        //         ]);
        //     }


        //     // route to generate LPN page.

        // }
        // dd('qwe');
        // if($request->sohai=="")
        // {
        //     dd('here');
        // }
        //  dd($request);
        $pass_uom = $request->uom;
        // dd($pass_uom);
        $this->middleware('auth');
        $request = validateSansentiveValue($request);
        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
        $sap_single_bin = $tparm->getTparmValue('System', 'sap_single_bin');
        if ($request->lot_num == "") {
            $request->merge([
                'lot_num' => null
            ]);
        }

        if ($sap_trans_order_integration == 1) {

            $arrJsonEncodeParameters = json_encode($request->except('_token'));
            // Checking the SAME Doc only for Same Vendor
            $checkpo_details = DB::table('sap_po_batch_sync')->where('PO_type', 1)->where('doc_num', $request->document_num)->where('site_id', auth()->user()->site_id)->where('sync_status', 1)->orderBy('po_line', 'ASC')->get()->toArray();
            if (isset($checkpo_details)) {
                foreach ($checkpo_details as $key => $value) {
                    // dd($value->vend_num,$request->vend_num);
                    if ($value->vend_num != $request->vend_num) {
                        // This Doc Number exists and belongs to another Vendor. You cannot use the same Doc Number for different Vendor.
                        throw ValidationException::withMessages([__('error.mobile.vendor_diff')]);
                    }
                }
            }
        }



        // $arrJsonDeEncodeparameters = json_decode( $arrJsonEncodeParameters,true);

        // $request->merge([]);
        //$arr = $request->merge([$arrJsonDeEncodeparameters]);
        // $request = $request->merge($arrJsonDeEncodeparameters);

        //dd($arrJsonEncodeParameters,$request);
        // dd($request->all());
        DB::beginTransaction();
        try {
            $grn_num = $request->grn_num;
            $grn = GRN::where('grn_num', $request->grn_num)->where('site_id', auth()->user()->site_id)->exists();

            if ($request->receipt_type == "NewGRN") {
                $vend_name = Vendor::select('vend_name')->where('vend_num', $request->vend_num)->where('site_id', auth()->user()->site_id)->pluck('vend_name')->first();
                // create new grn
                GRN::create([
                    "grn_num" => $request->grn_num,
                    "grn_status" => "O",
                    "grn_date" => Carbon::now()->toDateTimeString(),
                    "vend_num" => $request->vend_num,
                    "vend_name" => $vend_name,
                    "vend_do" => $request->vend_do,
                    "whse_num" => $request->whse_num,
                ]);


                $order_type = 'GRN';
                $prefix = OrderNumberDefinition::select('prefix_define')->where('status', 'Y')->where('order_type', $order_type)->first();

                if ($prefix == NULL) {
                    $prefix['prefix_define'] = "";
                } else {
                    @$prefix = $prefix->toArray();
                }

                $prefix = $prefix['prefix_define'];
                // increase the order number
                $grn_num_generate = OrderNumberDefinitionService::getGenerateCodeSucess($order_type, auth()->user()->site_id, $prefix, $grn_num);

                // create new grn item
                GRNItem::create([
                    'grn_num' => $request->grn_num,
                    'grn_line' => 1,
                    'po_num' => $request->ref_num,
                    'po_line' => $request->ref_line,
                    'item_num' => $request->item_num,
                    'item_desc' => $request->item_desc,
                    // 'qty_received' => $request->qty_received,
                    'uom' => $request->base_uom,
                    "status" => 'O',
                    "site_id" => auth()->user()->site_id,
                ]);

                $request->grn_line = 1;
            }

            $grn_num = $request->grn_num;
            $grn_line = $request->grn_line;
            // $request->original_uom = $request->base_uom;
            $request->merge([
                'original_uom' => $request->base_uom,
            ]);

            $item_num = $request->item_num;
            $site_id = auth()->user()->site_id;
            $info = DB::table('middleware_connections')->where('site_id', $site_id)->get() ?? NULL;

            @$countInfo = count($info);

            // move to validation function
            // // Store in Location
            // $checkLoc = Loc::where('loc_num', $request->loc_num)->where('whse_num', $request->whse_num)->where('site_id', $site_id)->first();
            // // If not exist, store it
            // if (!$checkLoc) {
            //     $loc = new Loc;
            //     $loc->whse_num = $request->whse_num;
            //     $loc->loc_num = $request->loc_num;
            //     $loc->loc_type = "S";
            //     $loc->loc_status = 1;
            //     $loc->save();
            // } else {
            //     if ($checkLoc->loc_status == 0) {
            //         throw ValidationException::withMessages([__('error.mobile.processinactive', ['resource' => __('mobile.list.locations')])]);
            //     }
            // }




            // Store in sap_po_batch_sync table
            // if ($request->last_receive == "No") {
            Session::put('modulename', 'POReceipt');
            Session::put('grn_num', $request->grn_num);
            Session::put('whse_num', $request->whse_num);
            $transDate = Carbon::now()->toDateTimeString();
            Session::put('timestamp', $transDate);
            $transType = 'PO Receipt';
            $qty = $request->qty;
            $uom = $request->uom;
            $vend = $request->vend_num ?? $request->vend_do;

            if (@$request->non_inv == 1 && @$request->item_num == "NON-INV") {
                $getBaseUom = $request->original_uom;
            } else {
                $getBaseUom = Item::where('item_num', $request->item_num)->where('site_id', auth()->user()->site_id)->pluck('uom')->first();
            }

            $request->merge([
                'base_uom' => $getBaseUom,
                'vend_num' => $request->vend_num ?? $request->vend_do
            ]);

            // Conversion for inventory and material trans
            $convertUom = UomConv::convertUOM($request->base_uom, $uom, $request->original_uom, $qty, $item_num, '', $vend, __('mobile.nav.po_receipt'));

            $request['transtype'] = __('mobile.nav.po_receipt');
            $grn_qty = $convertUom['conv_qty_to_line']['qty'];
            $po_rel = PurchaseOrderItem::where('po_num', $request->ref_num)->where('po_line', $request->ref_line)->pluck('po_rel')->first();

            $request['qty_conv'] = $convertUom['conv_qty_to_base']['qty'];
            $request['uom_conv'] = $convertUom['conv_qty_to_base']['uom'];
            $request->merge([
                'qty_conv' => $convertUom['conv_qty_to_base']['qty'] ?? 0,
                'uom_conv' => $request->base_uom ?? 0,
                'grn_qty' => $grn_qty,
                'ref_release' => $po_rel

            ]);
            // dd($convertUom);
            // dd($request);
            // Conversion for PO item using Qty and UOM converted to item's base UOM
            // $po_qty = UOMService::convertPORequest($request);


            if ($sap_trans_order_integration != 1) {

                if ($request->item_num != "NON-INV") {
                    $updateItemLocation = GeneralService::updateItemLocationQty($request->whse_num, $request->loc_num, $request->item_num, $request->qty_conv, $request->lot_num, $request->uom_conv, $request->non_inv);
                }

                //dd($request);
                $insertmatl = GeneralService::newMatlTrans($transType, $request);
            }

            if ($sap_trans_order_integration != 1) {
                $record = new Request($getRecordData);
                //dd($record,$request);
                $updateGRNItem = POService::updateGrnItemQty($request);
                // $updatePOItem = POService::updatePoItemQty($request->ref_num, $request->ref_line, $po_rel, $grn_qty);
                // $request->replace([
                //     'whse_num' => $record->whse_num,
                //     'ref_num' => $record->ref_num,
                //     'ref_release' => $record->ref_release,
                //     'ref_line' => $record->ref_line,
                //     'item_num' => $record->item_num,
                //     'mfg_date' => $record->mfg_date,
                //     'expiry_date' => $record->expiry_date,
                //     'vend_lot' => $record->vend_num,
                //     'lot_num' => $record->lot_num,
                //     'qty' => $request->qty,
                // ]);
                // dd($request->qty);
                if (!$updateGRNItem) {
                    $request->replace(['whse_num' => $request->whse_num, 'grn_num' => $request->grn_num, 'grn_line' => $request->grn_line, 'item_num' => $request->item_num, 'vend_do' => $request->vend_do]);
                    Alert::error('Error', 'Qty to receive is more than Qty required');
                    return $this->showPoProcess($request);
                }
                //  dd( $request->uom);
                // if($request->lpn_num != ""){
                //     $toArr = [
                //         'whse_num' => $request->whse_num,
                //         'loc_num' => $request->loc_num,
                //         'lot_num' => $request->lot_num,
                //         'item_num' => $request->item_num,
                //         'qty_conv' => $request->qty_conv,
                //         'uom_conv' => $request->uom_conv,
                //     ];

                //     $fromArr = [];

                //     $updateItemLocation = PalletService::updateItemLocLotQty($fromArr, $toArr, $request->non_inv, 'To PO Receipt');

                //     $insertPalletData = PalletService::updatePalletItemQty($request);
                // }
                // else{
                //     $updateItemLocation = GeneralService::updateItemLocationQty($request->whse_num, $request->loc_num, $request->item_num, $request->qty_conv, $request->lot_num, $request->uom_conv, $request->non_inv);
                // }

                // $line = ContainerItem::select('lpn_line')->where('lpn_num', $request->lpn_num)->where('site_id',auth()->user()->site_id)->where('created_by',auth()->user()->name)->orderByRaw('CAST(lpn_line as UNSIGNED) DESC')->first();
                // $request->merge([
                //     'lpn_line' => $line->lpn_line
                // ]);

                // $insertmatl = GeneralService::newMatlTrans($transType, $request);
                //dd("zzz",$request);
                $lotServ =  LotService::updateLot("PO Receipt", $request);
            }

            // update preassign lots
            $uom_conv = UomConv::convert($request->uom, $request->qty, $request->item_num, null, null, null);
            PreassignLotsService::updatePreassignLot('po', $request->ref_num, $request->ref_line, $request->item_num, $request->lot_num, auth()->user()->site_id, $uom_conv['qty']);


            DB::commit();


            $req_rioginal_uom = $pass_uom;
            // dd($request->uom);
            $printLabelReq = $request;
            if ($printLabelReq->lot_num != null) {
                $check_expiry_date = LotService::getExpiryDate($printLabelReq);

                // Generate barcode
                // changed $request->qty to $qty because $request->qty was empty
                $print_input = app('App\Http\Controllers\BarcodeController')->GetPOReceiveLabelData($printLabelReq->ref_num, $printLabelReq->ref_line, $request->qty_conv, $printLabelReq->whse_num, null, 'GRNReceipt', $printLabelReq->loc_num, $printLabelReq->lot_num, $check_expiry_date, $printLabelReq->vend_lot, $printLabelReq->uom_conv);
            } else {
                // changed $request->qty to $qty because $request->qty was empty

                // Generate barcode
                $print_input = app('App\Http\Controllers\BarcodeController')->GetPOReceiveLabelData($printLabelReq->ref_num, $printLabelReq->ref_line, $request->qty_conv, $printLabelReq->whse_num, null, 'GRNReceipt', $printLabelReq->loc_num, $printLabelReq->lot_num, null, $printLabelReq->vend_lot, $printLabelReq->uom_conv);
            }
            // if ($sap_trans_order_integration == 1) {
            //     $baseuom = Item::where('item_num', $item_num)->where('site_id', auth()->user()->site_id)->value('uom');
            //     // $selectuom = '', $lineuom = ''
            //     $convertUom = UomConv::convertUOM($baseuom, $request->uom, $request->original_uom, $request->qty, $item_num, '', '', __('mobile.nav.po_receive'));

            //     $qtyafterconvert = $convertUom['conv_qty_to_line']['qty'];
            //     DB::table('sap_po_batch_sync')->insert([
            //         'po_num' => $po_num,
            //         'po_line' => $po_line,
            //         'po_rel' => $po_rel ?? 0,
            //         'erp_ID' => PurchaseOrderItem::select('erp_ID')->where('po_num', $po_num)
            //             ->where('po_line', $po_line)
            //             //->where('po_rel', $po_rel)
            //             ->where('site_id', auth()->user()->site_id)
            //             ->value('erp_ID'),
            //         'whse_num' => $request->whse_num,
            //         'po_type' => 1,
            //         'loc_num' => $request->loc_num,
            //         'lot_num' => $request->lot_num,
            //         'expiry_date' => $request->expiry_date,
            //         'mfg_date' => $request->mfg_date,
            //         'item_num' => $item_num,
            //         'vend_do' => $request->vend_do,
            //         'vend_lot' => $request->vend_lot,
            //         'vend_num' => $request->vend_num,
            //         'qty_received' => $qtyafterconvert,
            //         'qty_received_uom' => $request->uom,
            //         'qty_required' => $request->qty_required,
            //         'qty_required_uom' => $request->original_uom,
            //         'qty_conv' => $request->qty_conv,
            //         'doc_num' => $request->document_num,
            //         'sync_status' => 1,
            //         // "sync_initiated" => $request->last_receive == "Yes" ? 1 : 0,
            //         'site_id' => auth()->user()->site_id,
            //         'created_by' => auth()->user()->name,
            //         'modified_by' => auth()->user()->name,
            //         'created_date' => now(),
            //         'modified_date' => now(),
            //         'json_parameters' => $arrJsonEncodeParameters
            //     ]);


            //     if ($request->last_receive == "Yes") {
            //         if (@$request->document_num != "" || @$request->document_num != null) {
            //             // Query all relate PO based on the document no.

            //             $po_details = DB::table('sap_po_batch_sync')->where('PO_type', 1)->where('vend_num', $request->vend_num)->where('doc_num', $request->document_num)->where('site_id', auth()->user()->site_id)->whereIn('sync_status', [1, 2])->orderBy('po_line', 'ASC')->get()->toArray();

            //             if (isset($po_details)) {
            //                 //dd($request->qty);
            //                 $record = new Request($getRecordData);
            //                 $request->replace(['whse_num' => $record->whse_num, 'ref_num' => $record->ref_num, 'ref_release' => $record->ref_release, 'ref_line' => $record->ref_line, 'item_num' => $record->item_num, 'mfg_date' => $record->mfg_date, 'expiry_date' => $record->expiry_date, 'vend_lot' => $record->vend_num, 'lot_num' => $record->lot_num]);
            //                 foreach ($po_details as $key => $value) {
            //                     $arrJsonDeEncodeparameters = @json_decode($value->json_parameters, true);

            //                     //dd($arrJsonDeEncodeparameters);

            //                     $request->merge([]);
            //                     //$arr = $request->merge([$arrJsonDeEncodeparameters]);
            //                     $request = $request->merge($arrJsonDeEncodeparameters);

            //                     $transType = 'PO Receipt';
            $pomodel = new PurchaseOrderItem();
            //                     $qty = $request->qty;

            //                     $uom = $request->uom;
            //                     $request->base_uom = Item::where('item_num', $request->item_num)->pluck('uom')->first();

            //                     // Conversion for inventory and material trans
            //                     // $request = UOMService::convertRequest($request);

            //                     $convertUom = UomConv::convertUOM($request->base_uom, $uom, $request->original_uom, $qty, $item_num, '', $request->vend_num, __('mobile.nav.po_receipt'));

            //                     $request['qty_conv'] = $convertUom['conv_qty_to_base']['qty'];
            //                     $request['uom_conv'] = $convertUom['conv_qty_to_base']['uom'];

            //                     // Conversion for PO item using Qty and UOM converted to item's base UOM

            //                     if (isset($request->lpnNumList) && count($request->lpnNumList) > 0) {
            //                         // This for LPN purposes
            //                         $poqty = $po_qty;
            //                     } else {
            //                         $poqty = UOMService::convertPORequest($request);
            //                     }

            //                     $updatePOItem = POService::updatePoItemQty($request->ref_num, $request->ref_line, $request->ref_release, $poqty);
            //                     $updateGRNItem = POService::updateGrnItemQty($request->grn_num, $request->grn_line, $poqty);

            //                     if (!$updatePOItem) {
            //                         $request->replace(['whse_num' => $request->whse_num, 'po_num' => $request->ref_num, 'po_rel' => $request->ref_release, 'po_line' => $request->ref_line, 'item_num' => $request->item_num, 'vend_do' => $request->vend_do]);
            //                         Alert::error('Error', 'Qty Receive more than Qty Ordered');
            //                         return $this->showPoProcess($request);
            //                     }

            //                     $updateItemLocation = GeneralService::updateItemLocationQty($request->whse_num, $request->loc_num, $request->item_num, $request->qty_conv, $request->lot_num, $request->uom_conv, $request->non_inv);



            //                     $insertmatl = GeneralService::newMatlTrans($transType, $request);

            //                     LotService::updateLot("PO Receipt", $request);
            //                 }
            //             }

            //             $env_services = config('icapt.enable_sap_poreceive_to_drafts');
            //             $conn_key = "Post PO Receipt";
            //             $params = [$request, null];
            //             if ($sap_single_bin == 1) {

            //                 $result = SiteConnectionService::postIntergrationTransPO("PO Receipt", $request, null);
            //             } else {
            //                 if (config('icapt.enable_sap_ap_readfrom_maltrans')) {
            //                     // Later Change to Maltran
            //                     $result = SapCallService::postPOReceive($request, null, $env_services);
            //                 } else {
            //                     $result = SapCallService::postPOReceive($request, null, $env_services);
            //                 }
            //             }
            //         }
            //     }
            // }

            // dd(rand(1,393939));

            if (@$request->radiostacked1 != 'other') {



                // if ($sap_trans_order_integration == 1){
                //     if (@$result != 200) {
                //         Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result)->persistent('Dismiss');
                //         //return redirect()->back();
                //     }else{
                //     Alert::success('Success', __('success.processed', ['process' => __('Purchase Order Receipt')]));


                //     if ($request->lpn_num != null) {
                //         Alert::success('Success', __('success.added', ['resource' => __('Pallet(s)')]));
                //         }
                //     }
                // }
                // else
                // {

                Alert::success('Success', __('success.processed', ['process' => __('GRN Receipt')]));


                if ($request->lpn_num != null) {
                    Alert::success('Success', __('success.added', ['resource' => __('Pallet(s)')]));
                }


                // }
                $tparm = new TparmView;
                $print_label = $tparm->getTparmValue('POReceipt', 'print_label');

                $print_input['CancelURL'] = $request->CancelURL;
                $print_input['receipt_type'] = $request->receipt_type;



                if (!$pomodel->checkActivePOLine($printLabelReq->ref_num)) {
                    if ($print_label == 1) {
                        $print_input['frm_list'] = 1;
                        return BarcodeController::showLabelDefinition($print_input);
                    } else {
                        return $this->index();
                    }
                } else {

                    $print_input['frm_list'] = 2;
                    // dd($no_of_lpn);


                    $print_input['disable_qty'] = false;
                    if ($print_label == 1) {
                        return BarcodeController::showLabelDefinition($print_input);
                    } else {

                        $input['po_num'] = $request->ref_num;
                        $input['whse_num'] = $request->whse_num;
                        $input['po_line'] = $request->ref_line;
                        $input['po_rel'] = $request->ref_release;
                        $input['item_num'] = $request->item_num;
                        $input['uom'] = $request->uom;
                        $input['qty_required'] = $request->net_received;
                        $input['item_desc'] = $request->item_desc;
                        $input['indicate'] = 0;
                        // $input['vend_do'] = $request->vend_do;
                        // return redirect()->route('showPoLineReturn', ['whse_num' => $request->whse_num, 'po_num' => $request->ref_num, 'item_num' => $request->item_num, 'vend_do' => $request->vend_do]);
                        $url = generateRedirectUrl('PoReceipt', $input);
                        //$cancel_href = $url;
                        //return $url;


                        return app('App\Http\Controllers\RouteController')->BackButton();
                    }

                    // return BarcodeController::showLabelDefinition($print_input);
                }
            }
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    public function runGrnCWProcess(Request $request)
    {

        $batch_id = $request->batch_id;
        $cancelURL = $request->CancelURL;

        if ($batch_id && checkBatchIdExists($batch_id)) {
            throw ValidationException::withMessages([__('error.admin.batch_id_exists')]);
        }

        // Validate PO
        $grn_num = $request->grn_num;
        $grn_line = $request->grn_line;
        if (empty($grn_num) || empty($grn_line))
        {
            throw ValidationException::withMessages([ __('error.mobile.does_not_exists', ['resource' => __('admin.menu.grn') ]) ]);
        }

        $grn_item = GRNItem::where('grn_num', $grn_num)->where('grn_line', $grn_line)->where('site_id', auth()->user()->site_id)->first();
        if (!$grn_item)
        {
            throw ValidationException::withMessages([ __('error.mobile.does_not_exists', ['resource' => __('admin.menu.grn') ]) ]);
        }

        // Get Tolerance and UOM
        $tolerance = $grn_item->qty_shipped - $grn_item->net_received;
        $tolerance_uom = $grn_item->uom;

        // Validate Catch Weight Data
        CatchWeightService::validateCatchWeightData($request, $tolerance, $tolerance_uom);


        // if got lpn num put into array.
        $getRecordData = $request->all();

        $pass_uom = $tolerance_uom;
        // dd($pass_uom);
        $this->middleware('auth');
        $request = validateSansentiveValue($request);
        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
        $sap_single_bin = $tparm->getTparmValue('System', 'sap_single_bin');
        if ($request->lot_num == "") {
            $request->merge([
                'lot_num' => null
            ]);
        }

        if ($sap_trans_order_integration == 1) {

            $arrJsonEncodeParameters = json_encode($request->except('_token'));
            // Checking the SAME Doc only for Same Vendor
            $checkpo_details = DB::table('sap_po_batch_sync')->where('PO_type', 1)->where('doc_num', $request->document_num)->where('site_id', auth()->user()->site_id)->where('sync_status', 1)->orderBy('po_line', 'ASC')->get()->toArray();
            if (isset($checkpo_details)) {
                foreach ($checkpo_details as $key => $value) {
                    // dd($value->vend_num,$request->vend_num);
                    if ($value->vend_num != $request->vend_num) {
                        // This Doc Number exists and belongs to another Vendor. You cannot use the same Doc Number for different Vendor.
                        throw ValidationException::withMessages([__('error.mobile.vendor_diff')]);
                    }
                }
            }
        }

        DB::beginTransaction();
        try {
            $grn_num = $request->grn_num;
            $grn_line = $request->grn_line;
            $grn = GRN::where('grn_num', $grn_num)->where('site_id', auth()->user()->site_id)->exists();

            $request->merge([
                'original_uom' => $tolerance_uom,
            ]);

            $item_num = $request->item_num;
            $site_id = auth()->user()->site_id;
            $info = DB::table('middleware_connections')->where('site_id', $site_id)->get() ?? NULL;

            @$countInfo = count($info);


            // Store in sap_po_batch_sync table
            // if ($request->last_receive == "No") {
            Session::put('modulename', 'POReceipt');
            Session::put('grn_num', $request->grn_num);
            Session::put('whse_num', $request->whse_num);
            Session::put('print_status', 'No');
            $transDate = Carbon::now()->toDateTimeString();
            Session::put('timestamp', $transDate);
            $transType = 'PO Receipt';
            $qty = array_sum($request->arr_qty ?? []);
            $uom = $tolerance_uom;

            $vend = $request->vend_num ?? $request->vend_do;
            $getBaseUom = Item::where('item_num', $request->item_num)->where('site_id', auth()->user()->site_id)->pluck('uom')->first();

            $request->merge([
                'base_uom' => $getBaseUom,
                'vend_num' => $request->vend_num ?? $request->vend_do,
                'trans_type' => $transType,
                'transtype' => $transType,
            ]);

            // Conversion for inventory and material trans
            $convertUom = UomConv::convertUOM($request->base_uom, $uom, $request->original_uom, $qty, $item_num, '', $vend, __('mobile.nav.po_receipt'));

            $request['transtype'] = __('mobile.nav.po_receipt');
            $grn_qty = $convertUom['conv_qty_to_line']['qty'];

            $request['qty_conv'] = $convertUom['conv_qty_to_base']['qty'];
            $request['uom_conv'] = $convertUom['conv_qty_to_base']['uom'];
            $request->merge([
                'qty_conv' => $convertUom['conv_qty_to_base']['qty'] ?? 0,
                'uom_conv' => $request->base_uom ?? 0,
                'grn_qty' => $grn_qty

            ]);


            if ($sap_trans_order_integration != 1) {

                $updateItemLocation = CatchWeightService::updateItemLocLotNMatlTrans($request, $tolerance_uom, $transType, "PO Receipt");
                $record = new Request($getRecordData);

                $updateGRNItem = POService::updateGrnItemQty($request);
                if (!$updateGRNItem) {
                    $request->replace(['whse_num' => $request->whse_num, 'grn_num' => $request->grn_num, 'grn_line' => $request->grn_line, 'item_num' => $request->item_num, 'vend_do' => $request->vend_do]);
                    Alert::error('Error', 'Qty to receive is more than Qty required');
                    return $this->showPoProcess($request);
                }

            }


            DB::commit();


            $req_rioginal_uom = $pass_uom;
            $printLabelReq = $request;
            if ($printLabelReq->lot_num != null) {
                $check_expiry_date = LotService::getExpiryDate($printLabelReq);

                // Generate barcode
                // changed $request->qty to $qty because $request->qty was empty
                $print_input = app('App\Http\Controllers\BarcodeController')->GetPOReceiveLabelData($printLabelReq->ref_num, $printLabelReq->ref_line, $request->qty_conv, $printLabelReq->whse_num, null, 'GRNReceipt', $printLabelReq->loc_num, $printLabelReq->lot_num, $check_expiry_date, $printLabelReq->vend_lot, $printLabelReq->uom_conv);
            } else {
                // changed $request->qty to $qty because $request->qty was empty

                // Generate barcode
                $print_input = app('App\Http\Controllers\BarcodeController')->GetPOReceiveLabelData($printLabelReq->ref_num, $printLabelReq->ref_line, $request->qty_conv, $printLabelReq->whse_num, null, 'GRNReceipt', $printLabelReq->loc_num, $printLabelReq->lot_num, null, $printLabelReq->vend_lot, $printLabelReq->uom_conv);
            }
            // if ($sap_trans_order_integration == 1) {
            //     $baseuom = Item::where('item_num', $item_num)->where('site_id', auth()->user()->site_id)->value('uom');
            //     // $selectuom = '', $lineuom = ''
            //     $convertUom = UomConv::convertUOM($baseuom, $request->uom, $request->original_uom, $request->qty, $item_num, '', '', __('mobile.nav.po_receive'));

            //     $qtyafterconvert = $convertUom['conv_qty_to_line']['qty'];
            //     DB::table('sap_po_batch_sync')->insert([
            //         'po_num' => $po_num,
            //         'po_line' => $po_line,
            //         'po_rel' => $po_rel ?? 0,
            //         'erp_ID' => PurchaseOrderItem::select('erp_ID')->where('po_num', $po_num)
            //             ->where('po_line', $po_line)
            //             //->where('po_rel', $po_rel)
            //             ->where('site_id', auth()->user()->site_id)
            //             ->value('erp_ID'),
            //         'whse_num' => $request->whse_num,
            //         'po_type' => 1,
            //         'loc_num' => $request->loc_num,
            //         'lot_num' => $request->lot_num,
            //         'expiry_date' => $request->expiry_date,
            //         'mfg_date' => $request->mfg_date,
            //         'item_num' => $item_num,
            //         'vend_do' => $request->vend_do,
            //         'vend_lot' => $request->vend_lot,
            //         'vend_num' => $request->vend_num,
            //         'qty_received' => $qtyafterconvert,
            //         'qty_received_uom' => $request->uom,
            //         'qty_required' => $request->qty_required,
            //         'qty_required_uom' => $request->original_uom,
            //         'qty_conv' => $request->qty_conv,
            //         'doc_num' => $request->document_num,
            //         'sync_status' => 1,
            //         // "sync_initiated" => $request->last_receive == "Yes" ? 1 : 0,
            //         'site_id' => auth()->user()->site_id,
            //         'created_by' => auth()->user()->name,
            //         'modified_by' => auth()->user()->name,
            //         'created_date' => now(),
            //         'modified_date' => now(),
            //         'json_parameters' => $arrJsonEncodeParameters
            //     ]);


            //     if ($request->last_receive == "Yes") {
            //         if (@$request->document_num != "" || @$request->document_num != null) {
            //             // Query all relate PO based on the document no.

            //             $po_details = DB::table('sap_po_batch_sync')->where('PO_type', 1)->where('vend_num', $request->vend_num)->where('doc_num', $request->document_num)->where('site_id', auth()->user()->site_id)->whereIn('sync_status', [1, 2])->orderBy('po_line', 'ASC')->get()->toArray();

            //             if (isset($po_details)) {
            //                 //dd($request->qty);
            //                 $record = new Request($getRecordData);
            //                 $request->replace(['whse_num' => $record->whse_num, 'ref_num' => $record->ref_num, 'ref_release' => $record->ref_release, 'ref_line' => $record->ref_line, 'item_num' => $record->item_num, 'mfg_date' => $record->mfg_date, 'expiry_date' => $record->expiry_date, 'vend_lot' => $record->vend_num, 'lot_num' => $record->lot_num]);
            //                 foreach ($po_details as $key => $value) {
            //                     $arrJsonDeEncodeparameters = @json_decode($value->json_parameters, true);

            //                     //dd($arrJsonDeEncodeparameters);

            //                     $request->merge([]);
            //                     //$arr = $request->merge([$arrJsonDeEncodeparameters]);
            //                     $request = $request->merge($arrJsonDeEncodeparameters);

            //                     $transType = 'PO Receipt';
            $pomodel = new PurchaseOrderItem();
            //                     $qty = $request->qty;

            //                     $uom = $request->uom;
            //                     $request->base_uom = Item::where('item_num', $request->item_num)->pluck('uom')->first();

            //                     // Conversion for inventory and material trans
            //                     // $request = UOMService::convertRequest($request);

            //                     $convertUom = UomConv::convertUOM($request->base_uom, $uom, $request->original_uom, $qty, $item_num, '', $request->vend_num, __('mobile.nav.po_receipt'));

            //                     $request['qty_conv'] = $convertUom['conv_qty_to_base']['qty'];
            //                     $request['uom_conv'] = $convertUom['conv_qty_to_base']['uom'];

            //                     // Conversion for PO item using Qty and UOM converted to item's base UOM

            //                     if (isset($request->lpnNumList) && count($request->lpnNumList) > 0) {
            //                         // This for LPN purposes
            //                         $poqty = $po_qty;
            //                     } else {
            //                         $poqty = UOMService::convertPORequest($request);
            //                     }

            //                     $updatePOItem = POService::updatePoItemQty($request->ref_num, $request->ref_line, $request->ref_release, $poqty);
            //                     $updateGRNItem = POService::updateGrnItemQty($request->grn_num, $request->grn_line, $poqty);

            //                     if (!$updatePOItem) {
            //                         $request->replace(['whse_num' => $request->whse_num, 'po_num' => $request->ref_num, 'po_rel' => $request->ref_release, 'po_line' => $request->ref_line, 'item_num' => $request->item_num, 'vend_do' => $request->vend_do]);
            //                         Alert::error('Error', 'Qty Receive more than Qty Ordered');
            //                         return $this->showPoProcess($request);
            //                     }

            //                     $updateItemLocation = GeneralService::updateItemLocationQty($request->whse_num, $request->loc_num, $request->item_num, $request->qty_conv, $request->lot_num, $request->uom_conv, $request->non_inv);



            //                     $insertmatl = GeneralService::newMatlTrans($transType, $request);

            //                     LotService::updateLot("PO Receipt", $request);
            //                 }
            //             }

            //             $env_services = config('icapt.enable_sap_poreceive_to_drafts');
            //             $conn_key = "Post PO Receipt";
            //             $params = [$request, null];
            //             if ($sap_single_bin == 1) {

            //                 $result = SiteConnectionService::postIntergrationTransPO("PO Receipt", $request, null);
            //             } else {
            //                 if (config('icapt.enable_sap_ap_readfrom_maltrans')) {
            //                     // Later Change to Maltran
            //                     $result = SapCallService::postPOReceive($request, null, $env_services);
            //                 } else {
            //                     $result = SapCallService::postPOReceive($request, null, $env_services);
            //                 }
            //             }
            //         }
            //     }
            // }

            // dd(rand(1,393939));

            Alert::success('Success', __('success.processed', ['process' => __('GRN Receipt')]));

            if (@$request->radiostacked1 != 'other') {



                // if ($sap_trans_order_integration == 1){
                //     if (@$result != 200) {
                //         Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result)->persistent('Dismiss');
                //         //return redirect()->back();
                //     }else{
                //     Alert::success('Success', __('success.processed', ['process' => __('Purchase Order Receipt')]));


                //     if ($request->lpn_num != null) {
                //         Alert::success('Success', __('success.added', ['resource' => __('Pallet(s)')]));
                //         }
                //     }
                // }
                // else
                // {


                if ($request->lpn_num != null) {
                    Alert::success('Success', __('success.added', ['resource' => __('Pallet(s)')]));
                }


                // }
                $tparm = new TparmView;
                $print_label = $tparm->getTparmValue('POReceipt', 'print_label');

                $print_input['CancelURL'] = $request->CancelURL;
                $print_input['receipt_type'] = $request->receipt_type;



                if (!$pomodel->checkActivePOLine($printLabelReq->ref_num)) {
                    return $this->index();
                } else {

                    $print_input['frm_list'] = 2;
                    // dd($no_of_lpn);


                    $print_input['disable_qty'] = false;
                    $input['po_num'] = $request->ref_num;
                    $input['whse_num'] = $request->whse_num;
                    $input['po_line'] = $request->ref_line;
                    $input['po_rel'] = $request->ref_release;
                    $input['item_num'] = $request->item_num;
                    $input['uom'] = $request->uom;
                    $input['qty_required'] = $request->net_received;
                    $input['item_desc'] = $request->item_desc;
                    $input['indicate'] = 0;
                    // $input['vend_do'] = $request->vend_do;
                    // return redirect()->route('showPoLineReturn', ['whse_num' => $request->whse_num, 'po_num' => $request->ref_num, 'item_num' => $request->item_num, 'vend_do' => $request->vend_do]);
                    $url = generateRedirectUrl('PoReceipt', $input);
                    //$cancel_href = $url;
                    //return $url;


                    return app('App\Http\Controllers\RouteController')->BackButton();

                    // return BarcodeController::showLabelDefinition($print_input);
                }
            }
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    public function runPoProcess_old(Request $request)
    {
        $this->middleware('auth');
        $request = validateSansentiveValue($request);
        DB::beginTransaction();
        try {
            $po_num = $request->ref_num;
            $po_line = $request->ref_line;
            $po_rel = $request->ref_release;

            $item_num = $request->item_num;
            $po = new PurchaseOrderItem();
            $PoItemList = $po->where('po_num', $po_num)->where('po_rel', $po_rel)->where('po_line', $po_line)->get();

            $site_id = auth()->user()->site_id;
            $info = DB::table('middleware_connections')->where('site_id', $site_id)->get() ?? NULL;

            @$countInfo = count($info);
            $tparm = new TparmView;
            $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

            // Store in Location
            $checkLoc = Loc::where('loc_num', $request->loc_num)->where('whse_num', $request->whse_num)->first();
            // If not exist, store it
            if (!$checkLoc) {
                $loc = new Loc;
                $loc->whse_num = $request->whse_num;
                $loc->loc_num = $request->loc_num;
                $loc->loc_type = "S";
                $loc->loc_status = 1;
                $loc->save();
            } else {
                if ($checkLoc->loc_status == 0) {
                    throw ValidationException::withMessages([__('error.mobile.processinactive', ['resource' => __('mobile.list.locations')])]);
                }
            }
            $tparm = new TparmView;
            $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

            // Store in sap_po_batch_sync table
            //            if ($request->last_receive == "No") {
            if ($sap_trans_order_integration == 1) {
            }
            Session::put('modulename', 'POReceipt');
            Session::put('po_num', $request->ref_num);
            Session::put('whse_num', $request->whse_num);
            $transDate = Carbon::now()->toDateTimeString();
            Session::put('timestamp', $transDate);
            $transType = 'PO Receipt';
            $pomodel = new PurchaseOrderItem();
            $qty = $request->qty;
            $uom = $request->uom;
            $request->base_uom = Item::where('item_num', $request->item_num)->pluck('uom')->first();

            // Conversion for inventory and material trans
            $request = UOMService::convertRequest($request);
            // Conversion for PO item using Qty and UOM converted to item's base UOM
            $po_qty = UOMService::convertPORequest($request);
            $updatePOItem = POService::updatePoItemQty($request->ref_num, $request->ref_line, $request->ref_release, $po_qty);
            if (!$updatePOItem) {
                $request->replace(['whse_num' => $request->whse_num, 'po_num' => $request->ref_num, 'po_rel' => $request->ref_release, 'po_line' => $request->ref_line, 'item_num' => $request->item_num, 'vend_do' => $request->vend_do]);
                Alert::error('Error', 'Qty Receive more than Qty Ordered');
                return $this->showPoProcess($request);
            }

            $updateItemLocation = GeneralService::updateItemLocationQty($request->whse_num, $request->loc_num, $request->item_num, $request->qty_conv, $request->lot_num, $request->uom_conv, $request->non_inv);
            $insertmatl = GeneralService::newMatlTrans($transType, $request);

            LotService::updateLot("PO Receipt", $request);

            DB::commit();
            $tparm = new TparmView;
            $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
            $sap_single_bin = $tparm->getTparmValue('System', 'sap_single_bin');
            // $request->merge(['qty_conv'=>$po_qty]);
            //dd($request);


            if ($sap_trans_order_integration == 1) {
                $env_services = config('icapt.enable_sap_poreceive_to_drafts');

                if ($request->last_receive == "Yes") {
                    if (@$request->document_num != "" || @$request->document_num != null) {
                        DB::table('sap_po_batch_sync')->insert([
                            'po_num' => $po_num,
                            'po_line' => $po_line,
                            'po_rel' => $po_rel ?? 0,
                            'erp_ID' => PurchaseOrderItem::select('erp_ID')->where('po_num', $po_num)
                                ->where('po_line', $po_line)
                                //->where('po_rel', $po_rel)
                                ->where('site_id', auth()->user()->site_id)
                                ->value('erp_ID'),
                            'whse_num' => $request->whse_num,
                            'po_type' => 1,
                            'loc_num' => $request->loc_num,
                            'lot_num' => $request->lot_num,
                            'expiry_date' => $request->expiry_date,
                            'mfg_date' => $request->mfg_date,
                            'item_num' => $item_num,
                            'vend_do' => $request->vend_do,
                            'vend_lot' => $request->vend_lot,
                            'vend_num' => $request->vend_num,
                            'qty_received' => $request->qty,
                            'qty_received_uom' => $request->uom,
                            'qty_conv' => $request->qty_conv,
                            'doc_num' => $request->document_num,
                            'sync_status' => 1,
                            //                    "sync_initiated" => $request->last_receive == "Yes" ? 1 : 0,
                            'site_id' => auth()->user()->site_id,
                            'created_by' => auth()->user()->name,
                            'modified_by' => auth()->user()->name,
                            'created_date' => now(),
                            'modified_date' => now(),
                        ]);
                    }

                    $conn_key = "Post PO Receipt";
                    $params = [$request, null];
                    //$result = SapCallService::postPOReceive($request, null,$env_services );
                    if ($sap_single_bin == 1) {
                        $result = SiteConnectionService::postIntergrationTransPO("PO Receipt", $request, null);
                    } else {
                        if (config('icapt.enable_sap_ap_readfrom_maltrans')) {
                            // Later Change to read from Matltrans
                            $result = SapCallService::postPOReceive($request, null, $env_services);
                        } else {
                            $result = SapCallService::postPOReceive($request, null, $env_services);
                        }
                    }
                    if ($result != 200) {
                        Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result)->persistent('Dismiss');
                    }
                } else {
                    DB::table('sap_po_batch_sync')->insert([
                        'po_num' => $po_num,
                        'po_line' => $po_line,
                        'po_rel' => $po_rel ?? 0,
                        'erp_ID' => PurchaseOrderItem::select('erp_ID')->where('po_num', $po_num)
                            ->where('po_line', $po_line)
                            //->where('po_rel', $po_rel)
                            ->where('site_id', auth()->user()->site_id)
                            ->value('erp_ID'),
                        'whse_num' => $request->whse_num,
                        'po_type' => 1,
                        'loc_num' => $request->loc_num,
                        'lot_num' => $request->lot_num,
                        'expiry_date' => $request->expiry_date,
                        'mfg_date' => $request->mfg_date,
                        'item_num' => $item_num,
                        'vend_do' => $request->vend_do,
                        'vend_lot' => $request->vend_lot,
                        'vend_num' => $request->vend_num,
                        'qty_received' => $request->qty,
                        'qty_received_uom' => $request->uom,
                        'qty_conv' => $request->qty_conv,
                        'doc_num' => $request->document_num,
                        'sync_status' => 1,
                        //                    "sync_initiated" => $request->last_receive == "Yes" ? 1 : 0,
                        'site_id' => auth()->user()->site_id,
                        'created_by' => auth()->user()->name,
                        'modified_by' => auth()->user()->name,
                        'created_date' => now(),
                        'modified_date' => now(),
                    ]);
                }
            }

            if ($request->radiostacked1 != 'other') {

                Alert::success('Success', __('success.processed', ['process' => __('Purchase Order Receipt')]));

                if (!$pomodel->checkActivePOLine($request->ref_num)) {
                    return $this->index();
                } else {

                    if ($request->lot_num != null) {
                        $check_expiry_date = LotService::getExpiryDate($request);

                        // Generate barcode
                        $input = app('App\Http\Controllers\BarcodeController')->GetPOReceiveLabelData($request->ref_num, $request->ref_line, $request->qty, $request->whse_num, null, 'PoReceipt', $request->loc_num, $request->lot_num, $check_expiry_date, $request->vend_lot, $request->uom);
                    } else {
                        // Generate barcode
                        $input = app('App\Http\Controllers\BarcodeController')->GetPOReceiveLabelData($request->ref_num, $request->ref_line, $request->qty, $request->whse_num, null, 'PoReceipt', $request->loc_num, $request->lot_num, null, $request->vend_lot, $request->uom);
                    }

                    $tparm = new TparmView;
                    $print_label = $tparm->getTparmValue('POReceipt', 'print_label');

                    if ($print_label == 1) {
                        return BarcodeController::showLabelDefinition($input);
                    } else {
                        return app('App\Http\Controllers\RouteController')->BackButton();
                    }
                    return BarcodeController::showLabelDefinition($input);
                }
            }
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    public function POvalidation(Request $request)
    {
        $vinput = $request->all();
        foreach ($vinput as $name => $value) {
            if ($name == "whse_num") {
                $model = new Warehouse();
                $result = $model->exists($value);
            }
            if ($name == "loc_num") {
                $model = new Loc();
                $result = $model->exists($value);
            }
            if ($name == "item_num") {
                $model = new PurchaseOrderItem();
                $result = $model->itemExists($value);
            }
            if ($name == "po_num" || $name == "ponum") {
                $model = new PurchaseOrderItem();
                if ($request->whse_num != "") {
                    $result = $model->where('whse_num', $request->whse_num)->where('po_num', $value)->first();
                } else {
                    $result = $model->exists($value);
                }
            }
            if ($name == "vend_do") {
                $model = new PurchaseOrder();
                $result = $model->vendorDOexists($value);
            }
            if ($name == "vend_num") {
                $model = new PurchaseOrderItem();
                $resultObj = $model->where('vend_num', $value)->first();
                $result = $resultObj ? true : false;
            }
            if ($name == "vend_num1") {
                $model = new PurchaseOrderItem();
                $resultObj = $model->where('vend_num', $value)->first();
                $result = $resultObj ? true : false;
            }
            if ($name == "lpn_num") {
                $model = new Container();
                $result = $model->exists($value);
            }

            if ($result == true) {
                return "true";
            } else {
                return "false";
            }
        }
    }

    public function POLineOpenvalidation(Request $request)
    {
        $po_num = $request->po_num;
        $model = new PurchaseOrderItem();

        $result = $model->where('po_num', $po_num);

        if ($result->exists()) {
            $result = $result->first();

            if ($result->rel_status != 'O') {
                return "inactive";
            }
            return "true";
        } else {
            return "false";
        }
    }

    public function POItemList(Request $request)
    {
        //dd($request);
        $tparm = new TparmView();
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $allow_over_receive = $tparm->getTparmValue('POReceipt', 'allow_over_receive');
        //  dd('hjshshshsh',$request->vend_num,$request->get('po_num'));
        if ($request->ajax()) {
            $output = '';
            $query = addslashes($request->get('query'));
            $po_num = $request->get('po_num');
            $whse_num = $request->get('whse_num');
            $item_num = $request->get('item_num');
            $vend_num = $request->get('vend_num');
            $vend_do = $request->get('vend_do');
            $po_line = $request->get('po_line');
            $receipt_type = $request->get('receipt_type');
            $grn_num = $request->get('grn_num');
            $form_action = $receipt_type == "NewGRN" ? "/home/<USER>/po-receipt/grn-process" : "/home/<USER>/po-receipt/process";

            if ($query != null) {
                $data = PurchaseOrderItem::leftJoin('purchase_orders', function ($join) {

                    $join->on('po_items.po_num', '=', 'purchase_orders.po_num');
                    $join->on('po_items.site_id', '=', 'purchase_orders.site_id');
                })
                    ->where('po_items.po_num', $po_num)
                    ->where('po_items.whse_num', $whse_num)
                    ->where('po_items.rel_status', '!=', 'C')
                    ->where(function ($q) use ($query) {
                        $q->orwhere('po_items.po_line', 'like', '%' . $query . '%')
                            ->orWhere('po_items.po_rel', 'like', '%' . $query . '%')
                            ->orWhere('po_items.item_num', 'like', '%' . $query . '%')
                            // ->orWhere('item_num', 'like', "%\\\\%")
                            ->orWhere('po_items.qty_balance', 'like', '%' . $query . '%')
                            ->orWhere('po_items.uom', 'like', '%' . $query . '%')
                            ->orWhere('po_items.item_desc', 'like', '%' . $query . '%')
                            ->orWhere('po_items.vend_do', 'like', '%' . $query . '%')
                            ->orderBy('po_items.po_line');
                    })->orderByRaw('cast(po_items.po_line as unsigned) ASC');
                if ($vend_num != "") {
                    $data = $data->where('po_items.vend_num', 'like', '%' . $vend_num . '%');
                }
                // dd($po_line);
                if ($po_line != "") {
                    $data = $data->where('po_items.po_line',  $po_line);
                }
                // ->where('item_num', '!=', 'NON-INV')
                $data = $data->get();
            } else {
                $data = PurchaseOrderItem::leftJoin('purchase_orders', function ($join) {

                    $join->on('po_items.po_num', '=', 'purchase_orders.po_num');
                    $join->on('po_items.site_id', '=', 'purchase_orders.site_id');
                })
                    ->where('po_items.po_num', $po_num)
                    ->where('po_items.whse_num', $whse_num)
                    // ->Where('vend_num', $vend_num)

                    //->orWhere('po_items.uom', 'like', '%' . $query . '%')
                    ->where('po_items.rel_status', '!=', 'C')
                    ->where('po_items.item_num', 'like', '%' . $query . '%');
                // ->where('item_num', '!=', 'NON-INV')
                if ($vend_num != "") {
                    $data = $data->where('po_items.vend_num', 'like', '%' . $vend_num . '%');
                    //    $data = $data->where('po_items.vend_num', $vend_num );
                }
                if ($po_line != "") {
                    $data = $data->where('po_items.po_line',  $po_line);
                }
                if ($vend_do != "" && $receipt_type != 'NewGRN') {
                    $data = $data->where('purchase_orders.vend_do', 'like', '%' . $vend_do . '%');
                }
                $data = $data->orderByRaw('cast(po_items.po_line as unsigned) ASC')
                    ->get();
            }

            //dd($data);
            $total_row = $data->count();


            // if SAP ON need to check sap_po_batch_sync table

            $tparm = new TparmView;
            $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

            if ($sap_trans_order_integration == 1) {
                // Query Data from sap_po_batch_sync based on : Site_id , Po_type = 1 , Po_num , Po_line
                $site_id = auth()->user()->site_id;
                //$getPOBatchDetails =  $po_details = DB::table('sap_po_batch_sync')->where('PO_type', 1)->where('po_num', $po_num)->where('site_id', $site_id)->whereIn('sync_status', [1])->orderBy('po_line','ASC')->get()->toArray();
                $getPOBatchDetails = DB::table('sap_po_batch_sync')
                    ->select('po_line', DB::RAW('sum(qty_received) as total'))
                    ->where('po_num', $po_num)
                    ->where('site_id', $site_id)
                    ->whereIn('sync_status', [1])
                    //->where('po_line', $request->po_line)
                    ->groupBy('po_line')
                    ->where('PO_type', '=', 1)->get();
                // $getPOBatchDetails =  $po_details = DB::table('sap_po_batch_sync')->where('po_line', $request->po_line)->where('PO_type', 1)->where('po_num', $po_num)->where('site_id', $site_id)->whereIn('sync_status', [1])->orderBy('po_line','ASC')->get()->toArray();
                //dd($getPOBatchDetails);
                $arrDataSAPBatchData = array();
                $count = 0;
                foreach ($getPOBatchDetails as $key) {
                    $arrDataSAPBatchData[$key->po_line] = $key->total;
                }
            }

            if ($total_row > 0) {
                foreach ($data as $row) {
                    $qty_balance = $row->qty_ordered - $row->qty_received + $row->qty_returned;

                    // if SAP ON need to check sap_po_batch_sync table
                    if ($sap_trans_order_integration == 1) {
                        if (@$arrDataSAPBatchData[$row->po_line] > 0) {
                            $qty_balance = $qty_balance - @$arrDataSAPBatchData[$row->po_line];
                            // $qty_required = $row->qty_balance - @$arrDataSAPBatchData[$row->po_line];
                        }
                    }
                    if ($qty_balance > 0.00 || $allow_over_receive == 1) {
                        $output .= '
                            <form class="form form-horizontal" method="get" action="' . $form_action . '">
                                <div class="row border border-primary" id="mybox" onclick="javascript:this.parentNode.submit();">
                                    <div class="col-xs-12">
                                        <table width="100%">
                                            <input type="hidden" name="_token" value="' . csrf_token() . '">
                                            <input type="hidden" name="receipt_type" value="' . $receipt_type . '">
                                            <input type="hidden" name="grn_num" value="' . $grn_num . '">
                                            <input type="hidden" name="vend_do" value="' . $vend_do . '"  >
                                            <input type="hidden" name="vend_num" value="' . $vend_num . '"  >
                                            <input type="hidden" name="frm_listing" value="PO"  >
                                            <tr>
                                                <td>
                                                    <label for="po_line">' . __('mobile.label.po_line') . '</label>
                                                </td>
                                                <td>
                                                    <input value="' . $row->whse_num . '" name="whse_num" type="hidden">
                                                    <input value="' . $row->id . '" name="id" type="hidden">
                                                    <input value="' . $row->po_num . '" name="po_num" type="hidden">
                                                    <input value="' . $row->po_line . '" name="po_line" type="hidden">
                                                    <input value="' . $row->po_rel . '" name="po_rel" type="hidden">
                                                    <input  size="3" type="text" id="po_line" class="form-control border-primary" value="' . $row->po_line . '" readonly>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td width="70px" >
                                                    <label for="qty_required">' . __('mobile.label.qty_required') . '</label>
                                                </td>
                                                <td>
                                                    <input type="text" id="qty_required" class="form-control border-primary" style="text-align: right;" name="qty_required" value="' . numberFormatPrecision($qty_balance, $unit_quantity_format, '.', '') . '" readonly>
                                                </td>
                                                <td>
                                                    <input type="text" id="uom" class="form-control border-primary" name="qty_required" value="' . $row->uom . '" readonly>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td> <label for="item">' . __('mobile.label.item_num') . '</label></td>
                                                <td colspan="3">
                                                    <input  size="10" style="text-align:left;" type="text" id="item" class="form-control border-primary" name="item" value="' . $row->item_num . '" readonly>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td></td>
                                                <td colspan="4">
                                                    <textarea  size="10" style="text-align:left;" type="text" id="description" name="item_desc" class="inputtext form-control border-primary" readonly>' . $row->item_desc . '</textarea>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </form>
                        ';
                    }
                }
            } else {
                $output = "
                <tr>
                <td align='center' colspan='4'>Record not found</td>
                </tr>
                ";
            }
            $data = array(
                'table_data' => $output,
                'total_data' => $total_row
            );

            echo json_encode($data);
        }
    }

    public function GRNList(Request $request)
    {
        $tparm = new TparmView();
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $allow_over_receive = $tparm->getTparmValue('POReceipt', 'allow_over_receive');

        if ($request->ajax()) {
            $output = '';
            $query = addslashes($request->get('query'));
            $whse_num = $request->get('whse_num');

            if ($query != null) {
                $data = GRN::where('whse_num', $whse_num)
                    ->where('grn_status', '!=', 'C')
                    ->where(function ($q) use ($query) {
                        $q->orwhere('grn_num', 'like', '%' . $query . '%')
                            ->orWhere('vend_num', 'like', '%' . $query . '%')
                            ->orWhere('vend_name', 'like', '%' . $query . '%')
                            // ->orWhere('item_num', 'like', "%\\\\%")
                            ->orWhere('shipped_date', 'like', '%' . $query . '%')
                            ->orWhere('vend_do', 'like', '%' . $query . '%')
                            ->orderBy('grn_num');
                    })
                    // ->where('item_num', '!=', 'NON-INV')
                    ->get();
            } else {
                $data = GRN::where('whse_num', $whse_num)
                    ->where('grn_status', '!=', 'C')
                    // ->where('item_num', 'like', '%' . $query . '%')
                    // ->where('item_num', '!=', 'NON-INV')
                    ->orderBy('grn_num')
                    ->get();
            }

            $total_row = $data->count();

            if ($total_row > 0) {
                foreach ($data as $row) {
                    // $qty_balance = number_format($row->qty_ordered - $row->qty_received + $row->qty_returned, $unit_quantity_format, '.', '');

                    // if ($qty_balance > 0.00 || $allow_over_receive == 1) {
                    $output .= '
                            <form class="form form-horizontal" method="get" action="/home/<USER>/po-receipt/process">
                                <div class="row border border-primary" id="mybox" onclick="javascript:this.parentNode.submit();">
                                    <div class="col-xs-12">
                                        <table width="100%">
                                            <input type="hidden" name="_token" value="' . csrf_token() . '">
                                            <tr>
                                                <td>
                                                    <label for="po_line">' . __('mobile.label.po_line') . '</label>
                                                </td>
                                                <td>
                                                    <input value="' . $row->whse_num . '" name="whse_num" type="hidden">
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <label for="grn_num">' . __('mobile.label.grn_num') . '</label>
                                                </td>
                                                <td width="50%">
                                                    <input type="text" id="grn_num" class="form-control border-primary" name="grn_num" value="' . $row->grn_num . '" readonly>
                                                </td>
                                                <td>
                                                    <label for="shipped_date">&nbsp;&nbsp;' . __('mobile.label.shipped_date') . '</label>
                                                </td>
                                                <td>
                                                    <input type="text" id="shipped_date" class="form-control border-primary" name="shipped_date" value="' . $row->shipped_date . '" readonly>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <label for="vend_num">' . __('mobile.label.vend_name') . '</label>
                                                </td>
                                                <td>
                                                    <input type="text" id="vend_num" class="form-control border-primary" name="vend_num" value="' . $row->vend_num . '" readonly>
                                                </td>
                                                <td>
                                                    <label for="vend_do">&nbsp;&nbsp;' . __('mobile.label.vend_do') . '</label>
                                                </td>
                                                <td>
                                                    <input type="text" id="vend_do" class="form-control border-primary" name="vend_do" value="' . $row->vend_do . '" readonly>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td></td>
                                                <td>
                                                    <input  size="10" style="text-align:left;" type="text" id="vend_name" name="vend_name" class="inputtext form-control border-primary"  value="' . $row->vend_name . '" readonly>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </form>
                        ';
                    // }
                }
            } else {
                $output = "
                <tr>
                <td align='center' colspan='4'>Record not found</td>
                </tr>
                ";
            }
            $data = array(
                'table_data' => $output,
                'total_data' => $total_row
            );

            echo json_encode($data);
        }
    }

    public function GRNItemList(Request $request)
    {
        $tparm = new TparmView();
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $allow_over_receive = $tparm->getTparmValue('POReceipt', 'allow_over_receive');

        if ($request->ajax()) {
            $output = '';
            $query = addslashes($request->get('query'));
            $whse_num = $request->get('whse_num');
            $grn_num = $request->get('grn_num');

            if ($query != null) {
                $data = GRNItem::where('grn_status', '!=', 'C')
                    ->where(function ($q) use ($query) {
                        $q->orwhere('grn_num', 'like', '%' . $query . '%')
                            ->orWhere('vend_num', 'like', '%' . $query . '%')
                            ->orWhere('vend_name', 'like', '%' . $query . '%')
                            // ->orWhere('item_num', 'like', "%\\\\%")
                            ->orWhere('shipped_date', 'like', '%' . $query . '%')
                            ->orWhere('vend_do', 'like', '%' . $query . '%')
                            ->orderBy('grn_num');
                    })
                    // ->where('item_num', '!=', 'NON-INV')
                    ->get();
            } else {
                $data = GRNItem::where('grn_num', $grn_num)
                    ->where('status', '!=', 'C')
                    // ->where('item_num', 'like', '%' . $query . '%')
                    // ->where('item_num', '!=', 'NON-INV')
                    ->orderBy('grn_line')
                    ->get();
            }

            $total_row = $data->count();

            if ($total_row > 0) {
                foreach ($data as $row) {
                    $qty_required = numberFormatPrecision($row->qty_shipped - $row->net_received, $unit_quantity_format);

                    // if ($qty_balance > 0.00 || $allow_over_receive == 1) {
                    $output .= '
                            <form class="form form-horizontal" method="get" action="/home/<USER>/po-receipt/process">
                                <div class="row border border-primary" id="mybox" onclick="javascript:this.parentNode.submit();">
                                    <div class="col-xs-12">
                                        <table width="100%">
                                            <input type="hidden" name="_token" value="' . csrf_token() . '">
                                            <tr>
                                                <td>
                                                    <input value="' . $row->whse_num . '" name="whse_num" type="hidden">
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <label for="grn_line">' . __('mobile.label.grn_line') . '</label>
                                                </td>
                                                <td width="45%">
                                                    <input type="text" id="grn_line" class="form-control border-primary" name="grn_line" value="' . $row->grn_line . '" readonly>
                                                </td>
                                                <td>
                                                    <label for="grn_num">&nbsp;&nbsp;' . __('mobile.label.grn_num') . '</label>
                                                </td>
                                                <td>
                                                    <input type="text" id="grn_num" class="form-control border-primary" name="grn_num" value="' . $row->grn_num . '" readonly>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <label for="item_num">' . __('mobile.label.item_num') . '</label>
                                                </td>
                                                <td >
                                                    <input type="text" id="item_num" class="form-control border-primary" name="item_num" value="' . $row->item_num . '" readonly>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td> </td>
                                                <td >
                                                    <input type="text" id="item_desc" class="form-control border-primary" name="item_desc" value="' . $row->item_desc . '" readonly>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <label for="qty_required">' . __('mobile.label.qty_required') . '</label>
                                                </td>
                                                <td>
                                                    <input type="text" id="qty_required" class="form-control border-primary" name="qty_required" value="' . $qty_required . '" readonly>
                                                </td>
                                                <td>
                                                    <input type="text" id="uom" class="form-control border-primary" name="uom" value="' . $row->uom . '" readonly>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </form>
                        ';
                    // }
                }
            } else {
                $output = "
                <tr>
                <td align='center' colspan='4'>Record not found</td>
                </tr>
                ";
            }
            $data = array(
                'table_data' => $output,
                'total_data' => $total_row
            );

            echo json_encode($data);
        }
    }
    public function LPNList($lpnNum, $record)
    {
        dd($request, $lpnNum);

        return view('Receiving.poreceipt.lpnList')->with('lpnNum', $lpnNum)->with('request', $request);
    }

    public function checkLPNStatus()
    {
        $checkRunningNumber = LicensePlateNumberDefinition::first();
        if ($checkRunningNumber == null) {
            // 0 = "LPN Definition not defined."
            return 0;
        } else {
            if ($checkRunningNumber->status == "N") {
                // 1 = LPN Definition is currently disabled.
                return 1;
            } else {
                // 200 =  ok
                return 200;
            }
        }
    }

    public static function poReceiveValidation($request)
    {
        $tparm = new TparmView;
        $errors = [];
        // dd($request);
        if ($request['grn_num'] && $request['receipt_type'] != "NewGRN") {
            $result = ValidationController::checkGRNValidation($request);
            if ($result !== true) {
                $errors['grn_num'] = $result;
            } else {
                $list = GRNItem::with('grn')
                    ->where('grn_num', $request->grn_num)->where('grn_line', $request->grn_line)->where('site_id', auth()->user()->site_id)->first();

                // Rewrite the ReadOnly fields
                $request->merge([
                    'grn_num' => $list->grn_num,
                    'grn_line' => $list->grn_line,
                    'whse_num' => $list->grn->whse_num,
                    'item_num' => $list->item_num,
                    'base_uom' => $list->uom,
                    'vend_do' => $list->grn->vend_do,
                    'vend_num' => $list->grn->vend_num
                ]);
            }
        }

        if ($request['ref_num']) {
            $PoItemList = PurchaseOrderItem::where('po_num', $request->ref_num)->where('po_line', $request->ref_line)->where('site_id', auth()->user()->site_id)->first();

            // Rewrite the ReadOnly fields
            $request->merge([
                'ref_num' => $PoItemList->po_num,
                'ref_line' => $PoItemList->po_line,
                'ref_release' => $PoItemList->po_rel
            ]);

            $result = ValidationController::checkPOLineValidation($request);
            if ($result !== true) {
                $errors['ref_num'] = $result;
            }
        }
        if ($request['whse_num']) {
            $whse_num = $request['whse_num'];
            $result = ValidationController::checkWhseValidation($whse_num);
            if ($result !== true) {
                $errors['whse_num'] = $result;
            }
        }
        // if ($request['item_num']) {
        //     $item_num = $request['item_num'];
        //     $result = ValidationController::checkItemNumValidation($item_num, $request['whse_num']);

        //     if ($result !== true) {
        //         $errors['item_num'] = $result;
        //     }
        // }
        if ($request['loc_num']) {
            $disable_create_new_item_location = $tparm->getTparmValue('PoReceipt', 'disable_create_new_item_location');

            $result = ValidationController::checkTransitPickingLocValidtion($request, 'loc_num', $disable_create_new_item_location);
            if ($result !== true) {
                $errors['loc_num'] = $result;
            }
        }
        if ($request['lot_num'] && $request['lot_tracked'] == 1) {
            $result = ValidationController::checkLotNumValidtion($request);
            if ($result !== true) {
                $errors['lot_num'] = $result;
            }
        }
        // if ($request['qty']) {
        //     $request['qty'] = $request['qty_to_ship'];
        //     $result = ValidationController::checkLotLocQtyValidtion($request);
        //     if ($result !== true) {
        //         $errors['qty_to_ship'] = $result;
        //     }
        // }
        // if ($request['uom']) {
        //     $request['selected_uom'] = $request['uom'];
        //     $result = ValidationController::validateConvUOM($request);
        //     if ($result['result'] !== true) {
        //         $errors['uom'] = $result['msg'];
        //     }
        // }

        return [$errors, $request];
    }
}
