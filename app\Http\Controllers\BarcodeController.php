<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Validator;
use PDF;
use Illuminate\Support\Arr;
use Milon\Barcode\DNS1D;
use Milon\Barcode\DNS2D;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Redirect;
use App\Barcode;
use App\Label;
use App\LabelTemplate;
use App\ItemLoc;
use App\Reprint;
use App\Module;
use App\User;
use App\JobMatl;
use App\PurchaseOrderItem;
use App\CustomerOrderItem;

use App\Item;
use App\Job;
use App\Customer;
use Response;
use Illuminate\Support\Facades\Input;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7;
use Illuminate\Support\Facades\Crypt;
use Carbon\Carbon;
use Auth;
use Alert;
use File;
use Illuminate\Support\Facades\Storage;
use App\LogActivity;
use App\Services\LotService;
use App\SiteSetting;
use App\TransferLine;
use App\TransferOrder;
use App\View\TparmView;
use Camroncade\Timezone\Facades\Timezone;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\ValidationException;
use App\Printer;
use App\Lot;
use App\Container;
use Route;
use App\ContainerItem;
use App\CustomerOrder;
use App\CustomerReturn;
use App\CustomerReturnLine;
use App\DocNote;
use App\MatlTrans;
use Dompdf\Dompdf;
use DB;
use App\PicklistAllocate;
use App\PicklistTestItems;
use App\Services\BartenderCloudService;
use App\Services\GeneralService;

class BarcodeController extends Controller
{

    public $inputDataMapping = [
        '1' => "GetInventoryLabelData",
        '1' => "GetCustOrdLabelData",
        'Job Receipt' => "GetJobReceiptLabelData",
        'Job Matl Issue' => "GetJobMatLabelData",
        'PO Receipt' => "GetPOReceiveLabelData",
        '1' => "GetCustOrdLabelDataMuplti",
    ];
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        // if(!\Gate::allows('isUserEditable')){
        //     return view('errors.404')->with('page','error');
        // }
        //$printer = Printer::select('id', 'printer_name')->get();
        $label = Label::select('file_name', 'label_name')->get();

        return view('barcode/barcode')->with("page", "barcode")->with(["label" => $label]);
    }

    /**
     * Show Print Inventory Label form
     * <AUTHOR>
     * @param request
     * @return \Illuminate\Http\Response
     */
    public function showInventoryLabel()
    {
        if (!\Gate::allows('hasInvLabel')) {
            return view('errors.404')->with('page', 'error');;
        }

        $tparm = new TparmView();
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $tparm = $tparm->getTparmValue('InventoryLabel', 'enable_warehouse', auth()->user()->site_id);

        return view('Barcode.invlabel')->with('page', 'misc')->with('tparm', $tparm)->with('unit_quantity_format', $unit_quantity_format);
    }

    /**
     * Process Print Inventory Label
     * <AUTHOR>
     * @param request
     */
    public function processInventoryLabel(Request $request)
    {

        $request = validateSansentiveValue($request);
        // $request->validate([
        //     'item_num' => 'required|exists:items,item_num,item_status,1,site_id,' . auth()->user()->site_id,
        //     'whse_num' => 'required|exists:warehouses,whse_num,whse_status,1,site_id,' . auth()->user()->site_id,
        //     'loc_num' => 'required|exists:locs,loc_num,whse_num,' . $request->whse_num . ',loc_status,1,site_id,' . auth()->user()->site_id,
        // ], [
        //     'item_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.items')]),
        //     'whse_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.warehouses')]),
        //     'loc_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.locations')]),
        // ]);

        $request->validate(
            [
                'item_num' => 'required',
                'whse_num' => 'required',
                'loc_num' => 'required',
            ],
            [
                'item_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.items')]),
                'whse_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.warehouses')]),
                'loc_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.locations')]),
            ]
        );
        Session::put('modulename', 'NoTransInv');

        $check_expiry_date = LotService::getExpiryDate($request);

        $transType = 'Inventory Label';

        $input = $request->except('_token');

        // $transDate = Carbon::now()->toDateTimeString();
        $transDate = self::getSiteTimeZoneDate();
        $item = $request->item_num;
        $whse = $request->whse_num;
        $loc = $request->loc_num;
        $lot = $request->lot_num;
        $expiry_date = $check_expiry_date ?? null;
        $uom = $request->uom;
        $qty = $request->qty;

        $desc = Item::select('item_desc', 'uom')
            ->where("item_num", $item)->first();

        $document = $request->document_num;
        $input = $this->GetInventoryLabelData($whse, $loc, $item, $lot, $qty, $uom, $document, $expiry_date, $transDate, $transType);
        $input['frm_print_label'] = $request->frm_print_label ?? 0;

        //dd($input,$request);
        return BarcodeController::showLabelDefinition($input);
    }

    /**
     * Show  Pallet Label form
     * <AUTHOR>
     * @param request
     */
    public function showPalletLabel()
    {

        if (!\Gate::allows('hasPalletLabel')) {
            return view('errors.404')->with('page', 'error');
        }
        $tparm = new TparmView();
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $tparm = $tparm->getTparmValue('POReceiptLabel', 'enable_warehouse', auth()->user()->site_id);

        return view('Barcode.pallet_label')->with('page', 'misc')->with('tparm', $tparm)->with('unit_quantity_format', $unit_quantity_format);
    }

    /**
     * Process Print Pallet Label form
     * <AUTHOR>
     * @param request
     */
    public function processPalletLabel(Request $request)
    {
        $request = validateSansentiveValue($request);
        // $request->validate([
        //     'item_num' => 'required|exists:items,item_num,item_status,1,site_id,' . auth()->user()->site_id,
        // ], [
        //     'item_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.items')]),
        // ]);


        $check_expiry_date = LotService::getExpiryDate($request);

        Session::put('modulename', 'NoTransPallet');

        $transType = 'Pallet';


        // $transDate = Carbon::now()->toDateTimeString();
        $transDate = self::getSiteTimeZoneDate();

        $lpn_num = $request->lpn_num;
        $getlpn = Container::where('lpn_num', $lpn_num)->first();
        $input = $this->GetPalletLabelData(
            $getlpn->id,
            $getlpn->creation_date,
            1,
            $transType
        );
        //dd($input);

        return BarcodeController::showLabelDefinition($input);
    }


    /**
     * Show Print PO Label form
     * <AUTHOR>
     *
     */
    public function showPORcptLabel()
    {

        if (!\Gate::allows('hasPoLabel')) {
            return view('errors.404')->with('page', 'error');
        }
        $tparm = new TparmView();
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting_total_qty');
        $tparm = $tparm->getTparmValue('POReceiptLabel', 'enable_warehouse', auth()->user()->site_id);

        return view('Barcode.polabel')->with('page', 'misc')->with('tparm', $tparm)->with('unit_quantity_format', $unit_quantity_format);
    }

    /**
     * Process Print PO Label form
     * <AUTHOR>
     * @param request
     */
    public function processPORcptLabel(Request $request)
    {
        $request = validateSansentiveValue($request);
        $request->validate([
            'item_num' => 'required|exists:items,item_num,item_status,1,site_id,' . auth()->user()->site_id,
            'whse_num' => 'required|exists:warehouses,whse_num,whse_status,1,site_id,' . auth()->user()->site_id,
            'loc_num' => 'required|exists:locs,loc_num,whse_num,' . $request->whse_num . ',loc_status,1,site_id,' . auth()->user()->site_id,
        ], [
            'item_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.items')]),
            'whse_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.warehouses')]),
            'loc_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.locations')]),
        ]);

        // Send error if po_num's status is completed
        $checkPoNum = PurchaseOrderItem::where('po_num', $request->po_num)->where('po_line', $request->po_line)->where('po_rel', $request->po_rel)->first();
        if ($checkPoNum) {
            if ($checkPoNum->rel_status == "C") {
                throw ValidationException::withMessages(['po_num' => 'PO-' . $request->po_num . ' cannot be proceed due to status is completed/closed.']);
            }
        }
        $check_expiry_date = LotService::getExpiryDate($request);

        Session::put('modulename', 'NoTransPO');

        $loc = '';

        $input = request()->validate([
            'po_num' => ['required', 'string', 'exists:po_items,po_num'],
            'po_line' => 'required|Integer',
            // 'po_rel' => 'required|Integer',
            'loc_num' => 'required',
            'qty' => 'required|numeric',
        ]);

        $transType = 'PoReceipt';

        if ($request->exists('transdate')) {
            $transDate = $request->transdate;
        } else {
            // $transDate = Carbon::now()->toDateTimeString();
            $transDate = self::getSiteTimeZoneDate();
        }
        $po_num = $request->po_num;
        $po_line = $request->po_line;
        // $po_rel = $request->po_rel;
        //search SLPO for vendor number and vendor name
        $po = PurchaseOrderItem::where("po_num", $po_num)->first();

        //search SLPOItem for item and description
        $poitem = PurchaseOrderItem::where("po_num", $po_num)
            ->where("po_line", $po_line)
            // ->where("po_rel", $po_rel)
            ->first();

        $whse = is_null($poitem) ? $po->whse_num : $poitem->whse_num;
        $qty = $request->qty;
        // Use given location.

        if ($request->loc_num == NULL) {
            $itemloc = ItemLoc::where("whse_num", $whse)
                ->where("item_num", $poitem->item)->first();
            if (!is_null($itemloc)) {
                $loc = $itemloc->loc_num;
            }
        } else {
            $loc = $request->loc_num;
        }
        $lot = $request->lot_num;
        $vendlot = $request->vendlot;
        $expiry_date = $check_expiry_date ?? null;

        $input = $this->GetPOReceiveLabelData($po_num, $po_line, $qty, $whse, $transDate, $transType, $loc, $lot, $expiry_date, $vendlot);
        $input['frm_po_print_label'] = $request->frm_po_print_label ?? null;
        return BarcodeController::showLabelDefinition($input);
    }

    /**
     * Show Print CO Ship Label form
     * <AUTHOR>
     *
     */
    public function showCOShippingLabel()
    {

        if (!\Gate::allows('hasCoLabel')) {
            return view('errors.404')->with('page', 'error');
        }
        $tparm = new TparmView();
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $tparm = $tparm->getTparmValue('COShippingLabel', 'enable_warehouse', auth()->user()->site_id);

        return view('Barcode.colabel')->with('tparm', $tparm)->with('unit_quantity_format', $unit_quantity_format);
    }

    /**
     * Process Print CO Ship Label form
     * <AUTHOR>
     * @param request
     */
    public function processCOShippingLabel(Request $request)
    {
        // dd($request);
        $request = validateSansentiveValue($request);
        $request->validate([
            'item_num' => 'required|exists:items,item_num,item_status,1,site_id,' . auth()->user()->site_id,
            'whse_num' => 'required|exists:warehouses,whse_num,whse_status,1,site_id,' . auth()->user()->site_id,
            'loc_num' => 'required|exists:locs,loc_num,whse_num,' . $request->whse_num . ',loc_status,1,site_id,' . auth()->user()->site_id,
        ], [
            'item_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.items')]),
            'whse_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.warehouses')]),
            'loc_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.locations')]),
        ]);

        // Send error if co_num's status is not open
        $checkCoNum = CustomerOrderItem::where('co_num', $request->co_num)->where('co_line', $request->co_line)->where('co_rel', $request->co_rel)->first();
        if ($checkCoNum) {
            if ($checkCoNum->rel_status == "C") {
                throw ValidationException::withMessages(['co_num' => 'CO-' . $request->co_num . ' cannot be proceed due to status is completed/closed']);
            }
        }

        $check_expiry_date = LotService::getExpiryDate($request);

        Session::put('modulename', 'NoTransCOShip');

        $validator = Validator::make($request->all(), [
            'co_num' => ['required', 'string'],
            'co_line' => 'required|string|min:1',
            'qty' => 'required|numeric',
        ])->validate();

        $transType = 'CustOrdShipping';
        $input = $request->except('_token');
        // $transDate = Carbon::now()->toDateTimeString();
        $transDate = self::getSiteTimeZoneDate();

        $conum = $request->co_num;
        $coline = $request->co_line;
        $corelease = $request->co_rel;
        $loc_num = $request->loc_num;
        $lot_num = $request->lot_num;
        $expiry_date = $check_expiry_date ?? null;
        $qty = $request->qty;
        // $coitem = coitem::where('co_num', $request['co_num'])->where('co_line', $coline)->where('co_rel', $corelease)->first();
        $coitem = CustomerOrderItem::where('co_num', $request['co_num'])->where('co_line', $coline)->first();
        $input = $this->GetCustOrdLabelData($conum, $coline, $corelease, $qty, $coitem->uom, $coitem->whse, $loc_num, $lot_num, $expiry_date, $transDate, $transType);

        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
        // dd(config('icapt.enable_cust_name_notfrom_cust_table'));

        if ($sap_trans_order_integration == 1 && config('icapt.enable_cust_name_notfrom_cust_table') == true) {
            $cust_name = $coitem->cust_name;
        } else {
            $cust_name =  $input['cust_name'];
        }
        $input['cust_name'] = $cust_name;


        //dd($input);
        return BarcodeController::showLabelDefinition($input);
    }

    /**
     * Show Print Job Material Label form
     * <AUTHOR>
     *
     */
    public function showJobMatLabel()
    {
        if (!\Gate::allows('hasJobMaterialIssueLabel')) {
            return view('errors.404')->with('page', 'error');
        }
        $tparm = new TparmView();
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $tparm = $tparm->getTparmValue('JobMaterialIssueLabel', 'enable_warehouse', auth()->user()->site_id);

        return view('Barcode.jobissuelabel')->with('page', 'misc')->with('tparm', $tparm)->with('unit_quantity_format', $unit_quantity_format);
    }

    /**
     * Process Print Job material Label form
     * <AUTHOR>
     * @param request
     */
    public function processJobMatLabel(Request $request)
    {
        $request = validateSansentiveValue($request);
        $request->validate([
            'item_num' => 'required|exists:items,item_num,item_status,1,site_id,' . auth()->user()->site_id,
            'whse_num' => 'required|exists:warehouses,whse_num,whse_status,1,site_id,' . auth()->user()->site_id,
            'loc_num' => 'required|exists:locs,loc_num,whse_num,' . $request->whse_num . ',loc_status,1,site_id,' . auth()->user()->site_id,
        ], [
            'item_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.items')]),
            'whse_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.warehouses')]),
            'loc_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.locations')]),
        ]);

        // Send error if job_num's job_status is not released
        $checkJobNum = Job::where('job_num', $request->job_num)->where('suffix', $request->suffix)->where('job_status', '!=', "R")->exists();
        if ($checkJobNum) {
            throw ValidationException::withMessages(['job_num' => 'Job Order-' . $request->job_num . ' cannot be proceed due to status is not released.']);
        }

        Session::put('modulename', 'NoTransJobMatl');

        $validator = Validator::make($request->all(), [
            'job_num' => ['required', 'string'],
            'qty' => 'required|numeric',
        ])->validate();

        $job_matl = JobMatl::select('matl_item', 'uom')->where('job_num', $request->job_num)->where('suffix', $request->suffix)->where('oper_num', $request->oper_num)->where('sequence', $request->sequence)->first();

        $check_expiry_date = LotService::getExpiryDate($request);

        $transType = 'JobMaterialIssue';
        $input = $request->except('_token');
        // $transDate = Carbon::now()->toDateTimeString();
        $transDate = self::getSiteTimeZoneDate();

        $jobnum = $request->job_num;
        $suffix = $request->suffix;
        $whse_num = $request->whse;
        $sequence = $request->sequence;
        $operation = $request->oper_num;
        $item_num = $job_matl->matl_item;
        $uom = $job_matl->uom;
        $expiry_date = $check_expiry_date ?? null;
        $qty = $request->qty;
        $lot_num = $request->lot_num;
        $loc_num = $request->loc_num;
        if ($request->operation) {
            $operation = $request->operation;
        }

        $input = $this->GetJobMatLabelData($jobnum, $suffix, $sequence, $operation, $item_num, $qty, $uom, $whse_num, $loc_num, $lot_num, $expiry_date, $transDate, 'JobMaterialIssue');
        return BarcodeController::showLabelDefinition($input);
    }

    /**
     * Show Print Job Receipt Label form
     * <AUTHOR>
     *
     */
    public function showJobRcptLabel()
    {
        if (!\Gate::allows('hasJobReceiptLabel')) {
            return view('errors.404')->with('page', 'error');
        }
        $tparms = new TparmView();
        $tparm = $tparms->getTparmValue('JobReceiptLabel', 'enable_warehouse');
        $def_loc = $this->getlocationFromTransParm($tparms->getTparmValue('JobReceipt', 'def_location'));

        return view('Barcode.jobreceiptlabel')->with('tparm', $tparm)->with('def_loc', $def_loc);
    }

    private function getlocationFromTransParm($def_loc)
    {
        if ($def_loc != '') {
            $loc = json_decode($def_loc);
            if ($loc->whse_num ?? null) {
                if (auth()->user()->getCurrWhse() == $loc->whse_num)
                    return $loc->loc_num;
            } else
                return '';
        }
        return '';
    }

    /**
     * Process Print Job Receipt Label form
     * <AUTHOR>
     * @param request
     */
    public function processJobRcptLabel(Request $request)
    {
        $request = validateSansentiveValue($request);
        $request->validate([
            'item_num' => 'required|exists:items,item_num,item_status,1,site_id,' . auth()->user()->site_id,
            'whse_num' => 'required|exists:warehouses,whse_num,whse_status,1,site_id,' . auth()->user()->site_id,
            'loc_num' => 'required|exists:locs,loc_num,whse_num,' . $request->whse_num . ',loc_status,1,site_id,' . auth()->user()->site_id,
        ], [
            'item_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.items')]),
            'whse_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.warehouses')]),
            'loc_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.locations')]),
        ]);

        // Send error if job_num's job_status is not released
        $checkJobNum = Job::where('job_num', $request->job_num)->where('job_status', '!=', "R")->exists();
        if ($checkJobNum) {
            throw ValidationException::withMessages(['job_num' => 'Job Order-' . $request->job_num . ' cannot be proceed due to status is not release.']);
        }

        $check_expiry_date = LotService::getExpiryDate($request);

        Session::put('modulename', 'NoTransJobReceipt');

        $validator = Validator::make($request->all(), [
            'job_num' => ['required', 'string'],
            'qty' => 'required|numeric',
            'lot_num' => $request->lot_num ? 'required' : '',
            'loc_num' => ['required', 'string'],
        ])->validate();

        $transType = 'JobReceipt';
        $input = $request->except('_token');
        // $transDate = Carbon::now()->toDateTimeString();
        $transDate = self::getSiteTimeZoneDate();

        $job_num = $request->job_num;
        $suffix = $request->suffix;
        $expiry_date = $check_expiry_date ?? null;
        $qty = $request->qty;
        $lot_num = $request->lot_num;
        $loc_num = $request->loc_num;
        $item_num = $request->item;
        $item_desc = $request->item_desc;
        $uom = $request->uom;
        $document_num = $request->document_num;

        $input = $this->GetJobReceiptLabelData($job_num, $suffix, $item_num, $item_desc, $qty, $uom, $loc_num, $lot_num, $expiry_date, $transDate, $document_num, 'JobReceipt');
        return BarcodeController::showLabelDefinition($input);
    }

    /**
     * Show Print TO Shipping Label form
     * <AUTHOR>
     *
     */
    public function showTOShippingLabel()
    {
        if (!\Gate::allows('hasTOShippingLabel')) {
            return view('errors.404')->with('page', 'error');
        }

        $tparm = new TparmView();
        $tparm = $tparm->getTparmValue('TOShippingLabel', 'enable_warehouse', auth()->user()->site_id);

        return view('Barcode.toshippinglabel')->with('tparm', $tparm);
    }

    /**
     * Process Print TO Shipping Label
     * <AUTHOR>
     * @param request
     */
    public function processTOShippingLabel(Request $request)
    {
        $request = validateSansentiveValue($request);
        $request->validate([
            'item_num' => 'required|exists:items,item_num,item_status,1,site_id,' . auth()->user()->site_id,
            'from_whse' => 'required|exists:warehouses,whse_num,whse_status,1,site_id,' . auth()->user()->site_id,
            // 'to_whse' => 'required|exists:warehouses,whse_num,whse_status,1,site_id,'.auth()->user()->site_id,
            'loc_num' => 'required|exists:locs,loc_num,whse_num,' . $request->from_whse . ',loc_status,1,site_id,' . auth()->user()->site_id,
        ], [
            'item_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.items')]),
            'whse_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.warehouses')]),
            'loc_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.locations')]),
        ]);

        Session::put('modulename', 'NoTransTOShipping');

        $to_details = TransferLine::with('item')->with('lot')
            ->where('trn_num', $request->trn_num)
            ->where('trn_line', $request->trn_line)
            ->where('item_num', $request->item_num)
            ->first();
        //         dd($to_details);
        $check_expiry_date = LotService::getExpiryDate($request);

        $transType = 'TranOrderShipping';

        $input = $request->except('_token');

        // $transDate = Carbon::now()->toDateTimeString();
        $transDate = self::getSiteTimeZoneDate();

        $item_num = $request->item_num;
        $from_whse = $request->from_whse;
        $to_whse = $to_details ? $to_details->to_whse : $request->to_whse;
        $trn_num = $request->trn_num;
        $trn_line = $request->trn_line;
        $loc_num = $request->loc_num;
        $lot_num = $request->lot_num;
        $expiry_date = $check_expiry_date ?? null;
        $uom = $request->uom;
        $qty = $request->qty;
        $fromlabel = $request->fromlabel ?? null;
        $desc = Item::select('item_desc', 'uom')
            ->where("item_num", $item_num)->first();

        $document = $request->document_num;

        $input = $this->GetTOShippingLabelData($from_whse, $to_whse, $trn_num, $trn_line, $item_num, $desc->item_desc, $loc_num, $lot_num, $qty, $uom, $expiry_date, $transDate, $transType, $fromlabel);
        // dd($input);
        return BarcodeController::showLabelDefinition($input);
    }
    /**
     * Label configuration and storing it for reprint
     * <AUTHOR> Perk
     */
    public function PrintMultiLinesBartenderLabelProcess(Request $request)
    {

        if (config('dcapt.btadebug') == "1") {
            $file_ext = '.test';
            $input = $request->except('_token', 'datajson', 'hqty');
            return $this->LabelOutput($request, $input);
        } else {
            $file_ext = '.dat';
        }
        $input = $request->except('_token', 'datajson', 'hqty');
        $datetime = Timezone::convertFromUTC(now(), auth()->user()->getSiteTimezone(), SiteSetting::getOutputDateFormat() . ' H:i:s');

        $arrMultiplevalue = array();
        $index = 0;
        foreach ($request->co_num as $kname => $avalue) {
            $arrMultiplevalue['co_num'][$index] = $avalue;
            $arrMultiplevalue['co_line'][$index] = $request->co_line[$kname];
            $arrMultiplevalue['lot_num'][$index] = $request->lot_num[$kname];
            $arrMultiplevalue['qty'][$index] = $request->qty[$kname];
            $arrMultiplevalue['uom'][$index] = $request->uom[$kname];
            $arrMultiplevalue['expiry_date'][$index] = $request->expiry_date[$kname];
            $arrMultiplevalue['trans_date'][$index] = $request->trans_date[$kname];
            $arrMultiplevalue['transType'][$index] = $request->transType[$kname];
            $arrMultiplevalue['whse_num'][$index] = $request->whse_num[$kname];

            $arrMultiplevalue['loc_num'][$index] = $request->loc_num[$kname];
            $arrMultiplevalue['cust_num'][$index] = $request->cust_num[$kname];
            $arrMultiplevalue['item_num'][$index] = $request->item_num[$kname];
            $arrMultiplevalue['item_desc'][$index] = $request->item_desc[$kname];
            $arrMultiplevalue['cust_item_code'][$index] = $request->cust_item_code[$kname];
            $arrMultiplevalue['ref'][$index] = $request->ref[$kname];
            $arrMultiplevalue['printer'][$index] = $request->printer;

            $index++;
            //$arrMultiplevalue[$kname]['co_line'] = $request->co_num[$kname];

        }

        //dd($request,$arrMultiplevalue);
        //requesting configuration and encode it to json
        $json = json_decode($request->datajson);

        $maxbox = $boxnum = $labelperbox = 0;

        $count = 0;
        $arrTotal = array();
        // check for the directory and file
        // path
        //adding value to total box

        foreach ($json as $key => $value) {

            $maxbox = $value->numofbox + $maxbox;
        }
        //dd($json,$maxbox);
        if (config('icapt.bartender')) {
            // timestamp on the file
            $ts = date('YmdHi', time());
            $filename = 'labelfile_' . $ts;
            $BTParams = '/D="%Trigger File Name%" /R=3 /P '; // BT Print parameters
            // Label output file
            $data_file = config('icapt.btwdatafolder') . $filename;   //'\\\\************\\LabelOutput\\';
            $labelTplpath = config('icapt.btwtemplatepath');  // label template

            foreach ($json as $key => $label) {
                $boxnum = 0;
                $labelfilename = Label::select('file_name')->where('id', $label->labelname)->value('file_name');
                //iterate how many boxes are there if 4 boxes, will loop 4 ti mes
                for ($i = 1; $i <= $label->numofbox; $i++) {
                    $count++;
                    //auto increment for number of box
                    $boxnum++;
                    //$arrTotal[$key] +=$boxnum;
                    //convert the quantity to 2 decimal places
                    // $qtyinbox = number_format((float) $label->quantityperbox, 2, '.', '');
                    $qtyinbox = numberFormatPrecision($label->quantityperbox, false);

                    //adding the number of box, quantity per box and total box
                    $input[$key]['co_num'] =  $arrMultiplevalue['co_num'][$key];
                    $input[$key]['co_line'] =  $arrMultiplevalue['co_line'][$key];
                    $input[$key]['lot_num'] =  $arrMultiplevalue['lot_num'][$key];
                    $input[$key]['qty'] =  $arrMultiplevalue['qty'][$key];
                    $input[$key]['uom'] =  $arrMultiplevalue['uom'][$key];
                    $input[$key]['expiry_date'] =  $arrMultiplevalue['expiry_date'][$key];
                    $input[$key]['trans_date'] =  $arrMultiplevalue['trans_date'][$key];
                    $input[$key]['transType'] =  $arrMultiplevalue['transType'][$key];
                    $input[$key]['whse_num'] =  $arrMultiplevalue['whse_num'][$key];
                    $input[$key]['loc_num'] =  $arrMultiplevalue['loc_num'][$key];
                    $input[$key]['cust_num'] =  $arrMultiplevalue['cust_num'][$key];


                    $input[$key]['item_num'] =  $arrMultiplevalue['item_num'][$key];
                    $input[$key]['item_desc'] =  $arrMultiplevalue['item_desc'][$key];
                    $input[$key]['cust_item_code'] =  $arrMultiplevalue['cust_item_code'][$key];
                    $input[$key]['ref'] =  $arrMultiplevalue['ref'][$key];
                    $input[$key]['printer'] =  $arrMultiplevalue['printer'][$key];



                    // $input[$key]['label']  = $request->label[$key];
                    $input[$key]['boxnum'] = $boxnum;
                    $input[$key]['bn'] = $boxnum;
                    $input[$key]['qtyinbox'] = $qtyinbox;
                    $input[$key]['qib'] = $qtyinbox;
                    $input[$key]['totalbox'] = (int)$label->numofbox;
                    $input[$key]['tb'] = (int)$label->numofbox;
                    $input[$key]['labelperbox'] = (int) $label->labelperbox;
                    $input[$key]['creator'] = Auth::user()->name;     // add Creator to the label info

                    $labelFile = $labelTplpath . "\\" . $labelfilename . "";
                    $text = preg_replace("/\r|\n/", "",  $input[$key]);
                    list($keys, $values) = array_divide($text);
                    $dattempfile = $filename . $count . $file_ext;
                    $label_out_file = $data_file . $count . $file_ext;



                    Storage::disk('local')->put($dattempfile, '%BTW% /AF="' . $labelFile . '"' . '/PRN="' . $request->printer . '"' .
                        $BTParams . ' /C=' . $input['labelperbox'] . "\r\n" . '%END%' . "\r\n" . '' . implode('|', $keys) . "\r\n" . '' . implode('|', $values) . '');

                    // Move to Label Output folder
                    if (copy(Storage::disk('local')->path($dattempfile), $label_out_file)) {
                        // delete the temp file
                        Storage::delete($dattempfile);
                    }
                    LogActivity::addToLog('Print Label.', array($dattempfile, $labelFile, $request->printer), NULL);
                }
            }
        }

        // dd($dattempfile,$labelFile);
        $input = $request->except('_token', 'datajson', 'hqty', 'boxnum', 'bn', 'tb', 'qib', 'qtyinbox', 'totalbox', 'ref');

        Session::put('print_status', 'Yes');
        return redirect()->route('backbutton');
    }






    public function PrintPreviewLabelProcess(Request $request)
    {
        //dd($request);

        $input = $request->all();
        //  $lpns=explode(PHP_EOL,$request->lpn_num);

        // if ( $input['pre_quantities'] == null) {
        //     $input['pre_quantities']=[];
        // }
        if (!isset($input['pre_quantities'])) {
            $input['pre_quantities'] = [];
        }
        // dd($input);
        $siteSettings = new SiteSetting();
        //label list according to transaction type
        $modules = new Module();
        $modules = $input['transType'] == "GRNReceipt" ? $modules->find('PoReceipt') : $modules->find($input['transType']);
        //$modules = "CustOrdPicking";
        $label_defaults = $modules ? $modules->labels()->get() : [];
        $default_label = DB::table('label_modules')->where('modulename', $input['transType'])->where('is_default', 1)->where('site_id', auth()->user()->site_id)
            ->first();
        // dd($label_defaults, $modules);
        $types = [];
        foreach ($label_defaults as $label_default) {
            $types[] = $label_default->type;
        }
        //dd($modules, $label_defaults, $types,$input);
        // $label = Label::whereIn('type', $types)->get();
        // dd($label);
        $label = $label_defaults;
        //dd($label);

        // dd($label);
        $no_of_boxes = Session::get('no_of_lpn', 1);
        // // dd($no_of_boxes);
        // $no_of_boxes= $no_of_boxes??1;
        $no_of_pallets = $no_of_boxes ?? 1;
        // dd($no_of_pallets);
        $input['no_of_pallets'] = $no_of_pallets;
        // dd($input);
        $defaultValues = array('labelperbox' => 1, 'numofbox' => 1, 'quantityperbox' => $input['qty']);
        $cancel_href = null;
        $back_href = null;
        $back_picklist_href = null;
        $back_using_js = false;
        //dd($input['transType'],$input);
        // dd($input,'kakak');
        $picklist_id = Session::get('picklist_id');
        // dd($input['transType']);
        // dd($input['transType'],session('JobRunEndLabour'));
        // dd(session('MachineRunCheck'),$input,session('WIPMove'));
        //dd($input['transType'],session()->get('modulename_action'));
        if ($input['transType'] == "CustOrdPicking" || $input['transType'] == "Picklist") {
            //dd($picklist_id);
            // CoPickingDetails // CoPick
            $pick_by = session()->get('pick_by_as');
            $select_pick_by = session()->get('select_pick_by');

            $shipping_zone_code =  session()->get('shipping_zone_code');
            $strsales_person = session()->get('strsales_person');
            $item_num =  session()->get('item_num');
            $cust_num =  session()->get('cust_num');
            $pick_action = session()->get('modulename_action');

            //dd($pick_action,$picklist_id);

            if ($picklist_id) {
                if ($pick_action == "Pick") {
                    $item_desc = session()->get('item_desc');
                    $qty_to_pick = session()->get('qty_to_pick');
                    $uom = session()->get('uom');
                    $zone_num = session()->get('zone_num');
                    $loc_num = session()->get('loc_num');
                    $lot_num = session()->get('lot_num');
                    $item_num_en = session()->get('item_num_encode');
                    $emp_num = session()->get('emp_num');
                    $qty_required = session()->get('qty_required');
                    $countplist = session()->get('count-picklist-' . $emp_num . '-' . $request->whse_num);
                    // dd($emp_num,$countplist);

                    /*$input = array(
                        'picklist_id' => $picklist_id,
                        'cust_num'    => $cust_num,
                        'item_num_en' => $item_num_en,
                        'item_desc'   => $item_desc,
                        'uom'         => $uom,
                        'zone_num'    => $zone_num,
                        'loc_num'     => $loc_num,
                        'lot_num'     => $lot_num,
                        'qty_to_pick' => $qty_to_pick,
                        'countplist'  => $countplist,
                        'emp_num'     => $emp_num,
                        'whse_num'    => $request->whse_num,
                        'qty'         => $input['qty'],
                        'no_of_pallets'         => $input['no_of_pallets'],
                        'transType'             => $input['transType']

                     );*/
                    // Redirect function
                    //$cancel_href = generateCancelUrl('Pick',$input);


                    $picklist_test_items = PicklistTestItems::with('item')->with('picklist_allocates')
                        ->where('picklist_id', $picklist_id)
                        ->where('line_status', '!=', 'C')
                        ->orderBy('due_date')
                        ->get();
                    $countplist = $picklist_test_items->count();
                    $cancel_href = route('picksTest.confirmPick', ['picklist' => $picklist_id, 'cust_num' => $cust_num, 'item_num' => $item_num_en, 'item_desc' => $item_desc, 'uom' => $uom, 'zone_num' => $zone_num, 'loc_num' => $loc_num, 'lot_num' => $lot_num, 'qty_to_pick' => $qty_to_pick]);
                    // dd( $countplist,$qty_to_pick);
                    /*if($qty_required==0){
                        $cancel_href = route('picksTest.picklistDetails', ['picklist' => $picklist_id]);
                    }
                    else{
                        $cancel_href = route('picksTest.confirmPick', ['picklist' => $picklist_id, 'cust_num' => $cust_num, 'item_num' => $item_num_en, 'item_desc' => $item_desc, 'uom' => $uom, 'zone_num' => $zone_num, 'loc_num' => $loc_num, 'lot_num' => $lot_num, 'qty_to_pick' => $qty_to_pick]);
                    }*/
                    if (@$countplist > 1) {
                        //dd(55);
                        //$cancel_href = route('picksTest.list', ['whse_num' => $request->whse_num,'emp_num'=>$emp_num]);

                        if ($qty_required > 0) {
                            $cancel_href = route('picksTest.picklistDetails', ['picklist' => $picklist_id]);
                        } else {
                            $cancel_href = route('picksTest.pickItem', ['picklist' => $picklist_id]);
                        }
                    } else {
                        if (($qty_to_pick == 0 || $qty_to_pick == null) && $countplist == 1) {

                            // $cancel_href = route('picksTest.pickItem', ['picklist' => $picklist_id]);
                            $cancel_href = route('picksTest.picklistDetails', ['picklist' => $picklist_id]);
                        } else {
                            //dd(2);
                            if ($countplist == 0) {
                                $cancel_href = route('picksTest.picklistDetails', ['picklist' => $picklist_id]);
                                //dd('jajajajaj',$cancel_href);
                            } else {
                                $cancel_href = route('picksTest.confirmPick', ['picklist' => $picklist_id, 'cust_num' => $cust_num, 'item_num' => $item_num_en, 'item_desc' => $item_desc, 'uom' => $uom, 'zone_num' => $zone_num, 'loc_num' => $loc_num, 'lot_num' => $lot_num, 'qty_to_pick' => $qty_to_pick]);
                            }
                        }
                    }
                } else if ($pick_action == "UnPick") {
                    $qty_picked = session()->get('qty_picked');
                    $emp_num = session()->get('unpick_emp_num');
                    //$uncountplist =  session()->put('count-unpick-'.auth()->user()->id.'-'.auth()->user()->site_id.'-'.$picklist_id, $countUnpick);
                    $uncountplist = session()->get('count-unpick-' . auth()->user()->id . '-' . auth()->user()->site_id . '-' . $picklist_id . '-' . $emp_num);

                    // dd($qty_picked - $request->qty);
                    $picklist_allocate = session()->get('picklist_allocate');
                    $to_loc = session()->get('to_loc_unpick');
                    //dd($uncountplist,$qty_picked - $request->qty);
                    if ($uncountplist == 1 && ($qty_picked - $request->qty > 0)) {
                        $qty_picked = session()->get('qty_picked');
                        $cancel_href = route('picksTest.unpick', [
                            'picklist' => $picklist_id,
                            'picklist_allocate' => $picklist_allocate,
                            'whse_num' => $request->whse_num,
                            'loc_num' => $to_loc,
                            'co_num' => $request->co_num,
                            'qty_picked' => $qty_picked - $request->qty,
                            'uom' => $request->uom,
                            'lot_num' => $request->lot_num,
                        ]);

                        // dd(1);

                        // $cancel_href = route('picksTest.pickedItems', ['picklist' => $picklist_id]);
                        // $cancel_href = route('picksTest.unpick', ['picklist' => $picklist_id,'picklist_allocate'=>$picklist_allocate]);
                    } else {


                        $cancel_href = route('picksTest.pickedItems', ['picklist' => $picklist_id]);


                        // $cancel_href = route('picksTest.confirmPick', ['picklist' => $picklist_id,'item_num' => $item_num,'item_desc'=>$item_desc, 'uom' => $pick_by, 'select_pick_by' => $select_pick_by, 'shipping_zone_code' => $shipping_zone_code, 'strsales_person' => $strsales_person, 'item_num' => $item_num, 'whse_num' => $input['whse_num'], 'co_num' => $input['co_num']]);
                    }
                    //
                }





                // ?item_num=QXBwbGU%3D&item_desc=Apple&qty_to_pick=0.60&uom=EA&zone_num=BULK-1&loc_num=A1-2&lot_num=LOT02SS
                // $cancel_href = route('picksTest.confirmPick', ['picklist' => $picklist_id,'item_num' => $item_num,'item_desc'=>$item_desc, 'uom' => $pick_by, 'select_pick_by' => $select_pick_by, 'shipping_zone_code' => $shipping_zone_code, 'strsales_person' => $strsales_person, 'item_num' => $item_num, 'whse_num' => $input['whse_num'], 'co_num' => $input['co_num']]);
            } else if ($pick_action == "COPick") {
                $input['all'] = $request->all();
                $qty_required = session()->get('qty_required');
                $input['indicate'] = 0;
                $input['qty_required'] = $qty_required;
                $url = generateRedirectUrl('CoPick', $input);

                $cancel_href = $url->getTargetUrl();
                // dd($cancel_href,"klksksk",$qty_required);

                // $strCancelURL = redirect()->route('CoPickingProcess', [
                //     'totalrecord' => $input['count_list'],
                //     'stage_num' => base64_encode($stage_num),
                //     'co_num' =>  base64_encode($co_num),
                //     'delivery_date' => @$input['all']['delivery_date'],
                //     'delivery_trip' => @$input['all']['delivery_trip'],
                //     'co_line' => $input['all']['co_line'],
                //     'co_rel' => @$input['co_rel'] ?? 0,
                //     'item' =>  $input['all']['item']  ?? $input['all']['item_num'],
                //     'uom' => $input['all']['uom'],
                //     'item_desc' => $input['all']['item_desc'],
                //     'whse_num' => base64_encode($whse_num),
                //     'qty_req' => $qty_shortage
                // ]);







            } else {
                $cancel_href = route('CoPickingDetails', ['cust_num' => $cust_num, 'pick_by' => $pick_by, 'select_pick_by' => $select_pick_by, 'shipping_zone_code' => $shipping_zone_code, 'strsales_person' => $strsales_person, 'item_num' => $item_num, 'whse_num' => $input['whse_num'], 'co_num' => $input['co_num'], 'co_line' => "", 'stage_num' => $input['stage_num']]);
            }


            // dd($cancel_href, session());
        } else if ($input['transType'] == "TranOrderShipping") {
            //

            if ($request->fromlabel == 1) {
                $back_using_js = true;
            } else {
                $TO_list = TransferLine::where('trn_num', $input['trn_num'])->where('from_whse', $input['from_whse'])->where('site_id', auth()->user()->site_id)->where('line_stat', 'O');
                //dd($TO_list->count());
                //dd($TO_list->get());
                if ($TO_list->count() > 1) {
                    //$concel_href =
                    $back_href = route('backTOShippingList', ['whse_num' => $input['from_whse'], 'trn_num' => $input['trn_num'], 'item_num' => '']);
                } else {
                    // If single and still have qty return to form else return selection
                    //dd($TO_list);
                    $Totalrequiored = $TO_list->first();
                    //dd($Totalrequiored);
                    if ($Totalrequiored->total_qty_required > 0) {
                        // Back to Form
                        //dd($TO_list,'here');
                        //dd($input);
                        $back_href = route('showTOShippingProcess', ['whse_num' => $input['from_whse'], 'trn_num' => $input['trn_num'], 'trn_line' => $input['trn_line']]);
                    } else {

                        $back_href = route('TransferOrderShipping');
                    }
                }



                //$back_href = route('backTOShippingList', ['whse_num' => $input['from_whse'], 'trn_num' => $input['trn_num'], 'item_num' => '']);

                // dd($back_href,'jsjs');
            }
        } else if ($input['transType'] == "TransferOrderReceipt") {
            $back_href = route('transferOrderItemList', ['whse_num' => $input['to_whse'], 'trn_num' => $input['trn_num'], 'item_num' => '']);
        } else if ($input['transType'] == "Picklist") {
            $back_picklist_href = route('picksTest.pickItem', ['picklist' => $picklist_id]);
        } else if ($input['transType'] == "JobReceipt" && session('WIPMove') == 1) {
            // session("WIPMove","");
            session()->put('WIPMove', "");
            $back_href = route('WIPMove');
        } else if ($input['transType'] == "JobReceipt" && session('MachineRunCheck') == 2) {
            // dd("dsd",session('WIPMove'));
            session()->put('MachineRunCheck', "");
            //session("MachineRunCheck","");
            $back_href = route('MachineRun');
            $tparm = new TparmView;
            $allow_multiple_job = $tparm->getTparmValue('MachineRun', 'allow_multiple_job');
            if ($allow_multiple_job) {
                $back_href = route('MachineRunEnd');
            }
        } else if ($input['transType'] == "JobReceipt" && session('JobRunEndLabour') == 3) {

            session()->put('JobRunEndLabour', "");
            $tparm = new TparmView();
            if ($tparm->getTparmValue('JobLabour', 'allow_multiple_job') == 0) {
                // return redirect()->route('LabourReporting');
                $back_href = route('LabourReporting');
            } else {
                //return redirect()->route('EndLabourReporting');
                $back_href = route('EndLabourReporting');
            }
        } else if ($input['transType'] == "Pallet") {
            // $back_href = route('PalletBuilder');
            // if(isset($input['labelPage']))
            // {
            //     $back_href = route('showPalletLabel');
            // }
            $back_using_js = true;
        } else if ($input['transType'] == "GRNReceipt") {
            //dd($input,'hshshshs');
            if ($input['receipt_type'] == "NewGRN") {
                $cancel_href = '/home/<USER>/po-receipt';
            } else {
                $cancel_href = $input['CancelURL'];
            }
        } else if ($input['transType'] == "PoReceipt") {
            //dd($input);
            $intFrmPrint = $input['frm_po_print_label'] ?? null;
            if ($intFrmPrint != 1) {

                $input['ref_num'] = $request->po_num;
                $input['whse_num'] = $request->whse_num;
                $input['po_line'] = $request->po_line;
                $input['po_rel'] = $request->ref_release;
                $input['item_num'] = $request->item_num;
                $input['uom'] = $request->uom;
                $input['qty_required'] = $request->qty_required;
                $input['item_desc'] = $request->item_desc;
                $input['indicate'] = 0;
                // $input['vend_do'] = $request->vend_do;
                // return redirect()->route('showPoLineReturn', ['whse_num' => $request->whse_num, 'po_num' => $request->ref_num, 'item_num' => $request->item_num, 'vend_do' => $request->vend_do]);
                $url = generateRedirectUrl('PoReceipt', $input);
                //$cancel_href = $url;
                $cancel_href = $url->getTargetUrl();
            }
        } elseif ($input['transType'] == "CustomerReturn") {


            $input['all'] = $request->all();
            $input['from_list'] = 0;
            $input['indicate'] = 2;
            $url = generateRedirectUrl('CustomerReturn', $input);
            $cancel_href = $url->getTargetUrl();
            //dd('redirect here');
        }

        // dd($cancel_href,'xxx');
        if (isset($input['trn_num']))
            $input['to_num'] = $input['trn_num'];
        if (isset($input['trn_line']))
            $input['to_line'] = $input['trn_line'];

        $sess = $request->session()->get('alert');
        // dd($sess,'kakaka',$input);
        if ($sess) {
            $alertData = json_decode($sess['config'], true);
            // dd($alertData);
            $alertText = $alertData['text'];
            $alertTitle = $alertData['title'];
            // dd($alertData);
            Alert::success($alertTitle, $alertText);
        }

        $view = "Barcode.labeldefinition";
        // dd($arrInput);
        if (isset($input['disable_qty'])) {
            $view = "Barcode.pallet.labeldefinition";
        }

        $tparm = new TparmView();
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $quantity_per_format = $tparm->getTparmValue('System', 'decimal_setting_quantity');
        //dd(config('icapt.bartender'));
        if (config('icapt.bartender')) {
            Session::put('print_status', 'No');
            $tparm = new TparmView();

            //printer list
            $printer = Printer::select('id', 'printername')->get();
            $use_bartender = $tparm->getTparmValue('System', 'default_label_print_method');



            return view('Barcode.labeldefinition')
                ->with("page", "barcode")
                ->with(["printer" => $printer, "label" => $label])
                ->with("input", $input)
                ->with('default_label', $default_label)
                ->with('defaultValues', $defaultValues)
                ->with('use_bartender', $use_bartender)
                ->with('cancel_href', $cancel_href)
                ->with("label_default", @$label_default)
                ->with('back_href', $back_href)
                ->with('back_using_js', $back_using_js)
                ->with('back_picklist_href', $back_picklist_href)
                ->with('unit_quantity_format', $unit_quantity_format)
                ->with('quantity_per_format', $quantity_per_format);
        }

        $print_node_setting = \App\SiteConnection::where('site_id', auth()->user()->site_id)->where('type', 'simple')->where('active', 1)->where('connector', 'print_node')->first();
        // dd($print_node_setting);
        $printers = [];


        $error = "";
        if ($print_node_setting) {
            $apiKey = $print_node_setting;
            // $printerId = "72442662";

            $apiKey = \PrintNode\Client::getAxacuteConnection($print_node_setting);
            $credentials = new \PrintNode\Credentials\ApiKey($apiKey);
            try {
                $client = new \PrintNode\Client($credentials);
                $printers = $client->viewPrinters();
            } catch (\Throwable $th) {
                //throw $th;

                // $error = "PrintNode: " . $th->getMessage();
                $errorMsg = $th->getMessage();
                $errorText = __('error.admin.print_node_error', ['errorText' => $errorMsg]);
                Alert::html("Oops!", $errorText, 'error');
            }

            // dd($printers);
        }
        $templates = [];
        $bartender_cloud_setting = \App\SiteConnection::where('site_id', auth()->user()->site_id)->where('type', 'simple')->where('active', 1)->where('connector', 'bartender_cloud')->first();
        if ($bartender_cloud_setting) {

            try {
                $printers = BartenderCloudService::getPrinters();
                $templates = BartenderCloudService::getTemplates();
            } catch (\Throwable $th) {
                //throw $th;

                // $error = "PrintNode: " . $th->getMessage();
                $errorMsg = $th->getMessage();
                $errorText = __('error.admin.bartender_cloud_error', ['errorText' => $errorMsg]);
                Alert::html("Oops!", $errorText, 'error');
            }

            // dd($printers);
        }
        // dd($default_label);
        // dd($input,'kk',$view,$label);
        $view = view('Barcode.labeldefinition')
            ->with("page", "barcode")
            ->with(["label" => $label, 'bartender_cloud_setting' => $bartender_cloud_setting, 'print_node_setting' => $print_node_setting, 'printers' => $printers, 'templates' => $templates])
            ->with("label_default", @$label_default)
            ->with("input", $input)
            ->with('default_label', $default_label)
            ->with('defaultValues', $defaultValues)
            ->with('cancel_href', $cancel_href)
            ->with('back_href', $back_href)
            ->with('back_using_js', $back_using_js)
            ->with('back_picklist_href', $back_picklist_href)
            ->with('unit_quantity_format', $unit_quantity_format)
            ->with('quantity_per_format', $quantity_per_format);
        // if ($error != "")
        //     $view->withErrors($error);
        return $view;
    }
    /**
     * Label configuration and storing it for reprint
     * <AUTHOR> Firdaus
     */
    public function PrintLabelProcess(Request $request)
    {
        //dd($request);
        $tparm = new TparmView;
        $quantity_per_format = $tparm->getTparmValue('System', 'decimal_setting');
        $item = $request->item_num;
        $desc = Item::select("item_desc", "unit_weight", "unit_length", "unit_width", "unit_height")
            ->where("item_num", $item)->first();


        //debug 1 == true  debug 0 == false
        //show the label instead
        if (config('dcapt.btadebug') == "1") {
            $file_ext = '.test';
            $input = $request->except('_token', 'datajson', 'hqty');
            $input['unit_width'] =  $desc->unit_width;
            $input['unit_height'] = $desc->unit_height;
            $input['unit_length'] = $desc->unit_length;
            $input['unit_weight'] = $desc->unit_weight;

            //dd($request, $input);
            return $this->LabelOutput($request, $input);
        } else {
            $file_ext = '.dat';
        }

        //requesting parameter without token and configuration
        $input = $request->except('_token', 'all', 'label_template', 'label_reel_type', 'datajson', 'hqty', 'pre_quantities');

        //  dd($input,$request);
        // dd($lpns);
        //$time = explode(" ",$input['trans_date']);
        $datetime = Timezone::convertFromUTC(now(), auth()->user()->getSiteTimezone(), SiteSetting::getOutputDateFormat() . ' H:i:s');

        if ($request->creation_date != null) {
            $createdDate = $request->creation_date;
            //$createdDate = Timezone::convertFromUTC($request->creation_date, auth()->user()->getSiteTimezone(), SiteSetting::getOutputDateFormat() . ' H:i:s');
            $pallet_creation_date = Timezone::convertFromUTC($request->pallet_creation_date, auth()->user()->getSiteTimezone(), SiteSetting::getOutputDateFormat() . ' H:i:s');

            $request->merge([
                'creation_date' => $createdDate,
                'pallet_creation_date' => $pallet_creation_date,
                "unit_weight" => $desc->unit_weight ?? "",
                "unit_length" => $desc->unit_length ?? "",
                "unit_width" => $desc->unit_width ?? "",
                "unit_height" => $desc->unit_height ?? "",

            ]);
            $input['creation_date'] =  $createdDate;
            $input['pallet_creation_date'] =  $pallet_creation_date;
        }

        //requesting configuration and encode it to json
        $json = json_decode($request->datajson);
        // dd($request->all());
        //declaring integer for total box, number of box and label per box
        $maxbox = $boxnum = $labelperbox = 0;
        $count = 0;
        // check for the directory and file
        // path
        //adding value to total box
        foreach ($json as $key => $value) {
            $maxbox = $value->numofbox + $maxbox;
        }


        // For Bartender
        if (config('icapt.bartender')) {
            // timestamp on the file
            $ts = date('YmdHi', time());
            $filename = 'labelfile_' . $ts;
            $BTParams = '/D="%Trigger File Name%" /R=3 /P '; // BT Print parameters
            // Label output file
            $data_file = config('icapt.btwdatafolder') . $filename;   //'\\\\************\\LabelOutput\\';
            $labelTplpath = config('icapt.btwtemplatepath');  // label template
            // dd($data_file,$labelTplpath,$request);
            //looping the configuration
            unset($input['unit_width']);
            unset($input['unit_height']);
            unset($input['unit_weight']);
            unset($input['unit_length']);
            //dd($json,'ssss11',$input);

            foreach ($json as $key => $value) {
                $labelfilename = Label::select('file_name')->where('id', $value->labelname)->value('file_name');
                //iterate how many boxes are there if 4 boxes, will loop 4 ti mes
                $label = Label::find($value->labelname);
                $input['label_name'] = $label->label_name;
                for ($i = 1; $i <= $value->numofbox; $i++) {
                    $count++;
                    //auto increment for number of box
                    $boxnum++;
                    //convert the quantity to 2 decimal places
                    $qtyinbox = number_format((float) $value->quantityperbox, $quantity_per_format, '.', '');

                    //adding the number of box, quantity per box and total box
                    $input['boxnum'] = $boxnum;
                    $input['bn'] = $boxnum;
                    $input['qtyinbox'] = $qtyinbox;
                    $input['qib'] = $qtyinbox;
                    $input['qty'] = $qtyinbox;
                    $input['totalbox'] = $maxbox;
                    $input['tb'] = $maxbox;
                    $input['labelperbox'] = (int) $value->labelperbox;
                    $input['creator'] = Auth::user()->name;
                    $input['unit_width'] =  $desc->unit_width;
                    $input['unit_height'] = $desc->unit_height;
                    $input['unit_length'] = $desc->unit_length;
                    $input['unit_weight'] = $desc->unit_weight;
                    if (isset($input['item_image']) && $input['item_image'] != "") {
                        $item_image = $input['item_image'];
                        $imagedata = file_get_contents($item_image);
                        // alternatively specify an URL, if PHP settings allow
                        $item_image_data = base64_encode($imagedata);
                        $input['item_image'] = $item_image_data;
                    }
                    // add Creator to the label info
                    // dd($input);
                    $labelFile = $labelTplpath . "\\" . $labelfilename . "";
                    if (config('icapt.client_prefix') == "MSSB") {
                        $labelFile = config('icapt.mapbtwtemplatepath') . "\\" . $labelfilename . "";
                    }

                    $text = preg_replace("/\r|\n/", "", $input);
                    list($keys, $values) = array_divide($text);



                    if (isset($request->label_reel_type) && ($request->label_reel_type == "pre_print" || $request->label_reel_type == "add_process")) {
                        $reel_lot_num = $input['reel_lot'];
                        if ($request->label_reel_type == "pre_print") {
                            $reel_lot_num = $input['lot_num'];
                        }

                        $no_of_prints = (isset($request->reel_count) && $request->reel_count > 1) ? $request->reel_count : 1;
                        // dd($no_of_prints, $input);
                        for ($iI = 0; $iI < $no_of_prints; $iI++) {
                            $temp_count = $iI + 1;
                            $temp_count_format = sprintf("%02d", $temp_count);
                            $reel_lot = $reel_lot_num . "$temp_count_format";

                            // $reel_lot = $reel_lot_num . "0$temp_count";
                            $input['reel_lot'] = $reel_lot;
                            // dd($input);
                            $text = preg_replace("/\r|\n/", "", $input);
                            list($keys, $values) = array_divide($text);
                            $dattempfile = $filename . "_" . $reel_lot . "_" . $count . $file_ext;

                            $label_out_file = $data_file . "_" . $reel_lot . "_" . $count . $file_ext;
                            //dd($input);
                            $this->generatetBartenderfile($input, $value, $dattempfile, $labelFile, $label_out_file, $BTParams, $keys, $values);
                        }
                    } else {
                        $dattempfile = $filename . $count . $file_ext;
                        $label_out_file = $data_file . $count . $file_ext;
                        // dd($label,'ss');
                        //dd($input,'ssssqq');
                        /// $input['unit_width'] =  $desc->unit_width;
                        //$input['unit_height'] = $desc->unit_height;
                        // $input['unit_length'] = $desc->unit_length;
                        // $input['unit_weight'] = $desc->unit_weight;
                        //dd($input,'ssss');
                        $this->generatetBartenderfile($input, $value, $dattempfile, $labelFile, $label_out_file, $BTParams, $keys, $values);
                    }
                }
            }

            $input = $request->except('_token', 'datajson', 'hqty', 'boxnum', 'tb', 'bn', 'qib', 'qtyinbox', 'totalbox', 'ref');


            Session::put('print_status', 'Yes');
            if ($request->catch_weight) {
                return true;
            }
            return redirect()->route('backbutton');
        }

        $bartender_cloud_setting = \App\SiteConnection::where('site_id', auth()->user()->site_id)->where('active', 1)->where('type', 'simple')->where('connector', 'bartender_cloud')->first();
        if ($bartender_cloud_setting) {
            foreach ($json as $key => $value) {
                // dd($value);
                // $labelfilename = Label::select('file_name')->where('id', $value->labelname)->value('file_name');
                //iterate how many boxes are there if 4 boxes, will loop 4 ti mes
                // $label = Label::find($value->labelname);

                $input['label_name'] = $value->labelname;
                for ($i = 1; $i <= $value->numofbox; $i++) {
                    $count++;
                    //auto increment for number of box
                    $boxnum++;
                    //convert the quantity to 2 decimal places
                    $qtyinbox = number_format((float) $value->quantityperbox, $quantity_per_format, '.', '');

                    //adding the number of box, quantity per box and total box
                    $input['boxnum'] = $boxnum;
                    $input['bn'] = $boxnum;
                    $input['qtyinbox'] = $qtyinbox;
                    $input['qib'] = $qtyinbox;
                    $input['qty'] = $qtyinbox;
                    $input['totalbox'] = $maxbox;
                    $input['tb'] = $maxbox;
                    $input['labelperbox'] = (int) $value->labelperbox;
                    $input['creator'] = Auth::user()->name;
                    $input['unit_width'] =  $desc->unit_width;
                    $input['unit_height'] = $desc->unit_height;
                    $input['unit_length'] = $desc->unit_length;
                    $input['unit_weight'] = $desc->unit_weight;
                    // add Creator to the label info
                    // dd($input);
                    $text = preg_replace("/\r|\n/", "", $input);
                    list($keys, $values) = array_divide($text);

                    // dd($label,'ss');
                    BartenderCloudService::printLabel($input, $value, $keys, $values);
                }
            }
            $input = $request->except('_token', 'datajson', 'hqty', 'boxnum', 'tb', 'bn', 'qib', 'qtyinbox', 'totalbox', 'ref');

            Session::put('print_status', 'Yes');
            if ($request->catch_weight) {
                return true;
            }
            return redirect()->route('backbutton');
        }

        // dd($request,$input);

        //looping the configuration

        $labels = $testLabels = array();
        $height = 100;
        $width = 60;
        $margin_left = $margin_right = $margin_top = $margin_bottom = 0;
        $rows = 1;
        $columns = 1;
        $lpns = explode(PHP_EOL, $request->lpn_num);
        // dd($lpns);
        $lpn_count = 0;
        foreach ($json as $key => $value) {
            //iterate how many boxes are there if 4 boxes, will loop 4 times

            // foreach($lpns as $lpn){

            $lpn = $lpns[$lpn_count] ?? "";

            $input['lpn_num'] = $lpn;
            // dd($lpn, $lpn_count);
            $lpn_count++;
            for ($i = 1; $i <= $value->numofbox; $i++) {
                $count++;
                $boxnum++;

                //auto increment for number of box

                //convert the quantity to 2 decimal places
                // $qtyinbox = number_format((float) $value->quantityperbox, $quantity_per_format, '.', '');
                $qtyinbox = numberFormatPrecision($value->quantityperbox, false);

                //adding the number of box, quantity per box, total box, expiry date
                $input['boxnum'] = $boxnum;
                $input['bn'] = $boxnum;
                $input['qtyinbox'] = $qtyinbox;
                $input['qib'] = $qtyinbox;

                $input['totalbox'] = $maxbox;
                $input['tb'] = $maxbox;
                $input['labelperbox'] = (int) $value->labelperbox;
                $label = Label::find($value->labelname);
                $input['label_name'] = $label->label_name;

                // $label = Label::find($input['label']);
                $site_settings = SiteSetting::select('*')->where('site_id', auth()->user()->site_id)->first();

                //Convert time to local
                //$input['trans_date'] = Timezone::convertFromUTC($input['trans_date'], auth()->user()->timezone, SiteSetting::getOutputDateFormat());
                $input['trans_date'] = $datetime;

                if ($count == 1) {
                    $width = $label->width;
                    $height = $label->height;
                    $margin_left = $label->margin_left;
                    $margin_right = $label->margin_right;
                    $margin_top = $label->margin_top;
                    $margin_bottom = $label->margin_bottom;
                    $rows =  $label->rows;
                    $columns = $label->columns;
                }
                $template = $label->content;
                if ($label->content_html != "") {
                    $template = $label->content_html;
                }

                $text = preg_replace("/\r|\n/", "", $input);
                list($keys, $values) = Arr::divide($text);

                $placeholders = array();

                foreach ($keys as $key)
                    $placeholders[] = '%' . $key . '%';

                if (@$request->shipementID) {
                    $template = str_replace('%shipmentId%', $request->shipementID, $template);
                } else {
                    $template = str_replace('%shipmentId%', '', $template);
                }

                if (isset($request->po_num)) {
                    $template = str_replace('%po_num%', $request->po_num, $template);
                } else {
                    $template = str_replace('%po_num%', '', $template);
                }

                $dnsd = new DNS2D();
                if (isset($input['item_num'])) {
                    $item_qrcode = $dnsd->getBarcodeSVG($input['item_num'], "QRCODE", 40, 40, "black", true);
                }
                $loc_qrcode = "";
                if (isset($input['loc_num'])) {
                    $loc_qrcode = $dnsd->getBarcodeSVG($input['loc_num'], "QRCODE", 40, 40, "black", true);
                }

                if (isset($input['lot_num'])) {
                    $lot_num = $input['lot_num'];
                    $lot = Lot::where('lot_num', $lot_num)->first();
                    if ($lot) {
                        $template = str_replace('%lot_mfg_date%', $lot->mfg_date, $template);
                    }
                    $lot_qrcode = $dnsd->getBarcodeSVG($input['lot_num'], "QRCODE", 40, 40, "black", true);
                    $template = str_replace('%lot_qrcode%', '<img src="data:image/svg+xml;base64,' . base64_encode($lot_qrcode) . '" height="40px" />', $template);
                } else {
                    $template = str_replace('%lot_qrcode%', '', $template);
                }

                if (array_key_exists('job_num', $input)) {
                    $job_qrcode = $dnsd->getBarcodeSVG($input['job_num'], "QRCODE", 40, 40, "black", true);
                    $template = str_replace('%job_qrcode%', '<img src="data:image/svg+xml;base64,' . base64_encode($job_qrcode) . '" height="40px" />', $template);
                } else {
                    $template = str_replace('%job_qrcode%', '', $template);
                }

                if (array_key_exists('suffix', $input)) {
                    $suffix_qrcode = $dnsd->getBarcodeSVG($input['suffix'], "QRCODE", 40, 40, "black", true);
                    $template = str_replace('%suffix_qrcode%', '<img src="data:image/svg+xml;base64,' . base64_encode($suffix_qrcode) . '" height="40px" />', $template);
                } else {
                    $template = str_replace('%suffix_qrcode%', '', $template);
                }


                if (isset($input['vend_lot'])) {
                    $vend_lot_qrcode = $dnsd->getBarcodeSVG($input['vend_lot'], "QRCODE", 40, 40, "black", true);
                    $template = str_replace('%vendlot_qrcode%', '<img src="data:image/svg+xml;base64,' . base64_encode($vend_lot_qrcode) . '" height="40px" />', $template);
                } else {
                    $template = str_replace('%vendlot_qrcode%', '', $template);
                }

                if (array_key_exists('from_whse', $input)) {
                    $from_whse_qrcode = $dnsd->getBarcodeSVG($input['from_whse'], "QRCODE", 20, 20, "black", true);
                    $template = str_replace('%from_whse_qrcode%', '<img src="data:image/svg+xml;base64,' . base64_encode($from_whse_qrcode) . '" height="40px" />', $template);

                    if ($input['to_whse'] == null) {
                        $input['to_whse'] = " ";
                    }

                    if ($input['to_whse'] != null && $input['to_whse'] != " ") {
                        $to_whse_qrcode = $dnsd->getBarcodeSVG($input['to_whse'], "QRCODE", 20, 20, "black", true);
                        $template = str_replace('%to_whse_qrcode%', '<img src="data:image/svg+xml;base64,' . base64_encode($to_whse_qrcode) . '" height="40px" />', $template);
                    } else {
                        $to_whse_qrcode = $dnsd->getBarcodeSVG($input['to_whse'], "QRCODE", 20, 20, "black", true);
                        $template = str_replace('%to_whse_qrcode%', ' ' . ' ' . ' ', $template);
                    }
                } else {
                    $template = str_replace('%TO_qrcode%', '', $template);
                    $template = str_replace('%TO_line_qrcode%', '', $template);
                }

                if (array_key_exists('trn_num', $input)) {
                    $TO_qrcode = $dnsd->getBarcodeSVG($input['trn_num'], "QRCODE", 20, 20, "black", true);
                    $template = str_replace('%TO_qrcode%', '<img src="data:image/svg+xml;base64,' . base64_encode($TO_qrcode) . '" height="40px" />', $template);

                    $TO_line_qrcode = $dnsd->getBarcodeSVG($input['trn_line'], "QRCODE", 20, 20, "black", true);
                    $template = str_replace('%TO_line_qrcode%', '<img src="data:image/svg+xml;base64,' . base64_encode($TO_line_qrcode) . '" height="40px" />', $template);
                } else {
                    $template = str_replace('%TO_qrcode%', '', $template);
                    $template = str_replace('%TO_line_qrcode%', '', $template);
                }

                //Pallet
                if (isset($input['lpn_num']) && $input['lpn_num'] != "") {
                    $lpn_qrcode = $dnsd->getBarcodeSVG($input['lpn_num'], "QRCODE", 40, 40, "black", true);
                }
                $template = str_replace($placeholders, $values, $template);
                if ($input['transType'] == "Pallet") {
                    $template = str_replace('%lpn_qrcode%', '<img src="data:image/svg+xml;base64,' . base64_encode($lpn_qrcode) . '" height="40px"  />', $template);
                } else {
                    $template = str_replace('%item_qrcode%', '<img src="data:image/svg+xml;base64,' . base64_encode($item_qrcode) . '" height="40px"  />', $template);
                    $template = str_replace('%loc_qrcode%', '<img src="data:image/svg+xml;base64,' . base64_encode($loc_qrcode) . '" height="40px" />', $template);
                    $template = str_replace('%expiry_date%', $input['expiry_date'], $template);
                }


                $template = str_replace('%site_name%', $site_settings->site_name, $template);
                // $template = str_replace('%label_name%', $value->labelname, $template);
                $company_info = json_decode($site_settings->company_info, true);
                $company_name = isset($company_info['company_name']) ? $company_info['company_name'] : "";
                // dd($company_name);
                $template = str_replace('%company_name%', $company_name, $template);

                $template = str_replace('%print_date%', $datetime, $template);

                $template = str_replace('%trans_date%', $datetime, $template);

                $input['site_name'] = $site_settings->site_name;
                $input['print_date'] = $datetime;
                $input['company_name'] = $company_name;
                // dd($template);
                $template = QrcodeController::replacePlaceholders($template, $input);
                $template = QrcodeController::handleQrCode($template, $input);
                $template = QrcodeController::handleQrCodeImage($template, $input);
                $template = QrcodeController::handlPrimaryImage($template, $input);


                //Print each label per box
                $x = 1;
                while ($x <= $input['labelperbox']) {
                    $labels[] = $template;
                    $testLabels[] = $label;
                    $x++;
                }

                // Move to Label Output folder
                //$move = Storage::disk('s3')->move($dattempfile, $label_out_file);
                //LogActivity::addToLog('Print Label.', array($dattempfile, $labelFile), NULL);
            }
            // }


            Session::forget('alert');
            Session::forget('_flash');
        }
        // dd($testLabels, $labels);
        $print_node_setting = \App\SiteConnection::where('site_id', auth()->user()->site_id)->where('active', 1)->where('type', 'simple')->where('connector', 'print_node')->first();

        if ($print_node_setting) {
            foreach ($labels as $key => $labelHtml) {
                // $htmlView = preg_replace('/>\s+</', "><", $htmlView);
                $labelData = $testLabels[$key];

                $paper_size = $labelData->paper_size;
                $htmlView = view('Barcode.pdfhtml', compact('labelHtml', 'labelData'))->render();

                // echo $htmlView;
                // exit;
                // $htmlView="<h1>Test</h1>";
                $path = '/temp_prints';
                $time = strtotime("now");
                $fileName = $time . "_" . $input['label_name'] . "_" . auth()->user()->site_id . ".pdf";
                // if (file_exists($path . '/' . $fileName)) {
                //     unlink($path . '/' . $fileName);
                // }
                $filePath = storage_path("/app/$path" . "/" . $fileName);
                // dd($filePath);
                $dompdf = new Dompdf();


                $dompdf->setPaper(array(
                    0,
                    0,
                    ($label->width * $columns),
                    ($label->height * $rows)
                ), 'portrait');

                // // Set document information
                // // $dompdf->set_option('isPhpEnabled', true);
                $dompdf->loadHtml($htmlView);
                // // Render the PDF
                $dompdf->render();
                $output = $dompdf->output();

                // $dompdf->stream($path);
                Storage::put("$path" . "/" . $fileName, $output);

                // file_put_contents($filePath, $output);
                $printerId = $request->printer;
                try {
                    //code...
                    $apiKey = \PrintNode\Client::getAxacuteConnection($print_node_setting);
                    $credentials = new \PrintNode\Credentials\ApiKey($apiKey);
                    $client = new \PrintNode\Client($credentials);

                    $printJob = new \PrintNode\Entity\PrintJob($client);

                    $options = [
                        'paper' => "$paper_size",
                        'fit_to_page' => true,
                        // 'rotate'=>0,
                    ];
                    // $filePAth = public_path('YNInventory Label_AXA_TEST.pdf');
                    $printJob->title = $labelData->label_name;
                    $printJob->source = "AXACUTE-$labelData->site_name";
                    $printJob->printer = $printerId;
                    $printJob->contentType = 'pdf_base64';
                    $printJob->options = $options;
                    $printJob->addPdfFile($filePath);

                    $printJobId = $client->createPrintJob($printJob);
                    // dd($printJobId);
                    if (file_exists($filePath)) {
                        unlink($filePath);
                    }
                } catch (\Throwable $th) {
                    //throw $th;
                    $errorMsg = $th->getMessage();
                    $errorText = __('error.admin.print_node_error', ['errorText' => $errorMsg]);
                    Alert::html("Oops!", $errorText, 'error');
                    // throw ValidationException::withMessages($th->getMessage());
                }

                // dd("a");
                // // // Output the PDF as a download
                // $pdf = PDF::loadView('Barcode.pdfhtml', compact('labelHtml', 'width', 'margin_left', 'margin_right', 'margin_top', 'margin_bottom', 'height', 'rows', 'columns'))
                //     ->setOption('page-width', $label->width . "mm")
                //     ->setOption('page-height', $label->height . "mm");
                // //  ->setOption('margins', 0);
                // $pdf->save($path . '/' . $fileName);
                // $pdf->stream();
            }
            $input = $request->except('_token', 'datajson', 'hqty', 'boxnum', 'tb', 'bn', 'qib', 'qtyinbox', 'totalbox', 'ref');

            Session::put('print_status', 'Yes');
            if ($request->catch_weight) {
                return true;
            }
            return redirect()->route('backbutton');


            // dd($pdf);
        }
        if ($request->catch_weight) {
            return true;
        }


        // dd($testLabels);
        return view('Barcode.htmltemplate', compact("labels"))
            ->with('width', $width)
            ->with('margin_left', $margin_left)
            ->with('margin_right', $margin_right)
            ->with('margin_top', $margin_top)
            ->with('margin_bottom', $margin_bottom)
            ->with('height', $height)->with('rows', $rows)->with('columns', $columns);
        // $pdf = PDF::loadView('Barcode.template', compact("labels"))
        //     ->setOption('page-width', $width)->setOption('page-height', $height);
        // return $pdf->stream();
    }


    public function generatetBartenderfile($input, $label, $dattempfile, $labelFile, $label_out_file, $BTParams, $keys, $values)
    {
        // dd($input);
        $printer = $input['printer'] ?? $label->printer;
        // dd($printer);
        Storage::disk('local')->put($dattempfile, '%BTW% /AF="' . $labelFile . '"' . '/PRN="' . $printer . '"' .
            $BTParams . ' /C=' . $input['labelperbox'] . "\r\n" . '%END%' . "\r\n" . '' . implode('|', $keys) . "\r\n" . '' . implode('|', $values) . '');
        // Move to Label Output folder
        if (copy(Storage::disk('local')->path($dattempfile), $label_out_file)) {
            // delete the temp file
            Storage::delete($dattempfile);
        }
        //   if(Storage::copy($dattempfile,$label_out_file)){
        //     Storage::delete($dattempfile);
        // }
        LogActivity::addToLog('Print Label.', array($dattempfile, $labelFile, $printer), NULL);
    }
    public function PrintMultiLabelProcess(Request $request)
    {
        $tparm = new TparmView;
        $quantity_per_format = $tparm->getTparmValue('System', 'decimal_setting');
        //dd($request);
        // $arr = [1,2];
        // $arrPrint = ['1'=>array('1'=>'222','2'=>'eeee'),'2'=>array('1'=>'eeerrr','2'=>'3dddd','3'=>'eededed')];
        // $arrrr = array();
        // foreach($arr as $k =>$v)
        // {
        //     foreach($arrPrint[$v] as $key => $valued)
        //     {
        //         echo $valued;
        //         echo "<br>";
        //         $arrrr[] = $valued;
        //     }
        // }
        // exit;
        $desc = Item::select("item_desc", "unit_weight", "unit_length", "unit_width", "unit_height")
            ->where("item_num", $request->item_num)->first();

        $request->merge([
            "unit_weight" => $desc->unit_weight ?? "",
            "unit_length" => $desc->unit_length ?? "",
            "unit_width" => $desc->unit_width ?? "",
            "unit_height" => $desc->unit_height ?? "",

        ]);


        if (config('dcapt.btadebug') == "1") {
            $input = $request->except('_token', 'datajson', 'hqty');
            return $this->LabelOutput($request, $input);
        }



        //requesting parameter without token and configuration
        $input = $request->except('_token', 'datajson', 'hqty', 'shipementID');
        //$time = explode(" ",$input['trans_date']);
        $datetime = Timezone::convertFromUTC(now(), auth()->user()->getSiteTimezone(), SiteSetting::getOutputDateFormat() . ' H:i:s');
        $arrSet = array();
        // dd($input);

        $bartender_cloud_setting = \App\SiteConnection::where('site_id', auth()->user()->site_id)->where('active', 1)->where('type', 'simple')->where('connector', 'bartender_cloud')->first();
        foreach ($input as $key => $valeu) {

            if ($key == "job_num" || $key == "suffix" || $key == "oper_num" || $key == "sequence" ||  $key == "co_num" || $key == "co_line" || $key == "lot_num" || $key == "qty" || $key == "uom" || $key == "expiry_date" || $key == "trans_date" || $key == "whse_num" || $key == "cust_num" || $key == "cust_name" || $key == "item_num" || $key == "item_desc" || $key == "cust_item_code" || $key == "loc_num") {
                foreach ($valeu as $iTemKey => $val) {
                    $descloop = Item::select("item_desc", "unit_weight", "unit_length", "unit_width", "unit_height")
                        ->where("item_num", $input['item_num'][$iTemKey])->first();

                    @$arrSet[$iTemKey]['job_num'] = $input['job_num'][$iTemKey];
                    @$arrSet[$iTemKey]['suffix'] = $input['suffix'][$iTemKey];
                    @$arrSet[$iTemKey]['oper_num'] = $input['oper_num'][$iTemKey];
                    @$arrSet[$iTemKey]['sequence'] = $input['sequence'][$iTemKey];

                    @$arrSet[$iTemKey]['co_num'] = $input['co_num'][$iTemKey];
                    @$arrSet[$iTemKey]['co_line'] = $input['co_line'][$iTemKey];
                    @$arrSet[$iTemKey]['loc_num'] = $input['loc_num'][$iTemKey];
                    @$arrSet[$iTemKey]['lot_num'] = $input['lot_num'][$iTemKey];
                    @$arrSet[$iTemKey]['qty'] = $input['qty'][$iTemKey];
                    @$arrSet[$iTemKey]['uom'] = $input['uom'][$iTemKey];
                    @$arrSet[$iTemKey]['expiry_date'] = $input['expiry_date'][$iTemKey];
                    @$arrSet[$iTemKey]['trans_date'] = $datetime;
                    @$arrSet[$iTemKey]['whse_num'] = $input['whse_num'][$iTemKey];
                    @$arrSet[$iTemKey]['cust_num'] = $input['cust_num'][$iTemKey];
                    @$arrSet[$iTemKey]['cust_name'] = $input['cust_name'][$iTemKey];
                    @$arrSet[$iTemKey]['item_num'] = $input['item_num'][$iTemKey];
                    @$arrSet[$iTemKey]['item_desc'] = $input['item_desc'][$iTemKey];
                    // dd($input);

                    // @$arrSet[$iTemKey]['unit_weight'] = $input['unit_weight'][$iTemKey]??"";
                    // @$arrSet[$iTemKey]['unit_length'] = $input['unit_length'][$iTemKey] ?? "";
                    // @$arrSet[$iTemKey]['unit_width'] = $input['unit_width'][$iTemKey] ?? "";
                    // @$arrSet[$iTemKey]['unit_height'] = $input['unit_height'][$iTemKey] ?? "";

                    @$arrSet[$iTemKey]['unit_weight'] = $descloop->unit_weight ?? "";
                    @$arrSet[$iTemKey]['unit_length'] = $descloop->unit_length ?? "";
                    @$arrSet[$iTemKey]['unit_width']  = $descloop->unit_width ?? "";
                    @$arrSet[$iTemKey]['unit_height'] = $descloop->unit_height ?? "";


                    @$arrSet[$iTemKey]['cust_item_code'] = $input['cust_item_code'][$iTemKey];
                }
            }
        }

        //requesting configuration and encode it to json
        $json = json_decode($request->datajson);
        // dd($json);
        //declaring integer for total box, number of box and label per box
        $maxbox = $boxnum = $labelperbox = 0;
        $count = 0;
        // check for the directory and file
        // path
        // $arrMaxbox = array();
        //adding value to total box
        foreach ($json as $key => $value) {

            // $maxbox = $value->numofbox + $maxbox;
            $arrMaxbox[$value->Item] = $value->numofbox;
        }



        //looping the configuration
        $labels = array();
        $height = 100;
        $width = 60;

        foreach ($arrMaxbox as $itemId => $ItemValue) {

            $boxnum = 1;
            foreach ($json as $key => $value) {

                if ($itemId == $value->Item) {

                    //iterate how many boxes are there if 4 boxes, will loop 4 times
                    for ($i = 1; $i <= $value->numofbox; $i++) {

                        $count++;
                        //auto increment for number of box
                        //convert the quantity to 2 decimal places
                        $qtyinbox = numberFormatPrecision($value->quantityperbox, false);
                        $text = preg_replace("/\r|\n/", "", $arrSet[$itemId]);
                        list($keys, $values) = Arr::divide($text);
                        // $qtyinbox = number_format((float) $value->quantityperbox, $quantity_per_format, '.', '');
                        //adding the number of box, quantity per box, total box, expiry date
                        if ($value->numofbox == 1) {
                            $arrSet[$itemId]['boxnum'] = ($boxnum + $value->numofbox) - $value->numofbox;
                            $arrSet[$itemId]['bn'] = ($boxnum + $value->numofbox) - $value->numofbox;
                        } else {
                            $arrSet[$itemId]['boxnum'] = $i;
                            $arrSet[$itemId]['bn'] = $i;
                        }

                        $arrSet[$itemId]['qtyinbox'] = $qtyinbox;
                        $arrSet[$itemId]['qib'] = $qtyinbox;

                        $arrSet[$itemId]['tb'] = $value->numofbox;
                        $arrSet[$itemId]['totalbox'] = $value->numofbox;
                        $arrSet[$itemId]['labelperbox'] = (int) $value->labelperbox;
                        // dd($arrSet[$itemId]);
                        $arrSet[$itemId]['printer'] = $request->printer;

                        if ($bartender_cloud_setting) {
                            BartenderCloudService::printLabel($arrSet[$itemId], $value, $keys, $values);
                        } else {
                            if ($value->numofbox == 1) {
                                $input['boxnum'][$itemId] = ($boxnum + $value->numofbox) - $value->numofbox;
                                $input['bn'][$itemId] = ($boxnum + $value->numofbox) - $value->numofbox;
                            } else {
                                $input['boxnum'][$itemId] = $i;
                                $input['bn'][$itemId] = $i;
                            }

                            $input['qtyinbox'][$itemId] = $qtyinbox;
                            $input['qib'][$itemId] = $qtyinbox;
                            $input['totalbox'][$itemId] = (int)$value->numofbox;
                            $input['tb'][$itemId] = (int)$value->numofbox;
                            $input['creator'][$itemId] = Auth::user()->name;
                            $input['trans_date'][$itemId] = $datetime;

                            // dd($input);

                            // add C
                            $label = Label::find($value->labelname);
                            //  $label = Label::find($value->labelname);
                            $arrSet[$itemId]['label_name'] = $label->label_name;
                            $site_settings = SiteSetting::select('*')->where('site_id', auth()->user()->site_id)->first();

                            //Convert time to local
                            //$input['trans_date'] = Timezone::convertFromUTC($input['trans_date'], auth()->user()->timezone, SiteSetting::getOutputDateFormat());

                            if ($count == 1) {
                                $width = $label->width;
                                $height = $label->height;
                                $margin_left = $label->margin_left;
                                $margin_right = $label->margin_right;
                                $margin_top = $label->margin_top;
                                $margin_bottom = $label->margin_bottom;
                                $rows =  $label->rows;
                                $columns = $label->columns;
                            }

                            $template[$itemId] = $label->content;
                            if ($label->content_html != "") {
                                $template[$itemId] = $label->content_html;
                            }

                            // $template[$itemId] = $label->content;
                            // dd($template[$itemId]);
                            // dd($arrSet[$itemId]);



                            $placeholders = array();

                            foreach ($keys as $key)
                                $placeholders[] = '%' . $key . '%';
                            if (@$request->shipementID) {
                                $template[$itemId] = str_replace('%shipmentId%', $request->shipementID, $template[$itemId]);
                            } else {
                                $template[$itemId] = str_replace('%shipmentId%', '', $template[$itemId]);
                            }
                            // dd($placeholders);

                            $dnsd = new DNS2D();

                            $item_qrcode = $dnsd->getBarcodeSVG($input['item_num'][$itemId], "QRCODE", 40, 40, "black", true);
                            $loc_qrcode = $dnsd->getBarcodeSVG($input['loc_num'][$itemId], "QRCODE", 40, 40, "black", true);

                            if ($input['lot_num'][$itemId]) {
                                $lot_num = $input['lot_num'][$itemId];
                                $lot = Lot::where('lot_num', $lot_num)->first();
                                if ($lot) {
                                    $template[$itemId]  = str_replace('%lot_mfg_date%', $lot->mfg_date, $template[$itemId]);
                                }
                                $lot_qrcode = $dnsd->getBarcodeSVG($input['lot_num'][$itemId], "QRCODE", 40, 40, "black", true);
                                $template[$itemId] = str_replace('%lot_qrcode%', '<img src="data:image/svg+xml;base64,' . base64_encode($lot_qrcode) . '" height="40px" />', $template[$itemId]);
                            } else {
                                $template[$itemId] = str_replace('%lot_qrcode%', '', $template[$itemId]);
                            }

                            if (array_key_exists('job_num', $input)) {
                                $job_qrcode = $dnsd->getBarcodeSVG($input['job_num'][$itemId], "QRCODE", 40, 40, "black", true);
                                $template[$itemId] = str_replace('%job_qrcode%', '<img src="data:image/svg+xml;base64,' . base64_encode($job_qrcode) . '" height="40px" />', $template[$itemId]);
                            } else {
                                $template[$itemId] = str_replace('%job_qrcode%', '', $template[$itemId]);
                            }

                            if (isset($input['vend_lot'][$itemId])) {
                                $vend_lot_qrcode = $dnsd->getBarcodeSVG($input['vend_lot'][$itemId], "QRCODE", 40, 40, "black", true);
                                $template[$itemId] = str_replace('%vendlot_qrcode%', '<img src="data:image/svg+xml;base64,' . base64_encode($vend_lot_qrcode) . '" height="40px" />', $template[$itemId]);
                            } else {
                                $template[$itemId] = str_replace('%vendlot_qrcode%', '', $template[$itemId]);
                            }

                            if (array_key_exists('from_whse', $arrSet[$itemId])) {
                                $from_whse_qrcode = $dnsd->getBarcodeSVG($input['from_whse'][$itemId], "QRCODE", 20, 20, "black", true);
                                $template[$itemId] = str_replace('%from_whse_qrcode%', '<img src="data:image/svg+xml;base64,' . base64_encode($from_whse_qrcode) . '" height="40px" />', $template[$itemId]);

                                if ($input['to_whse'][$itemId] == null) {
                                    $input['to_whse'][$itemId] = " ";
                                }

                                if ($input['to_whse'][$itemId] != null && $input['to_whse'][$itemId] != " ") {
                                    $to_whse_qrcode = $dnsd->getBarcodeSVG($input['to_whse'][$itemId], "QRCODE", 20, 20, "black", true);
                                    $template[$itemId] = str_replace('%to_whse_qrcode%', '<img src="data:image/svg+xml;base64,' . base64_encode($to_whse_qrcode) . '" height="40px" />', $template[$itemId]);
                                } else {
                                    $to_whse_qrcode = $dnsd->getBarcodeSVG($input['to_whse'][$itemId], "QRCODE", 20, 20, "black", true);
                                    $template[$itemId] = str_replace('%to_whse_qrcode%', ' ' . ' ' . ' ', $template[$itemId]);
                                }
                            } else {
                                $template[$itemId] = str_replace('%TO_qrcode%', '', $template[$itemId]);
                                $template[$itemId] = str_replace('%TO_line_qrcode%', '', $template[$itemId]);
                            }

                            if (array_key_exists('trn_num', $arrSet[$itemId])) {
                                $TO_qrcode = $dnsd->getBarcodeSVG($input['trn_num'][$itemId], "QRCODE", 20, 20, "black", true);
                                $template[$itemId] = str_replace('%TO_qrcode%', '<img src="data:image/svg+xml;base64,' . base64_encode($TO_qrcode) . '" height="40px" />', $template[$itemId]);

                                $TO_line_qrcode = $dnsd->getBarcodeSVG($input['trn_line'][$itemId], "QRCODE", 20, 20, "black", true);
                                $template[$itemId] = str_replace('%TO_line_qrcode%', '<img src="data:image/svg+xml;base64,' . base64_encode($TO_line_qrcode) . '" height="40px" />', $template[$itemId]);
                            } else {
                                $template[$itemId] = str_replace('%TO_qrcode%', '', $template[$itemId]);
                                $template[$itemId] = str_replace('%TO_line_qrcode%', '', $template[$itemId]);
                            }

                            $template[$itemId] = str_replace($placeholders, $values, $template[$itemId]);
                            $template[$itemId] = str_replace('%item_qrcode%', '<img src="data:image/svg+xml;base64,' . base64_encode($item_qrcode) . '" height="40px"  />', $template[$itemId]);
                            $template[$itemId] = str_replace('%loc_qrcode%', '<img src="data:image/svg+xml;base64,' . base64_encode($loc_qrcode) . '" height="40px" />', $template[$itemId]);

                            $template[$itemId] = str_replace('%expiry_date%', $input['expiry_date'][$itemId], $template[$itemId]);
                            $template[$itemId] = str_replace('%site_name%', $site_settings->site_name, $template[$itemId]);
                            $template[$itemId] = str_replace('%label_name%', $label->label_name, $template[$itemId]);
                            $company_info = json_decode($site_settings->company_info, true);
                            $company_name = isset($company_info['company_name']) ? $company_info['company_name'] : "";
                            $template[$itemId] = str_replace('%company_name%', $company_name, $template[$itemId]);

                            $template[$itemId] = str_replace('%print_date%', $datetime, $template[$itemId]);

                            $template[$itemId] = str_replace('%trans_date%', $datetime, $template[$itemId]);

                            $input['site_name'] = $site_settings->site_name;
                            $input['label_name'] = $label->label_name;
                            $input['print_date'] = $datetime;
                            $input['company_name'] = $company_name;


                            // dd($template[$itemId]);
                            $template[$itemId]  = QrcodeController::replacePlaceholders($template[$itemId], $input, $itemId);

                            $template[$itemId]  = QrcodeController::handleQrCode($template[$itemId], $input, $itemId);
                            $template[$itemId]  = QrcodeController::handleQrCodeImage($template[$itemId], $input, $itemId);
                            $template[$itemId]  = QrcodeController::handlPrimaryImage($template[$itemId], $input, $itemId);

                            //Print each label per box
                            $x = 1;
                            while ($x <= $arrSet[$itemId]['labelperbox']) {
                                $labels[] = $template[$itemId];
                                $x++;
                                $boxnum++;
                            }
                        }

                        // dd($input, $template);
                        // Move to Label Output folder
                        //$move = Storage::disk('s3')->move($dattempfile, $label_out_file);
                        //LogActivity::addToLog('Print Label.', array($dattempfile, $labelFile), NULL);
                    }
                }

                Session::forget('alert');
                Session::forget('_flash');
            }
        }
        // dd("a");
        if ($bartender_cloud_setting) {

            Session::put('print_status', 'Yes');
            return redirect()->route('backbutton');
        }
        return view('Barcode.htmltemplatemultiple', compact("labels"))->with('width', $width)
            ->with('height', $height)
            ->with('margin_left', $margin_left)
            ->with('margin_right', $margin_right)
            ->with('margin_top', $margin_top)
            ->with('margin_bottom', $margin_bottom)->with('rows', $rows)->with('columns', $columns);
    }

    /**
     * TO
     * Misc Issue/Receipt/Putaway/Move Label
     * <AUTHOR> Firdaus
     * @param $whse, $loc, $item, $lot, $qty, $transDate, $transType
     */
    public static function GetInventoryLabelData($whse, $loc, $item, $lot, $qty, $uom, $document, $expiry_date, $transDate, $transType)
    {
        //create reference number
        if ($lot != null) {
            $ref = $whse . "-" . $loc . "-" . $item . "-" . $lot;
        } else {
            $ref = $whse . "-" . $loc . "-" . $item;
        }

        // dd($transDate);
        //if transaction date is null
        if ($transDate == null) {
            $transDate = getDateTimeConverted();

            // $transDate = Carbon::now()->toDateString();
        }

        //search Item for item description
        $desc = Item::select("item_desc", "unit_weight", "unit_length", "unit_width", "unit_height")
            ->where("item_num", $item)->first();

        //assigning keys to values
        $input = array(
            "whse_num" => $whse,
            "loc_num" => $loc,
            "item_num" => $item,
            "lot_num" => $lot,
            "qty" => $qty,
            "uom" => $uom,
            "trans_date" => $transDate,
            "transType" => $transType,
            "item_desc" => $desc->item_desc ?? "",
            "expiry_date" => $expiry_date ?? null,
            "ref" => $ref,
            "document" => $document,
            "document_num" => $document,
            "unit_weight" => $desc->unit_weight ?? "",
            "unit_length" => $desc->unit_length ?? "",
            "unit_width" => $desc->unit_width ?? "",
            "unit_height" => $desc->unit_height ?? "",
        );
        $input = self::getLpnDetail($input);
        $input = self::getItemImage($input);
        //return to handler function
        return $input;
    }

    /**
     * CO
     * CO Shipping/Picking/Pick&Ship Label
     * <AUTHOR> Firdaus
     * @param $conum, $coline, $corelease, $lot, $qty, $transDate, $transType
     */
    public static function GetCustOrdLabelDataMuplti($conum, $coline, $corelease, $qty, $uom, $whse, $loc, $lot, $expiry_date, $uniquekey, $transDate, $transType)
    {
        //create reference number
        $ref = $conum . "-" . $coline . "-" . $corelease;

        //if transaction date is null
        if ($transDate == null) {
            $transDate = getDateTimeConverted();

            // $transDate = Carbon::now()->toDateString();
        }

        //select coitem for customer number, customer name, shipto name and shipto address
        $coitem = CustomerOrderItem::where("co_num", $conum)
            ->where("co_line", $coline)
            // ->where("co_rel", $corelease)
            ->first();

        $co = CustomerOrder::where("co_num", $conum)
            // ->where("co_line", $coline)
            // ->where("co_rel", $corelease)
            ->first();
        // dd($co);
        $customer_address = "";
        if ($co) {
            $customer_address = $co->add_num;
        }
        if ($coitem) {
            $customer = Customer::where('cust_num', $coitem->cust_num)->first();
        }

        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

        if ($sap_trans_order_integration == 1 && config('icapt.enable_cust_name_notfrom_cust_table') == true) {
            $cust_name = $coitem->cust_name;
        } else {
            $cust_name = $customer->cust_name;
        }

        // dd($cust_name);


        //assigning keys to values
        $input = array(
            "co_num" => $conum,
            "co_line" => $coline,
            // "co_rel" => $corelease,
            "lot_num" => $lot,
            "qty" => $qty,
            "uom" => $uom,
            "expiry_date" => $expiry_date[$uniquekey] ?? null,
            "trans_date" => $transDate,
            "transType" => $transType,
            "whse_num" => $whse,
            "loc_num" => $loc,
            "lot_num" => $lot,
            "cust_num" => $coitem->cust_num ?? null,
            "cust_name" => $cust_name,
            "cust_address" => "$customer_address",
            "item_num" => $coitem->item_num ?? null,
            "item_desc" => $coitem->item_desc ?? null,
            "cust_item_code" => $coitem->cust_item ?? null,
            "ref" => $ref
        );
        // dd($input,"Lalala-Multi");
        //return to handler function
        $input = self::getLpnDetail($input);
        $input = self::getItemImage($input);
        return $input;
    }



    public static function GetCustReturnLabelDataMuplti($crnum, $crline,  $qty, $uom, $whse, $loc, $lot, $expiry_date, $uniquekey, $transDate, $transType)
    {
        //create reference number
        $ref = $crnum . "-" . $crline;

        //if transaction date is null
        if ($transDate == null) {
            $transDate = getDateTimeConverted();

            // $transDate = Carbon::now()->toDateString();
        }

        //select coitem for customer number, customer name, shipto name and shipto address
        $crlines = CustomerReturnLine::where("return_num", $crnum)
            ->where("return_line", $crline)
            // ->where("co_rel", $corelease)
            ->first();

        $cr = CustomerReturn::where("return_num", $crnum)
            // ->where("co_line", $coline)
            // ->where("co_rel", $corelease)
            ->first();
        // dd($co);
        $customer_address = "";
        if ($cr) {
            $customer_address = $cr->add_num;
        }
        if ($crlines) {
            $customer = Customer::where('cust_num', $crlines->cust_num)->first();
        }

        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

        if ($sap_trans_order_integration == 1 && config('icapt.enable_cust_name_notfrom_cust_table') == true) {
            $cust_name = $cr->cust_name;
        } else {
            $cust_name = $cr->cust_name;
        }

        // dd($cust_name);


        //assigning keys to values
        $input = array(
            "return_num" => $crnum,
            "return_line" => $crline,
            // "co_rel" => $corelease,
            "lot_num" => $lot,
            "qty" => $qty,
            "uom" => $uom,
            "expiry_date" => $expiry_date ?? null,
            "trans_date" => $transDate,
            "transType" => $transType,
            "whse_num" => $whse,
            "loc_num" => $loc,
            "lot_num" => $lot,
            "cust_num" => $cr->cust_num ?? null,
            "cust_name" => $cust_name,
            "cust_address" => "$customer_address",
            "item_num" => $crlines->item_num ?? null,
            "item_desc" => $crlines->item_desc ?? null,
            "cust_item_code" => $crlines->cust_item ?? null,
            "ref" => $ref
        );
        // dd($input,"Lalala-Multi");
        //return to handler function
        $input = self::getLpnDetail($input);
        $input = self::getItemImage($input);

        //dd($input);
        return $input;
    }






    /**
     * CO
     * CO Shipping/Picking/Pick&Ship Label
     * <AUTHOR> Firdaus
     * @param $conum, $coline, $corelease, $lot, $qty, $transDate, $transType
     */
    public static function GetCustOrdLabelData($conum, $coline, $corelease, $qty, $uom, $whse, $loc, $lot, $expiry_date, $transDate, $transType)
    {
        //create reference number
        $ref = $conum . "-" . $coline . "-" . $corelease;

        //if transaction date is null
        if ($transDate == null) {
            $transDate = getDateTimeConverted();

            // $transDate = Carbon::now()->toDateString();
        }

        $co = CustomerOrder::where("co_num", $conum)
            // ->where("co_line", $coline)
            // ->where("co_rel", $corelease)
            ->first();
        // dd($co);
        $customer_address = "";
        if ($co) {
            $customer_address = $co->add_num;
        }
        //select coitem for customer number, customer name, shipto name and shipto address
        $coitem = CustomerOrderItem::where("co_num", $conum)
            ->where("co_line", $coline)
            // ->where("co_rel", $corelease)
            ->first();
        $desc = Item::select("item_desc", "unit_weight", "unit_length", "unit_width", "unit_height")
            ->where("item_num", $coitem->item_num)->first();

        if ($coitem->cust_num != null) {
            $customer = Customer::where('cust_num', $coitem->cust_num)->first();
        }
        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

        if ($sap_trans_order_integration == 1 && config('icapt.enable_cust_name_notfrom_cust_table') == true) {
            $cust_name = $coitem->cust_name;
        } else {
            $cust_name = $customer->cust_name;
        }

        //assigning keys to values
        $input = array(
            "co_num" => $conum,
            "co_line" => $coline,
            // "co_rel" => $corelease,
            "lot_num" => $lot,
            "qty" => $qty,
            "uom" => $uom,
            "expiry_date" => $expiry_date ?? null,
            "trans_date" => $transDate,
            "transType" => $transType,
            "whse_num" => $whse,
            "loc_num" => $loc,
            "lot_num" => $lot,
            "cust_num" => $coitem->cust_num,
            "cust_name" =>  $cust_name,
            "cust_address" => "$customer_address",
            "item_num" => $coitem->item_num,
            "item_desc" => $coitem->item_desc,
            "cust_item_code" => $coitem->cust_item,
            "ref" => $ref,
            "unit_weight" => $desc->unit_weight ?? "",
            "unit_length" => $desc->unit_length ?? "",
            "unit_width" => $desc->unit_width ?? "",
            "unit_height" => $desc->unit_height ?? "",
        );
        //  dd($input,"Lalala");
        //return to handler function
        $input = self::getLpnDetail($input);
        $input = self::getItemImage($input);
        // dd($input);
        return $input;
    }

    /**
     * JO
     * Job Receipt Transaction Label
     * <AUTHOR> Firdaus
     * @param $jobnum, $jobsuffix, $lot, $qty, $transDate, $transType, $loc
     */
    public static function GetJobReceiptLabelData($jobnum, $suffix, $item_num, $item_desc, $qty, $uom, $loc_num, $lot_num, $expiry_date, $transDate, $document_num, $transType)
    {

        //if transaction date is null
        if ($transDate == null) {
            $transDate = Carbon::now()->format('d.m.Y H.i.sA');
        }

        //select job for item and description
        $job = Job::select("job_num", "item_num", "uom", "field_2", "field_3", "field_9", "field_10", "whse_num")
            ->where("job_num", $jobnum)
            ->first();

        $input = array(
            "job_num" => $job->job_num,
            "suffix" => $suffix,
            "loc_num" => $loc_num,
            "lot_num" => $lot_num,
            "qty" => $qty,
            "uom" => $job->uom,
            "expiry_date" => $expiry_date ?? null,
            "trans_date" => $transDate,
            "transType" => $transType,
            "item_num" => $job->item_num,
            "item_desc" => $item_desc,
            "document_num" => $document_num
        );
        $input = self::getLpnDetail($input);
        $input = self::getItemImage($input);
        //return to handler function
        return $input;
    }

    /**
     * JO
     * Job Material Issue Label
     * <AUTHOR> Firdaus
     * @param $jobnum, $jobsuffix, $operation, $itemcode, $qty, $transDate, $transType, $loc, $lot
     */
    public static function GetJobMatLabelData($jobnum, $suffix, $sequence, $operation, $matlitem, $qty, $uom, $whse_num, $loc_num, $lot_num, $expiry_date, $transDate, $transType)
    {
        if ($transDate == null) {
            // $transDate = Carbon::now()->toDateString();
            $transDate = getDateTimeConverted();
        }

        $desc = Item::select("item_desc", "unit_weight", "unit_length", "unit_width", "unit_height")
            ->where("item_num", $matlitem)->first();
        //select SLJob for item and description
        $job = JobMatl::select("matl_desc")
            ->where("job_num", $jobnum)
            ->where("matl_item", $matlitem)
            ->where("oper_num", $operation)
            ->where("sequence", $sequence)
            ->first();

        $input = array(
            "job_num" => $jobnum,
            "suffix" => $suffix,
            "operation" => $operation,
            'oper_num' => $operation,
            "item_num" => $matlitem,
            "sequence" => NULL,
            "qty" => $qty,
            "expiry_date" => $expiry_date ?? null,
            "trans_date" => $transDate,
            "transType" => $transType,
            "uom" => $uom,
            "whse_num" => $whse_num,
            "lot_num" => $lot_num,
            "loc_num" => $loc_num,
            "job_item" => '',
            "job_item_desc" => '',
            "item_desc" => $job->matl_desc,
            "wc" => '',
            "wcname" => '',
            'label_template' => 'jobs',
            'um' => '', //  add UM.
            "unit_weight" => $desc->unit_weight ?? "",
            "unit_length" => $desc->unit_length ?? "",
            "unit_width" => $desc->unit_width ?? "",
            "unit_height" => $desc->unit_height ?? "",

        );
        $input = self::getLpnDetail($input);
        $input = self::getItemImage($input);
        return $input;
        //return to handler function
        //return $input;
    }

    /**
     * PO
     * PO Receive Label
     * <AUTHOR> Firdaus
     * @param $ponum, $poline, $porelease, $qty, $whse, $transDate, $transType, $loc, $lot, $vendlot
     */
    public static function GetPOReceiveLabelData($ponum, $poline, $qty, $whse, $transDate, $transType, $loc, $lot, $expiry_date, $vendlot, $uom = '')
    {
        //create reference number
        // $ref = $ponum . "-" . $poline . "-" . $porelease;
        $ref = $ponum . "-" . $poline;
        $pre_quantities = [];

        //if transaction posted null on transaction date
        if ($transDate == null) {
            // $transDate = Carbon::now()->toDateString();
            $transDate = getDateTimeConverted();
        }

        //search SLPO for vendor number and vendor name
        //        $po = po_item::where("po_num", $ponum)->first();
        //        po_item got no vend name hence change to po
        $po = \App\PurchaseOrder::where("po_num", $ponum)->first();

        //        dd($po);
        //search SLPOItem for item and description
        $poitem = PurchaseOrderItem::where("po_num", $ponum)
            ->where("po_line", $poline)
            // ->where("po_rel", $porelease)
            ->first();

        //search Item for item description
        $desc = Item::select("item_desc", "unit_weight", "unit_length", "unit_width", "unit_height")
            ->where("item_num", $poitem->item_num)->first();
        if (is_null($poitem)) {
            //invalid PO Line
            // set Error Message
            session('error', 'Invalid PO Number/Line/Release. PO Item not found.');
            return back();
        }
        if (isset($_REQUEST['qty_rcv'])) {
            $pre_quantities = $_REQUEST['qty_rcv'];
            $qty = 0;
        }
        //assigning keys to values
        $input = array(
            "po_num" => $ponum,
            "po_line" => $poline,
            // "po_release" => $porelease,
            "qty" => $qty,
            "expiry_date" => $expiry_date ?? null,
            "trans_date" => $transDate,
            "transType" => $transType,
            "whse_num" => $whse,
            "uom" => $uom != '' ? $uom : $poitem->uom,
            "loc_num" => $loc,
            "lot_num" => $lot,
            "vend_num" => $po->vend_num ?? null,
            "vend_name" => $po->vend_name ?? null,
            "item_num" => $poitem->item_num,
            "item_desc" => $poitem->item_desc,
            "vend_lot" => $vendlot,
            "ref" => $ref,
            "pre_quantities" => $pre_quantities,
            "unit_weight" => $desc->unit_weight ?? "",
            "unit_length" => $desc->unit_length ?? "",
            "unit_width" => $desc->unit_width ?? "",
            "unit_height" => $desc->unit_height ?? "",
        );
        $input = self::getLpnDetail($input);
        $input = self::getItemImage($input);
        //return to handler function
        // dd($input);
        return $input;
    }


    public static function getItemImage($input)
    {


        if (isset($input['item_num'])) {
            $primary_image = DocNote::where('ref_type', "Item")->where('ref_num', $input['item_num'])->where('is_primary', 1)->first();

            if ($primary_image)
                $input['item_image'] = $primary_image->getDocFullPath();
            else
                $input['item_image'] = "";
        }
        // dd($input);

        return $input;
    }

    public static function getLpnDetail($input)
    {


        if (isset($_REQUEST['lpn_num'])) {
            $lpn_num_temp = $lpn_num = $_REQUEST['lpn_num'];

            $lpn_arr = explode("\r\n", "$lpn_num_temp");
            $lpn_num_first = isset($lpn_arr[0]) ? $lpn_arr[0] : $lpn_num_temp;
            $creation_date = Container::where('lpn_num', $lpn_num_first)->value('creation_date') ?? "";
            // dd($input, $lpn_num, $creation_date );
            // dd($creation_date);
            // $created_date_utc= Timezone::convertToUTC($creation_date, auth()->user()->getSiteTimezone(), SiteSetting::getOutputDateFormat() . ' H:i:s');
            // dd($created_date_utc);
            // if ($creation_date != "")
            //     $creation_date = Timezone::convertFromUTC($created_date_utc, auth()->user()->getSiteTimezone(), SiteSetting::getOutputDateFormat() . ' H:i:s');

            $input['creation_date'] = $creation_date;
            $input['lpn_num'] = $lpn_num;
        } else if (isset($_REQUEST['lpn_num_field'])) {
            $lpn_num = $_REQUEST['lpn_num_field'];
            $creation_date = Container::where('lpn_num', $lpn_num)->value('creation_date') ?? "";
            // if ($creation_date != "")
            //     $creation_date = Timezone::convertFromUTC($creation_date, auth()->user()->getSiteTimezone(), SiteSetting::getOutputDateFormat() . ' H:i:s');

            $input['creation_date'] = $creation_date;
            $input['lpn_num'] = $lpn_num;
        }
        if ((isset($_REQUEST['no_of_lpn']) && $_REQUEST['no_of_lpn'] != "") || (isset($_REQUEST['lpn_num']) && $_REQUEST['lpn_num'] != "") || (isset($_REQUEST['lpn_num_field']) && $_REQUEST['lpn_num_field'] != "")) {
            $input['disable_qty'] = "true";
        }
        // dd($input);

        return $input;
    }
    /**
     * Misc Issue
     * Misc Issue Label
     * <AUTHOR>
     * @param $whse, $loc, $item, $lot, $qty, $uom, $document, $expiry_date, $transDate, $transType
     */
    public static function GetMiscIssueLabelData($whse, $loc, $item, $lot, $qty, $uom, $document, $expiry_date, $transDate, $transType)
    {
        // create reference number
        if ($lot != null) {
            $ref = $whse . "-" . $loc . "-" . $item . "-" . $lot;
        } else {
            $ref = $whse . "-" . $loc . "-" . $item;
        }

        //if transaction date is null
        if ($transDate == null) {
            // $transDate = Carbon::now()->toDateString();
            $transDate = getDateTimeConverted();
        }

        //search Item for item description
        $desc = Item::select("item_desc", "unit_weight", "unit_length", "unit_width", "unit_height")
            ->where("item_num", $item)->first();

        //assigning keys to values
        $input = array(
            "whse_num" => $whse,
            "loc_num" => $loc,
            "item_num" => $item,
            "lot_num" => $lot,
            "qty" => $qty,
            "uom" => $uom,
            "trans_date" => $transDate,
            "transType" => $transType,
            "item_desc" => $desc->item_desc,
            "expiry_date" => $expiry_date ?? null,
            "ref" => $ref,
            "document" => $document,
            "document_num" => $document,
            "unit_weight" => $desc->unit_weight ?? "",
            "unit_length" => $desc->unit_length ?? "",
            "unit_width" => $desc->unit_width ?? "",
            "unit_height" => $desc->unit_height ?? "",



        );
        $input = self::getLpnDetail($input);
        $input = self::getItemImage($input);
        //return to handler function
        return $input;
    }

    /**
     * TO
     * TO Shipping Label
     * <AUTHOR>
     * @param $ponum, $poline, $porelease, $qty, $whse, $transDate, $transType, $loc, $lot, $vendlot
     */
    public static function GetTOShippingLabelData($from_whse, $to_whse, $trn_num, $trn_line, $item_num, $item_desc, $loc_num, $lot_num, $qty, $uom, $expiry_date, $transDate, $transType, $fromlabel)
    {
        //create reference number
        $ref = $trn_num . "-" . $trn_line;

        //if transaction posted null on transaction date
        if ($transDate == null) {
            // $transDate = Carbon::now()->toDateString();
            $transDate = getDateTimeConverted();
        }

        $to = TransferOrder::where("trn_num", $trn_num)->first();

        $to_item = TransferLine::where("trn_num", $trn_num)
            ->where("trn_line", $trn_line)
            ->first();

        $desc = Item::select("item_desc", "unit_weight", "unit_length", "unit_width", "unit_height")
            ->where("item_num", $to_item->item_num)->first();
        if (is_null($to_item)) {
            //invalid TO Line
            // set Error Message
            session('error', 'Invalid TO Number/Line. TO Item not found.');
            return back();
        }

        //assigning keys to values
        $input = array(
            "from_whse" => $from_whse,
            "to_whse" => $to_whse,
            "trn_num" => $trn_num,
            "trn_line" => $trn_line,
            "qty" => $qty,
            "expiry_date" => $expiry_date,
            "trans_date" => $transDate,
            "transType" => $transType,
            "loc_num" => $loc_num,
            "lot_num" => $lot_num,
            "uom" => $uom != '' ? $uom : $to_item->uom,
            "trn_loc" => $to->trn_loc,
            "vend_name" => $to->vend_num,
            "item_num" => $to_item->item_num,
            "item_desc" => $item_desc,
            "ref" => $ref,
            "fromlabel" => $fromlabel,
            "unit_weight" => $desc->unit_weight ?? "",
            "unit_length" => $desc->unit_length ?? "",
            "unit_width" => $desc->unit_width ?? "",
            "unit_height" => $desc->unit_height ?? "",
        );
        $input = self::getLpnDetail($input);
        $input = self::getItemImage($input);
        //return to handler function
        return $input;
    }

    /**
     * TO
     * TO Shipping Label
     * <AUTHOR>
     * @param $ponum, $poline, $porelease, $qty, $whse, $transDate, $transType, $loc, $lot, $vendlot
     */
    public static function GetTOReceiptLabelData($whse_num, $trn_num, $trn_line, $item_num, $item_desc, $loc_num, $lot_num, $qty, $uom, $expiry_date, $transDate, $transType, $from_whse, $to_whse)
    {
        //create reference number
        $ref = $trn_num . "-" . $trn_line;

        //if transaction posted null on transaction date
        if ($transDate == null) {
            // $transDate = Carbon::now()->toDateString();
            $transDate = getDateTimeConverted();
        }

        $to = TransferOrder::where("trn_num", $trn_num)->first();

        $to_item = TransferLine::where("trn_num", $trn_num)
            ->where("trn_line", $trn_line)
            ->first();

        if (!$item_desc) {
            $desc = Item::select("item_desc")
                ->where("item_num", $to_item->item_num)->first();
            $item_desc = $desc ? $desc->item_desc : "";
        }

        $desc = Item::select("item_desc", "unit_weight", "unit_length", "unit_width", "unit_height")
            ->where("item_num", $to_item->item_num)->first();
        // dd($item_desc);
        if (is_null($to_item)) {
            //invalid TO Line
            // set Error Message
            session('error', 'Invalid TO Number/Line. TO Item not found.');
            return back();
        }

        //assigning keys to values
        $input = array(
            "whse_num" => $whse_num,
            "trn_num" => $trn_num,
            "trn_line" => $trn_line,
            "qty" => $qty,
            "expiry_date" => $expiry_date,
            "trans_date" => $transDate,
            "transType" => $transType,
            "loc_num" => $loc_num,
            "lot_num" => $lot_num,
            "uom" => $uom != '' ? $uom : $to_item->uom,
            "trn_loc" => $to->trn_loc,
            "vend_name" => $to->vend_num,
            "item_num" => $to_item->item_num,
            "item_desc" => $item_desc,
            "ref" => $ref,
            'from_whse' => $from_whse,
            'to_whse' => $to_whse,
            "unit_weight" => $desc->unit_weight ?? "",
            "unit_length" => $desc->unit_length ?? "",
            "unit_width" => $desc->unit_width ?? "",
            "unit_height" => $desc->unit_height ?? "",
        );
        $input = self::getLpnDetail($input);
        $input = self::getItemImage($input);
        //return to handler function
        return $input;
    }

    /**
     * Generic Data Handler
     * <AUTHOR> Firdaus
     * @param $input from each transaction
     * @return
     * to definition page
     */
    public static function showLabelDefinition($input)
    {
        //dd($input,"ssss");

        // return redirect()->route('PrintPreviewLabelProcess', $input);
        // dd($input);

        $siteSettings = new SiteSetting();
        //label list according to transaction type
        $modules = new Module();
        $modules = $modules->find($input['transType']);

        // dd($modules);
        // $temp_in= $input;
        if ($input['transType'] != "Pallet") {
            if ($input['expiry_date'] != null) {
                // $input['expiry_date'] = Carbon::createFromFormat($siteSettings->getInputDateFormat(), $input['expiry_date'])->format('Y-m-d');
            }
        }
        // dd($input, $temp_in);
        // dd($modules);
        // $label_defaults = $modules ? $modules->labels()->where('is_default', 1)->get() : [];
        $label_defaults = $modules ? $modules->labels->sortBy('label_name', SORT_NATURAL | SORT_FLAG_CASE) : [];
        // dd($label_defaults);
        $types = [];
        foreach ($label_defaults as $label_default) {
            $types[] = $label_default->type;
        }
        // dd($modules, $label_defaults, $types);
        // $label = Label::whereIn('type', $types)->get();
        // dd($label);
        $label = $label_defaults;
        //dd($label);

        // dd($label);
        $no_of_boxes = Session::get('no_of_lpn', 1);
        $defaultValues = array('labelperbox' => 1, 'numofbox' => $no_of_boxes, 'quantityperbox' => $input['qty']);

        $cancel_href = null;
        $back_href = null;
        $back_picklist_href = null;
        if (isset($input['trn_num']))
            $input['to_num'] = $input['trn_num'];
        if (isset($input['trn_line']))
            $input['to_line'] = $input['trn_line'];
        if (session('stage_num') != null) {
            $input['stage_num'] = session('stage_num');
        } else {
            $input['stage_num'] = "";
        }

        foreach ($input as $key => $val) {
            if ($val == "")
                $input[$key] = "";
        }
        // dd($input);
        return redirect()->route('PrintPreviewLabelProcess', $input);
        $picklist_id = Session::get('picklist_id');
        // dd($input['transType'],session('JobRunEndLabour'));
        // dd(session('MachineRunCheck'),$input,session('WIPMove'));
        // Reeve
        //dd($input['transType']);
        if ($input['transType'] == "CustOrdPicking") {


            $pick_by = session()->get('pick_by_as');
            $select_pick_by = session()->get('select_pick_by');

            $shipping_zone_code =  session()->get('shipping_zone_code');
            $strsales_person = session()->get('strsales_person');
            $item_num =  session()->get('item_num');
            $cust_num =  session()->get('cust_num');
            $pick_action = session()->get('modulename_action');
            // dd(session());
            if ($picklist_id) {
                if ($pick_action == "Pick") {
                    $item_desc = session()->get('item_desc');
                    $qty_to_pick = session()->get('qty_to_pick');
                    $uom = session()->get('uom');
                    $zone_num = session()->get('zone_num');
                    $loc_num = session()->get('loc_num');
                    $lot_num = session()->get('lot_num');
                    $item_num_en = session()->get('item_num_encode');
                    $emp_num = session()->get('emp_num');
                    $countplist = session()->get('count-picklist-' . $emp_num . '-' . $request->whse_num);
                    // dd($emp_num,$countplist);

                    $input = array(
                        'picklist_id' => $picklist_id,
                        'cust_num'    => $cust_num,
                        'item_num_en' => $item_num_en,
                        'item_desc'   => $item_desc,
                        'uom'         => $uom,
                        'zone_num'    => $zone_num,
                        'loc_num'     => $loc_num,
                        'lot_num'     => $lot_num,
                        'qty_to_pick' => $qty_to_pick,
                        'countplist'  => $countplist,
                        'emp_num'     => $emp_num,
                        'whse_num'    => $request->whse_num,
                        'qty'         => $input['qty'],
                        'no_of_pallets'         => $input['no_of_pallets'],

                    );
                    // Redirect function
                    //$cancel_href = generateCancelUrl('Pick',$input);


                    $picklist_test_items = PicklistTestItems::with('item')->with('picklist_allocates')
                        ->where('picklist_id', $picklist_id)
                        ->where('line_status', '!=', 'C')
                        ->orderBy('due_date')
                        ->get();
                    $countplist = $picklist_test_items->count();
                    // $cancel_href = route('picksTest.picklistDetails', ['picklist' => $picklist_id]);
                    //$cancel_href = route('picksTest.confirmPick', ['picklist' => $picklist_id, 'cust_num' => $cust_num, 'item_num' => $item_num_en, 'item_desc' => $item_desc, 'uom' => $uom, 'zone_num' => $zone_num, 'loc_num' => $loc_num, 'lot_num' => $lot_num, 'qty_to_pick' => $qty_to_pick]);
                    if (@$countplist > 1) {
                        //dd(55);
                        //$cancel_href = route('picksTest.list', ['whse_num' => $request->whse_num,'emp_num'=>$emp_num]);
                        $cancel_href = route('picksTest.pickItem', ['picklist' => $picklist_id]);
                    } else {
                        if (($qty_to_pick == 0 || $qty_to_pick == null) && $countplist == 1) {

                            // $cancel_href = route('picksTest.pickItem', ['picklist' => $picklist_id]);
                            $cancel_href = route('picksTest.picklistDetails', ['picklist' => $picklist_id]);
                        } else {

                            $cancel_href = route('picksTest.confirmPick', ['picklist' => $picklist_id, 'cust_num' => $cust_num, 'item_num' => $item_num_en, 'item_desc' => $item_desc, 'uom' => $uom, 'zone_num' => $zone_num, 'loc_num' => $loc_num, 'lot_num' => $lot_num, 'qty_to_pick' => $qty_to_pick]);
                        }
                    }


                    // item_num=WW4yMzA4&item_desc=Yn2308&qty_to_pick=9.00&uom=Yn2308&zone_num=BULK-1&loc_num=A1-1&lot_num=Yn2308Yn2308

                } else if ($pick_action == "UnPick") {
                    $qty_picked = session()->get('qty_picked');
                    $emp_num = session()->get('unpick_emp_num');
                    //$uncountplist =  session()->put('count-unpick-'.auth()->user()->id.'-'.auth()->user()->site_id.'-'.$picklist_id, $countUnpick);
                    $uncountplist = session()->get('count-unpick-' . auth()->user()->id . '-' . auth()->user()->site_id . '-' . $picklist_id . '-' . $emp_num);

                    // dd($qty_picked - $request->qty);
                    $picklist_allocate = session()->get('picklist_allocate');
                    $to_loc = session()->get('to_loc_unpick');
                    //dd($uncountplist,$qty_picked - $request->qty);
                    if ($uncountplist == 1 && ($qty_picked - $request->qty > 0)) {
                        $qty_picked = session()->get('qty_picked');
                        $cancel_href = route('picksTest.unpick', [
                            'picklist' => $picklist_id,
                            'picklist_allocate' => $picklist_allocate,
                            'whse_num' => $request->whse_num,
                            'loc_num' => $to_loc,
                            'co_num' => $request->co_num,
                            'qty_picked' => $qty_picked - $request->qty,
                            'uom' => $request->uom,
                            'lot_num' => $request->lot_num,
                        ]);

                        // dd(1);

                        // $cancel_href = route('picksTest.pickedItems', ['picklist' => $picklist_id]);
                        // $cancel_href = route('picksTest.unpick', ['picklist' => $picklist_id,'picklist_allocate'=>$picklist_allocate]);
                    } else {


                        $cancel_href = route('picksTest.pickedItems', ['picklist' => $picklist_id]);


                        // $cancel_href = route('picksTest.confirmPick', ['picklist' => $picklist_id,'item_num' => $item_num,'item_desc'=>$item_desc, 'uom' => $pick_by, 'select_pick_by' => $select_pick_by, 'shipping_zone_code' => $shipping_zone_code, 'strsales_person' => $strsales_person, 'item_num' => $item_num, 'whse_num' => $input['whse_num'], 'co_num' => $input['co_num']]);
                    }
                    //
                }

                // ?item_num=QXBwbGU%3D&item_desc=Apple&qty_to_pick=0.60&uom=EA&zone_num=BULK-1&loc_num=A1-2&lot_num=LOT02SS
                // $cancel_href = route('picksTest.confirmPick', ['picklist' => $picklist_id,'item_num' => $item_num,'item_desc'=>$item_desc, 'uom' => $pick_by, 'select_pick_by' => $select_pick_by, 'shipping_zone_code' => $shipping_zone_code, 'strsales_person' => $strsales_person, 'item_num' => $item_num, 'whse_num' => $input['whse_num'], 'co_num' => $input['co_num']]);
            } else {

                // CoPickingDetails
                //dd('ssssqqq');
                $cancel_href = route('CoPickingDetails', ['whse_num' => $input['whse_num'], 'co_num' => $input['co_num'], 'co_line' => "", 'stage_num' => $input['stage_num']]);
            }
        } else if ($input['transType'] == "TranOrderShipping") {
            // dd('hello');

            $back_href = route('backTOShippingList', ['whse_num' => $input['from_whse'], 'trn_num' => $input['trn_num'], 'item_num' => '']);
        } else if ($input['transType'] == "TransferOrderReceipt") {
            $back_href = route('transferOrderItemList', ['whse_num' => $input['to_whse'], 'trn_num' => $input['trn_num'], 'item_num' => '']);
        } else if ($input['transType'] == "Picklist") {
            $back_picklist_href = route('picksTest.pickItem', ['picklist' => $picklist_id]);
        } else if ($input['transType'] == "JobReceipt" && session('WIPMove') == 1) {
            // session("WIPMove","");
            session()->put('WIPMove', "");
            $back_href = route('WIPMove');
        } else if ($input['transType'] == "JobReceipt" && session('MachineRunCheck') == 2) {
            // dd("dsd",session('WIPMove'));
            session()->put('MachineRunCheck', "");
            //session("MachineRunCheck","");
            $back_href = route('MachineRun');
            $tparm = new TparmView;
            $allow_multiple_job = $tparm->getTparmValue('MachineRun', 'allow_multiple_job');
            if ($allow_multiple_job) {
                $back_href = route('MachineRunEnd');
            }
        } else if ($input['transType'] == "JobReceipt" && session('JobRunEndLabour') == 3) {

            session()->put('JobRunEndLabour', "");
            $tparm = new TparmView();
            if ($tparm->getTparmValue('JobLabour', 'allow_multiple_job') == 0) {
                // return redirect()->route('LabourReporting');
                $back_href = route('LabourReporting');
            } else {
                //return redirect()->route('EndLabourReporting');
                $back_href = route('EndLabourReporting');
            }
        } else if ($input['transType'] == "Pallet") {
            $back_href = route('PalletBuilder');
            // if(isset($input['labelPage']))
            // {
            //     $back_href = route('showPalletLabel');
            // }else{
            //     $back_href = route('PalletBuilder');
            // }

        }


        // dd($input);
        $view = "Barcode.labeldefinition";
        // dd($arrInput);
        if (isset($input['disable_qty'])) {
            $view = "Barcode.pallet.labeldefinition";
        }

        if (config('icapt.bartender')) {
            Session::put('print_status', 'No');
            $tparm = new TparmView();

            //printer list
            $printer = Printer::select('id', 'printername')->get();
            $use_bartender = $tparm->getTparmValue('System', 'default_label_print_method');

            return view('Barcode.labeldefinition')
                ->with("page", "barcode")
                ->with(["printer" => $printer, "label" => $label])
                ->with("input", $input)
                ->with('defaultValues', $defaultValues)
                ->with('use_bartender', $use_bartender)
                ->with('cancel_href', $cancel_href)
                ->with("label_default", @$label_default)
                ->with('back_href', $back_href)
                ->with('back_picklist_href', $back_picklist_href);
        }

        $print_node_setting = \App\SiteConnection::where('site_id', auth()->user()->site_id)->where('type', 'simple')->where('active', 1)->where('connector', 'print_node')->first();
        // dd($print_node_setting);
        $printers = [];

        $error = "";

        if ($print_node_setting) {


            $apiKey = $print_node_setting;
            // $printerId = "72442662";

            $apiKey = \PrintNode\Client::getAxacuteConnection($print_node_setting);
            $credentials = new \PrintNode\Credentials\ApiKey($apiKey);
            try {
                $client = new \PrintNode\Client($credentials);
                $printers = $client->viewPrinters();
            } catch (\Throwable $th) {
                //throw $th;

                $errorMsg = $th->getMessage();
                $errorText = __('error.admin.print_node_error', ['errorText' => $errorMsg]);
                Alert::html("Oops!", $errorText, 'error');
                // Alert::html("$message");
                // $error = "PrintNode: " . $th->getMessage();
            }

            // dd($printers);
        }

        // dd($input);
        $view = view('Barcode.labeldefinition')
            ->with("page", "barcode")
            ->with(["label" => $label, 'print_node_setting' => $print_node_setting, 'printers' => $printers])
            ->with("label_default", @$label_default)
            ->with("input", $input)
            ->with('defaultValues', $defaultValues)
            ->with('cancel_href', $cancel_href)
            ->with('back_href', $back_href)
            ->with('back_picklist_href', $back_picklist_href);

        return $view;
    }
    public static function PrintPreviewLabelProcessBatch(Request $request)
    {


        // $arrInput = request('arrInput', []);
        // $batch_id = request('batch_id', 0);
        $transType = $request->transType;
        if ($transType == "StockMove")
            $trans_type = "Stock Move To";
        // dd($transType);
        // $batch_id = "AXA_TEST_StockMove_anxfaxee_20240902145058";
        $batch_id = session()->get('batch_id');
        $getMaltTransByBatch = [];

        if ($batch_id)
            $getMaltTransByBatch = MatlTrans::where('batch_id', $batch_id)->where('trans_type', $trans_type)->orderBy('id', 'DESC')
                // ->take(2)
                ->get()->toArray();
        // dd($batch_id, $getMaltTransByBatch);
        // $getMaltTransBulk = [];
        // foreach ($getMaltTransByBatch as $elm) {
        //     $getMaltTransBulk[$elm->batch_id][] = $elm;
        // }
        $siteSettings = new SiteSetting();
        $arrKey = array();
        $arrStoreKey = array();

        //label list according to transaction type
        $arrInput = [];
        foreach ($getMaltTransByBatch as $input) {
            $temp_input = $input;
            $temp_input['transType'] = "StockMove";
            $temp_input['qty'] = $input['trans_qty'];
            $temp_input['uom'] = $input['trans_uom'];

            $key = $input['id'];

            $transType = 'StockMove';
            $request->merge($temp_input);

            if ($request->lot_num != "") {
                $check_expiry_date = LotService::getExpiryDate($request);

                $input = BarcodeController::GetInventoryLabelData($request->whse_num, $request->loc_num, $request->item_num, $request->lot_num, $request->qty, $request->uom, $request->document_num, $check_expiry_date, $request->trans_date, $transType);
            } else {
                $input = BarcodeController::GetInventoryLabelData($request->whse_num, $request->loc_num, $request->item_num, $request->lot_num, $request->qty, $request->uom, $request->document_num, null, $request->trans_date, $transType);
            }
            // dd($input,$request->all());
            $input['id'] = $key;
            $arrInput[$key] = $input;
        }
        // dd($arrayInput);
        foreach ($arrInput as $input) {
            // dd($input);

            $key = $input['id'];
            $modules = new Module();
            $modules = $modules->find($input['transType']);


            $label[$key] = $modules->labels()->get();

            $defaultValues[$key] = array('labelperbox' => 1, 'numofbox' => 1, 'quantityperbox' => $input['qty']);
            $arrKey[$key] = $key;
            array_push($arrStoreKey, $key);
        }
        // sort($arrStoreKey);


        $totaldefault = 0;
        foreach ($arrInput as $key => $value) {
            $totaldefault += $value['qty'];
        }

        $printer = "";
        if (config('icapt.bartender')) {
            Session::put('print_status', 'No');
            $tparm = new TparmView();

            //printer list
            $printer = Printer::select('id', 'printername')->get();
            $use_bartender = $tparm->getTparmValue('System', 'default_label_print_method');
        }

        $templates = $printers = [];
        $bartender_cloud_setting = \App\SiteConnection::where('site_id', auth()->user()->site_id)->where('type', 'simple')->where('active', 1)->where('connector', 'bartender_cloud')->first();
        if ($bartender_cloud_setting) {

            try {
                $printers = BartenderCloudService::getPrinters();
                $templates = BartenderCloudService::getTemplates();
            } catch (\Throwable $th) {
                //throw $th;

                // $error = "PrintNode: " . $th->getMessage();
                $errorMsg = $th->getMessage();
                $errorText = __('error.admin.bartender_cloud_error', ['errorText' => $errorMsg]);
                Alert::html("Oops!", $errorText, 'error');
            }

            // dd($printers);
        }
        $tparm = new TparmView();
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $quantity_per_format = $tparm->getTparmValue('System', 'decimal_setting_quantity');
        $view = "Barcode.labeldefinitionBatch";
        $input = $arrInput[$arrStoreKey[0]];
        // dd($arrKey,$arrInput);

        // dd($input);
        if (isset($input['disable_qty'])) {
            $view = "Barcode.pallet.labeldefinitionBatch";
        }
        return view($view)
            ->with('bartender_cloud_setting', $bartender_cloud_setting)
            ->with('printers', $printers)
            ->with('templates', $templates)
            ->with("totaldefault", $totaldefault)
            ->with("page", "barcode")
            ->with(["label" => $label[$arrStoreKey[0]]])
            ->with("printer", $printer)
            ->with("input", $input)
            ->with("arrKey", $arrKey)
            ->with("arrInfor", $arrInput)
            ->with('unit_quantity_format', $unit_quantity_format)
            ->with('quantity_per_format', $quantity_per_format)
            ->with("defSequence", json_encode($arrStoreKey))
            ->with('defaultValues', $defaultValues[$arrStoreKey[0]]);
    }
    /**
     * @param Request $request
     *
     * Print catch weight process
     *
     * @return void
     */
    public  function printCatchWeightProcess(Request $request)
    {

        // $data = [
        //     "po_num" => "POT0015",
        //     "po_line" => "1",
        //     "qty" => "4",
        //     "expiry_date" => null,
        //     "trans_date" => "13-06-2025 02:52:19",
        //     "transType" => "PoReceipt",
        //     "whse_num" => "MAIN",
        //     "uom" => "LB",
        //     "loc_num" => "A1-1012",
        //     "lot_num" => "92",
        //     "vend_num" => "Vend001",
        //     "vend_name" => "MT Spokes Company",
        //     "item_num" => "ItemLawLot4",
        //     "item_desc" => "ItemLawLot4",
        //     "vend_lot" => null,
        //     "ref" => "POT0015-1",
        //     "unit_weight" => "55.000000",
        //     "unit_length" => "66.000000",
        //     "unit_width" => "77.000000",
        //     "unit_height" => "88.000000",
        //     "item_image" => null,
        //     "frm_po_print_label" => "1",
        //     "stage_num" => null,
        //     "no_of_pallets" => "1",
        //     "label" => "163",
        // ];


        // $request->merge($data);
        // dd($request->all());
        $transType = $trans_type = $request->trans_type;
        // dd($modules);
        // dd($trans_type);
        // $transType = "PoReceipt";
        $modules = new Module();
        $modules = $modules->find($transType);
        $item = $request->item_num;
        $desc = Item::select("item_desc", "unit_weight", "unit_length", "unit_width", "unit_height")
            ->where("item_num", $item)->first();

        $label_defaults = $modules ? $modules->labels()->orderBy('pivot_is_default', "desc")->orderBy('label_name', 'asc')->first() : false;
        // dd($label_defaults);
        if (!$label_defaults || $label_defaults->file_name == "")
            return  ['status' => false, 'message' => 'No label template is assigned. Configure one in Label settings.'];

        // dd($label_defaults);
        $dataArray = [
            0 => [
                "no" => 1,
                "labelperbox" => "1",
                "labelname" => $label_defaults->id,
                "numofbox" => "1",
                "quantityperbox" => $request->qty,
                "quantity" => $request->qty,
            ]

        ];
        $datajson = json_encode($dataArray);

        $input = [];
        $check_expiry_date = null;
        if ($request->lot_num != "") {
            $check_expiry_date = LotService::getExpiryDate($request);
        }
        if ($trans_type == "PoReceipt") {
            $input = self::GetPOReceiveLabelData($request->ref_num, $request->ref_line, $request->qty, $request->whse_num, $request->trans_date, $request->trans_type, $request->loc_num, $request->lot_num, $request->expiry_date, $request->vend_lot);
        }
        if ($trans_type == "JobMaterialIssue") {
            $input = self::GetJobMatLabelData($request->ref_num, $request->suffix, $request->sequence,  $request->oper_num, $request->matl_item,  $request->qty,  $request->uom, $request->whse_num, $request->loc_num, $request->lot_num, $request->expiry_date, $request->trans_date, 'JobMaterialIssue');
        }
        if ($trans_type == "JobReceipt") {
            $input = $this->GetJobReceiptLabelData($request->ref_num, $request->suffix, $request->item_num, $request->item_desc, $request->qty, $request->uom, $request->loc_num, $request->lot_num, $request->expiry_date,  $request->trans_date, $request->document_num, 'JobReceipt');

            // $input = self::GetJobReceiptLabelData($request->ref_num, $request->suffix, $request->qty, $request->whse_num, $request->trans_date, $request->trans_type, $request->loc_num, $request->lot_num, $request->expiry_date, $request->vend_lot);
        }
        if ($trans_type == "TranOrderShipping") {
            $input = self::GetTOShippingLabelData($request->from_whse, $request->to_whse, $request->trn_num, $request->trn_line, $request->item_num, $request->item_desc, $request->loc_num, $request->lot_num, $request->qty, $request->uom, $request->expiry_date, $request->trans_date, $request->trans_type, $request->from_label);
        }
        if ($trans_type == "CustOrdShipping") {
            $input = self::GetCustOrdLabelData($request->ref_num, $request->ref_line, $corelease,  $request->qty, $request->uom, $request->whse, $request->loc_num, $request->lot_num, $request->expiry_date, $request->trans_date, $transType);
        }
        if ($trans_type == "StockMove" || $trans_type == "MiscIssue" || $trans_type == "MiscReceipt") {
            $input = self::GetInventoryLabelData($request->whse_num, $request->loc_num, $request->item_num, $request->lot_num, $request->qty, $request->uom, $request->document_num, $request->expiry_date, $request->trans_date, $transType);
        }
        // dd($input);
        $input['datajson'] = $datajson;

        $request->merge($input);
        $request['catch_weight'] = true;
        // $request['test'] = "true";
        try {
            $response = $this->PrintLabelProcess($request);
        } catch (\Throwable $th) {
            return ['status' => false, 'message' => $th->getMessage()];
        }
        // $response = $this->PrintLabelProcess($request);

        if ($response == true) {
            return ['status' => true, 'message' => 'Label printed successfully.'];
        } else {
            return ['status' => false, 'message' => 'Label not printed successfully.'];
        }
        dd($response);
        $print_node_setting = \App\SiteConnection::where('site_id', auth()->user()->site_id)->where('active', 1)->where('type', 'simple')->where('connector', 'print_node')->first();
        $bartender = config('icapt.bartender');
        $bartender_cloud_setting = \App\SiteConnection::where('site_id', auth()->user()->site_id)->where('type', 'simple')->where('active', 1)->where('connector', 'bartender_cloud')->first();

        $input['label_name'] = $label_defaults->labelname;
        //convert the quantity to 2 decimal places
        $qtyinbox = number_format((float) $request->quantity, $quantity_per_format, '.', '');
        $input['boxnum'] = 1;
        $input['bn'] = 1;
        $input['qtyinbox'] = $qtyinbox;
        $input['qib'] = $qtyinbox;
        $input['qty'] = $qtyinbox;
        $input['totalbox'] = 1;
        $input['tb'] = 1;
        $input['labelperbox'] = 1;
        $input['creator'] = Auth::user()->name;
        $input['unit_width'] =  $desc->unit_width;
        $input['unit_height'] = $desc->unit_height;
        $input['unit_length'] = $desc->unit_length;
        $input['unit_weight'] = $desc->unit_weight;
        if ($bartender_cloud_setting) {



            //adding the number of box, quantity per box and total box

            // add Creator to the label info
            // dd($input);
            $text = preg_replace("/\r|\n/", "", $input);
            list($keys, $values) = array_divide($text);

            // dd($label,'ss');
            BartenderCloudService::printLabel($input, $label_defaults, $keys, $values);
        }
        if ($print_node_setting) {
            $this->printNodeProcess($request, $input, $label_defaults, $print_node_setting);
        }
    }
    /**
     * This function is used to print the label using PrintNode.
     *
     * @param $request
     * @param $input
     * @param $label
     * @param $print_node_setting
     * @return void
     */
    public static function printNodeProcess($request, $input, $label, $print_node_setting)
    {

        $htmlView = view('Barcode.pdfhtml', compact('labelHtml', 'labelData'))->render();

        // echo $htmlView;
        // exit;
        // $htmlView="<h1>Test</h1>";
        $path = '/temp_prints';
        $time = strtotime("now");
        $fileName = $time . "_" . $input['label_name'] . "_" . auth()->user()->site_id . ".pdf";
        // if (file_exists($path . '/' . $fileName)) {
        //     unlink($path . '/' . $fileName);
        // }
        $filePath = storage_path("/app/$path" . "/" . $fileName);
        // dd($filePath);
        $dompdf = new Dompdf();


        $dompdf->setPaper(array(
            0,
            0,
            ($label->width * $label->columns),
            ($label->height * $label->rows)
        ), 'portrait');

        // // Set document information
        // // $dompdf->set_option('isPhpEnabled', true);
        $dompdf->loadHtml($htmlView);
        // // Render the PDF
        $dompdf->render();
        $output = $dompdf->output();

        // $dompdf->stream($path);
        Storage::put("$path" . "/" . $fileName, $output);

        // file_put_contents($filePath, $output);
        $printerId = $request->printer;
        try {
            //code...
            $apiKey = \PrintNode\Client::getAxacuteConnection($print_node_setting);
            $credentials = new \PrintNode\Credentials\ApiKey($apiKey);
            $client = new \PrintNode\Client($credentials);

            $printJob = new \PrintNode\Entity\PrintJob($client);

            $options = [
                'paper' => "$label->paper_size;",
                'fit_to_page' => true,
                // 'rotate'=>0,
            ];
            // $filePAth = public_path('YNInventory Label_AXA_TEST.pdf');
            $printJob->title = $label->label_name;
            $printJob->source = "AXACUTE-$label->site_name";
            $printJob->printer = $printerId;
            $printJob->contentType = 'pdf_base64';
            $printJob->options = $options;
            $printJob->addPdfFile($filePath);

            $printJobId = $client->createPrintJob($printJob);
            // dd($printJobId);
            if (file_exists($filePath)) {
                unlink($filePath);
            }
        } catch (\Throwable $th) {
            //throw $th;
            $errorMsg = $th->getMessage();
            $errorText = __('error.admin.print_node_error', ['errorText' => $errorMsg]);
            Alert::html("Oops!", $errorText, 'error');
            // throw ValidationException::withMessages($th->getMessage());
        }
    }
    public static function PrintPreviewLabelProcessCatchWeight(Request $request)
    {


        // $arrInput = request('arrInput', []);
        // $batch_id = request('batch_id', 0);
        $transType = $request->transType;
        if ($transType == "StockMove")
            $trans_type = "Stock Move To";
        // dd($transType);
        // $batch_id = "AXA_TEST_StockMove_anxfaxee_20240902145058";
        $batch_id = session()->get('batch_id');
        $batch_id = "AXA_TEST|sysadmin|2025032610230582969";
        $getMaltTransByBatch = [];

        if ($batch_id)
            $getMaltTransByBatch = MatlTrans::where('batch_id', $batch_id)->orderBy('id', 'DESC')
                // ->take(2)
                ->get()->toArray();
        // dd($batch_id, $getMaltTransByBatch);
        // $getMaltTransBulk = [];
        // foreach ($getMaltTransByBatch as $elm) {
        //     $getMaltTransBulk[$elm->batch_id][] = $elm;
        // }
        $siteSettings = new SiteSetting();
        $arrKey = array();
        $arrStoreKey = array();

        //label list according to transaction type
        $arrInput = [];
        foreach ($getMaltTransByBatch as $input) {
            $temp_input = $input;
            $trans_type = $input['trans_type'];
            // $temp_input['transType'] = "StockMove";
            $temp_input['qty'] = $input['trans_qty'];
            $temp_input['uom'] = $input['trans_uom'];

            $key = $input['id'];


            $request->merge($temp_input);
            $check_expiry_date = null;
            if ($request->lot_num != "") {
                $check_expiry_date = LotService::getExpiryDate($request);
            }
            if ($trans_type == "PO Receipt") {
                $input = self::GetPOReceiveLabelData($request->ref_num, $request->ref_line, $request->qty, $request->whse_num, $request->trans_date, $request->trans_type, $request->loc_num, $request->lot_num, $check_expiry_date, $request->vend_lot);
            }
            unset($input['pre_quantities']);
            // $input = BarcodeController::GetInventoryLabelData($request->whse_num, $request->loc_num, $request->item_num, $request->lot_num, $request->qty, $request->uom, $request->document_num, $check_expiry_date, $request->trans_date, $transType);

            // dd($input,$request->all());
            $input['id'] = $key;
            $arrInput[$key] = $input;
        }
        // dd($arrInput);/*  */

        foreach ($arrInput as $input) {
            // dd($input);

            $key = $input['id'];
            $modules = new Module();
            $transType = $input['transType'];
            $transType = "POReceipt";
            $modules = $modules->find($transType);
            // dd($modules);

            $label[$key] = $modules->labels()->get();

            $defaultValues[$key] = array('labelperbox' => 1, 'numofbox' => 1, 'quantityperbox' => $input['qty']);
            $arrKey[$key] = $key;
            array_push($arrStoreKey, $key);
        }
        // sort($arrStoreKey);

        // dd($label);
        $totaldefault = 0;
        foreach ($arrInput as $key => $value) {
            $totaldefault += $value['qty'];
        }

        $printer = "";
        if (config('icapt.bartender')) {
            Session::put('print_status', 'No');
            $tparm = new TparmView();

            //printer list
            $printer = Printer::select('id', 'printername')->get();
            $use_bartender = $tparm->getTparmValue('System', 'default_label_print_method');
        }

        $templates = $printers = [];
        $bartender_cloud_setting = \App\SiteConnection::where('site_id', auth()->user()->site_id)->where('type', 'simple')->where('active', 1)->where('connector', 'bartender_cloud')->first();
        if ($bartender_cloud_setting) {

            try {
                $printers = BartenderCloudService::getPrinters();
                $templates = BartenderCloudService::getTemplates();
            } catch (\Throwable $th) {
                //throw $th;

                // $error = "PrintNode: " . $th->getMessage();
                $errorMsg = $th->getMessage();
                $errorText = __('error.admin.bartender_cloud_error', ['errorText' => $errorMsg]);
                Alert::html("Oops!", $errorText, 'error');
            }

            // dd($printers);
        }
        $tparm = new TparmView();
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $quantity_per_format = $tparm->getTparmValue('System', 'decimal_setting_quantity');
        $view = "Barcode.labeldefinitionCatchWeight";
        $input = $arrInput[$arrStoreKey[0]];

        // dd($arrKey,$arrInput);

        // dd($input);
        if (isset($input['disable_qty'])) {
            $view = "Barcode.pallet.labeldefinitionBatch";
        }
        return view($view)
            ->with('bartender_cloud_setting', $bartender_cloud_setting)
            ->with('printers', $printers)
            ->with('templates', $templates)
            ->with("totaldefault", $totaldefault)
            ->with("page", "barcode")
            ->with(["label" => $label[$arrStoreKey[0]]])
            ->with("printer", $printer)
            ->with("input", $input)
            ->with("arrKey", $arrKey)
            ->with("arrInfor", $arrInput)
            ->with('unit_quantity_format', $unit_quantity_format)
            ->with('quantity_per_format', $quantity_per_format)
            ->with("defSequence", json_encode($arrStoreKey))
            ->with('defaultValues', $defaultValues[$arrStoreKey[0]]);
    }

    public static function getPrintersData()
    {
        $printers = [];
        if (config('icapt.bartender')) {
            Session::put('print_status', 'No');
            $tparm = new TparmView();

            //printer list
            $printer = Printer::select('id', 'printername')->get();
            foreach ($printer as $key => $value) {
                $printers[$value->id] = $value->printername;
            }
            return $printers;
        }

        $bartender_cloud_setting = \App\SiteConnection::where('site_id', auth()->user()->site_id)
            ->where('type', 'simple')->where('active', 1)
            ->where('connector', 'bartender_cloud')
            ->first();

        if ($bartender_cloud_setting) {

            try {
                $printer = BartenderCloudService::getPrinters();
                // dd($printer);
                foreach ($printer as $key => $value) {
                    $printers[$value->PrinterId] = $value->Name;
                }
                // dd($printers, $printer);
            } catch (\Throwable $th) {
                //throw $th;

                // $error = "PrintNode: " . $th->getMessage();
                $errorMsg = $th->getMessage();
                $errorText = __('error.admin.bartender_cloud_error', ['errorText' => $errorMsg]);
                Alert::html("Oops!", $errorText, 'error');
            }

            return $printers;
            // dd($printers);
        }
        $print_node_setting = \App\SiteConnection::where('site_id', auth()->user()->site_id)->where('type', 'simple')->where('active', 1)->where('connector', 'print_node')->first();


        $error = "";
        if ($print_node_setting) {
            $apiKey = $print_node_setting;
            // $printerId = "72442662";

            $apiKey = \PrintNode\Client::getAxacuteConnection($print_node_setting);
            $credentials = new \PrintNode\Credentials\ApiKey($apiKey);
            try {
                $client = new \PrintNode\Client($credentials);
                $printer = $client->viewPrinters();
                foreach ($printer as $key => $value) {
                    $printers[$key] = $value->name;
                }
            } catch (\Throwable $th) {
                //throw $th;

                // $error = "PrintNode: " . $th->getMessage();
                $errorMsg = $th->getMessage();
                $errorText = __('error.admin.print_node_error', ['errorText' => $errorMsg]);
                Alert::html("Oops!", $errorText, 'error');
            }
            return $printers;
            // dd($printers);
        }
        return $printers;
    }
    public static function PrintPreviewLabelProcessMulti(Request $request)
    {

        $tparm = new TparmView();
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $quantity_per_format = $tparm->getTparmValue('System', 'decimal_setting_quantity');
        // $arrInput = request('arrInput', []);
        $shipmentId = request('shipmentId', 0);
        // dd($arrInput);
        $siteSettings = new SiteSetting();
        $arrKey = array();
        $arrStoreKey = array();
        $default_label = false;
        // $arrInput = DB::table('temp_subscription')->find(request('arrInput'));
        // $arrInput = json_decode($arrInput->value_label, true);
        $arrInput = session()->get('largeData_' . request('arrInput'));
        // dd($arrInput);
        //label list according to transaction type
        foreach ($arrInput as $key => $input) {
            $modules = new Module();
            $modules = $modules->find($input['transType']);

            if ($input['expiry_date'] != null) {
                $input['expiry_date'] = Carbon::createFromFormat($siteSettings->getInputDateFormat(), $input['expiry_date'])->format('Y-m-d');
            }

            $label[$key] = $modules->labels->sortBy('label_name', SORT_NATURAL | SORT_FLAG_CASE);
            if (!$default_label)
                $default_label = DB::table('label_modules')->where('modulename', $input['transType'])->where('is_default', 1)->where('site_id', auth()->user()->site_id)
                    ->first();
            $defaultValues[$key] = array('labelperbox' => 1, 'numofbox' => 1, 'quantityperbox' => $input['qty']);
            $arrKey[$key] = $key;
            array_push($arrStoreKey, $key);
        }
        // sort($arrStoreKey);


        $totaldefault = 0;
        foreach ($arrInput as $key => $value) {
            $totaldefault += $value['qty'];
        }

        $printer = "";
        if (config('icapt.bartender')) {
            Session::put('print_status', 'No');
            $tparm = new TparmView();

            //printer list
            $printer = Printer::select('id', 'printername')->get();
            $use_bartender = $tparm->getTparmValue('System', 'default_label_print_method');
        }



        $view = "Barcode.labeldefinitionMultiSingleForm";
        $input = $arrInput[$arrStoreKey[0]];
        // dd($arrInput);
        $templates = $printers = [];
        $bartender_cloud_setting = \App\SiteConnection::where('site_id', auth()->user()->site_id)->where('type', 'simple')->where('active', 1)->where('connector', 'bartender_cloud')->first();
        if ($bartender_cloud_setting) {

            try {
                $printers = BartenderCloudService::getPrinters();
                $templates = BartenderCloudService::getTemplates();
            } catch (\Throwable $th) {
                //throw $th;

                // $error = "PrintNode: " . $th->getMessage();
                $errorMsg = $th->getMessage();
                $errorText = __('error.admin.bartender_cloud_error', ['errorText' => $errorMsg]);
                Alert::html("Oops!", $errorText, 'error');
            }

            // dd($printers);
        }
        // dd($input);
        if (isset($input['disable_qty'])) {
            $view = "Barcode.pallet.labeldefinitionMultiSingleForm";
        }
        if (isset($input['label_template']) && $input['label_template'] == 'jobs') {
            $view = "Barcode.labeldefinitionBatchJob";
        }
        return view($view)
            ->with("totaldefault", $totaldefault)
            ->with("page", "barcode")
            ->with(["label" => $label[$arrStoreKey[0]]])
            ->with('default_label', $default_label)
            ->with("printer", $printer)
            ->with("input", $input)
            ->with("arrKey", $arrKey)
            ->with('unit_quantity_format', $unit_quantity_format)
            ->with("shipmentId", $shipmentId)
            ->with('bartender_cloud_setting', $bartender_cloud_setting)
            ->with('printers', $printers)
            ->with('templates', $templates)

            ->with("arrInfor", $arrInput)
            ->with("defSequence", json_encode($arrStoreKey))
            ->with('defaultValues', $defaultValues[$arrStoreKey[0]]);
    }
    public static function showLabelDefinitionMultiple($arrInput, $shipmentId)
    {

        // dd($arrInput, $shipmentId);
        foreach ($arrInput as $key => $val) {
            foreach ($val as $key2 => $val2) {
                if ($val2 == "")
                    $arrInput[$key][$key2] = "";
            }
        }

        $unique_id = uniqid();
        session()->put('largeData_' . $unique_id, $arrInput);

        return redirect()->route('PrintPreviewLabelProcessMulti', ["arrInput" => $unique_id, "shipmentId" => $shipmentId]);


        $siteSettings = new SiteSetting();
        $arrKey = array();
        $arrStoreKey = array();
        //label list according to transaction type
        foreach ($arrInput as $key => $input) {
            $modules = new Module();
            $modules = $modules->find($input['transType']);

            if ($input['expiry_date'] != null) {
                $input['expiry_date'] = Carbon::createFromFormat($siteSettings->getInputDateFormat(), $input['expiry_date'])->format('Y-m-d');
            }

            $label[$key] = $modules->labels()->get();

            $defaultValues[$key] = array('labelperbox' => 1, 'numofbox' => 1, 'quantityperbox' => $input['qty']);
            $arrKey[$key] = $key;
            array_push($arrStoreKey, $key);
        }
        // sort($arrStoreKey);


        $totaldefault = 0;
        foreach ($arrInput as $key => $value) {
            $totaldefault += $value['qty'];
        }

        $printer = "";
        if (config('icapt.bartender')) {
            Session::put('print_status', 'No');
            $tparm = new TparmView();

            //printer list
            $printer = Printer::select('id', 'printername')->get();
            $use_bartender = $tparm->getTparmValue('System', 'default_label_print_method');
        }



        $view = "Barcode.labeldefinitionMultiSingleForm";
        $input = $arrInput[$arrStoreKey[0]];
        // dd($arrInput);

        if (isset($input['disable_qty'])) {
            $view = "Barcode.pallet.labeldefinitionMultiSingleForm";
        }
        if (isset($input['label_template']) && $input['label_template'] == 'jobs') {
            $view = "Barcode.pallet.labeldefinitionBatchJob";
        }
        return view($view)
            ->with("totaldefault", $totaldefault)
            ->with("page", "barcode")
            ->with(["label" => $label[$arrStoreKey[0]]])
            ->with("printer", $printer)
            ->with("input", $input)
            ->with("arrKey", $arrKey)
            ->with("shipmentId", $shipmentId)
            ->with("arrInfor", $arrInput)
            ->with("defSequence", json_encode($arrStoreKey))
            ->with('defaultValues', $defaultValues[$arrStoreKey[0]]);
    }

    public static function showLabelDefinitionBatch($batch_id, $transType)
    {

        // dd($arrInput, $shipmentId);
        session()->put('batch_id', "$batch_id");

        return redirect()->route('PrintPreviewLabelProcessBatch', ['transType' => $transType]);
    }
    public function templatehtml()
    {
        $placeholders = ['%whse_num%', '%item_num%', '%item_desc%', '%loc_num%', '%no_box%'];

        $data = [
            ['MAIN', 'I001', 'Long Item Description goes here', 'L001', 2],
        ];

        /* $template ='
          <table width="100%">
          <tr>
          <td width="15%">Item :</td>
          <td>%item_label%</td>
          <tr>
          <td width="15%">Desc :</td>
          <td>%item_desc%</td>
          </tr>
          <tr>
          <td><b>%item_num%</b>.</b></td>
          <td>%item_label%</td>
          </tr>
          <tr>
          <td>Loc:</td>
          <td>%loc_label%</td>
          </tr>
          </table>';
         */

        $template = '
            <div class="container">
                            <div class="row">
                                <div class="col-xs-2">
                                    Item
                                </div>
                                <div class="col-xs-10">
                                    %item_label%
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-xs-2">
                                    Item
                                </div>
                                <div class="col-xs-8">
                                    this is a very long item description. i dont know why it is so long
                                </div>
                            </div>
                        </div>
                            </div>';
        $newphrase = '';

        $item_label = DNS1D::getBarcodeSVG($data[0][1], "C128", 2, 50, "black", true);
        foreach ($data as $data) {
            $newphrase .= str_replace($placeholders, $data, $template);
            $newphrase = str_replace('%item_label%', '<img src="data:image/svg+xml;base64,' . base64_encode($item_label) . '"  height="30" />', $newphrase);
            $newphrase = str_replace('%loc_label%', $dnsd->getBarcodeHTML($input['loc_num'], "C128", 2, 50, "black", true), $newphrase);
        }

        $pdf = PDF::loadView('barcode.template', compact('newphrase'))
            ->setOption('page-width', '100')
            ->setOption('page-height', '60');
        //return $pdf->stream();

        return view('barcode.template')->with('newphrase', $newphrase);
    }

    /**
     * Pallet
     * Pallet Transaction Label
     * <AUTHOR>
     * @param $lpn_id, $transType
     */
    public static function GetPalletLabelData($lpn_id, $transDate, $qty, $transType)
    {


        //if transaction date is null
        if ($transDate == null) {
            // $transDate = Carbon::now()->toDateString();
            $transDate = getDateTimeConverted();
        }

        //select pallet for details
        // $lpn = Container::select("lpn_num")
        //     ->where("id", $lpn_id)
        //     ->first();
        $lpn = Container::where("id", $lpn_id)->with("container_item")->first();
        //$lpn_lines = ContainerItem::
        $cust = Customer::select('cust_name')->where('cust_num', $lpn->cust_num)->first();

        // $input = array(
        //     "lpn_num" => $lpn->lpn_num,
        //     "creation_date" => $transDate,
        //     "transType" => $transType,
        //     'labelPage' => true,
        //     "qty" => $qty,

        // );
        // dd($transDate);

        $input = array(
            "lpn" => $lpn->lpn_num,
            "lpn_num" => $lpn->lpn_num,
            "whse_num" => $lpn->whse_num,
            "loc_num" => $lpn->loc_num,
            "single_item" => $lpn->single_item,
            "cust_num" => $lpn->cust_num,
            "cust_name" => $cust->cust_name ?? null,
            "pallet_creation_date" => Timezone::convertFromUTC($lpn->created_date, auth()->user()->getSiteTimezone(), SiteSetting::getOutputDateFormat()),
            "ref_no" => $lpn->ref_num,
            "ref_line" => $lpn->ref_line,
            "item_num" => $lpn->container_item[0]->item_num,
            "lot_num"  => $lpn->container_item[0]->lot_num,
            "qty_contained" => $lpn->container_item[0]->qty_contained,
            "uom" => $lpn->container_item[0]->uom,
            "site_name" => $lpn->container_item[0]->site_id,
            "creation_date" => $transDate,
            "transType" => $transType,
            'labelPage' => true,
            "qty" => $qty
        );
        $input = self::getLpnDetail($input);
        $input = self::getItemImage($input);
        // dd($lpn,$input);
        //return to handler function
        return $input;
    }
    public static function getSiteTimeZoneDate()
    {
        $datetime = Timezone::convertFromUTC(now(), auth()->user()->getSiteTimezone(), SiteSetting::getOutputDateFormat() . ' H:i:s');
        return $datetime;
    }
}
