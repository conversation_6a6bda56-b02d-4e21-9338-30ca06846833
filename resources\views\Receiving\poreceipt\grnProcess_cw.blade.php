@extends('layout.mobile.app')

@section('content')
@section('title', __('GRN Receipt - CW'))
<style>
    .card{
        box-shadow:0px 0px 0px transparent;
        border: 1px solid transparent;}
    div.col-xs-2.col-md-1.col-lg-1, div.col-xs-2.col-md-1.col-lg-2{
        margin-top: 6px;}

    form .form-group {
        margin-bottom: 0.2rem;
    }
</style>

<div class="card-body collapse in">
    <div class="card-block-custom">
        <x-catch-weight-form
            :batch-id="$batch_id"
            :whsenum="$ItemList->whse_num"
            :itemnum="$ItemList->item_num"
            :itemdesc="$ItemList->item_desc"
            :refnum="$poItem->po_num"
            :refline="$poItem->po_line"
            :tolerance="$ItemList->qty_balance"
            :toleranceuom="$ItemList->uom"
            :submiturl="route('runGrnCWProcess')"
            :catch-weight-tolerance="$ItemList->item->catch_weight_tolerance"
            :disable-create-new-item-loc="$disable_create_new_item_location"
            :line-uom="$poItem->uom"
            :print-label="$printLabel"
            trans-type="PoReceipt"
            transtype="po">

            <input type="hidden" name="vend_num" id="vend_num" value="{{ $ItemList->vend_num }}">
            <input type="hidden" name="non_inv" id="non_inv" value="{{ $non_inv }}">
            <input type="hidden" name="ref_release" id="po_rel" class="form-control border-primary" value="{{ $poItem->po_rel }}">
            <input type="hidden" name="grn_num" id="grn_num" class="form-control border-primary" value="{{ $ItemList->grn_num }}">
            <input type="hidden" name="grn_line" id="grn_line" class="form-control border-primary" value="{{ $ItemList->grn_line }}">
            
            <!-- @if($sap_trans_order_integration==1)
                {{-- Document num --}}
                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control required" for="document_num">{{__('mobile.label.doc')}}</label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <div class="input-group">
                            <input type="text" name="document_num" id="document_num" required autocomplete="off" class="form-control border-primary" placeholder="{{__('mobile.placeholder.doc')}}" value="{{ old( 'document_num',$last_doc_num ) }}" maxlength="30">
                        </div>
                    </div>
                </div>

                {{-- Last Receive? --}}
                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control" for="last_receive">{{__('mobile.label.last_receive')}}</label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <div class="input-group">
                            <input type="radio" name="last_receive" value="Yes" id="last_receive"> {{ __('mobile.option.yes') }} &emsp;&emsp;&emsp;
                            <input type="radio" name="last_receive" value="No" id="last_receive_no" checked> {{ __('mobile.option.no') }}
                        </div>
                    </div>
                </div>
            @endif -->

            <div class="form-group row pt-1">
                <label class="col-xs-3 col-md-3 col-lg-3 label-control-custom required" for="loc_num">{{ __('mobile.label.loc_num') }}</label>
                <div class="col-xs-7 col-md-7 col-lg-7">
                    <div class="input-group">
                        <input type="text" id="loc_num" class="form-control border-primary" placeholder="{{__('mobile.placeholder.loc_num')}}" name="loc_num" value="{{old('loc_num',$def_loc) }}" maxlength="30">
                        <span id="locnumnotexist"></span>
                        <span id="checkLoc"></span>
                    </div>
                </div>
                <div class="col-xs-1 col-md-1 col-lg-1" style="margin-left:-0.8em; padding:0px;">
                    <button type="button" name="{{__('mobile.list.locations')}}" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal" onclick="selectionNull('/getLocNoPicking','whse_num,loc_num','loc_num','loc_num');modalheader(this.id,this.name)"><i class="icon-search"></i></button>
                </div>
            </div>

            <x-slot name="additionalFields">
                @if($ItemList->lot_tracked == 1)
                    <div class="form-group row">
                        <label class="col-xs-3 col-md-2 col-lg-3 label-control-custom" for="vend_lot">{{ __('mobile.label.vend_lot') }}</label>
                        <div class="col-xs-7 col-md-7 col-lg-7">
                            <div class="input-group">
                                <input type="text" maxlength="30" id="vend_lot" value="{{old('vend_lot')}}" class="form-control border-primary" placeholder="{{__('mobile.placeholder.vend_lot')}}" name="vend_lot" >
                            </div>
                        </div>
                    </div>
                @endif
                <div class="form-group row">
                    <div class="input-group">
                        <p class="col-xs-12 col-md-12 col-lg-12 label-control-custom required" style="word-break: break-all; word-wrap: break-word">
                            {{ __('mobile.label.po_num') }} : {{ $ItemList->po_num }} | {{ $ItemList->po_line }}
                    </div>
                </div>
            </x-slot>
            
        </x-catch-weight-form>
    </div>
</div>

<script>
    function historyBack() {

        var URLRedirect = "{{ @$url }}";
        url = URLRedirect.replace(/&amp;/g, "&");

        window.location.href = url;

    }
</script>

@endsection()
