<?php

namespace App\Http\Controllers;

// use App\SAPb1\SAPClient;
// use App\SAPb1\Filters\Equal;
// use App\SAPb1\Service;
// use App\SAPb1\Config;
// use App\Services\CallHttpService;
// use Illuminate\Support\Facades\Crypt;

use App\Services\SapCallService;

use Illuminate\Http\Request;
use App\Job;
use App\JobRoute;
use App\Warehouse;
use App\Loc;
use App\UomConv;
use App\Services\GeneralService;
use App\Item;
use App\Container;
use Alert;
use App\Exports\JobReceiptExport;
use App\Exports\JobReceiptExportV2;
use App\Services\LotService;
use Maatwebsite\Excel\Facades\Excel;
use App\View\TparmView;
use DB;
use Exception;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Carbon;
use Illuminate\Validation\ValidationException;
use App\Services\PalletService;
use App\ItemLoc;
use App\ContainerItem;
use App\JobMatl;
use App\Services\SiteConnectionService;
use App\Services\SapApiCallService;
use App\Services\PreassignLotsService;
use App\Services\CatchWeightService;

use Illuminate\Support\Facades\Log;

class JobReceiptController extends Controller
{
    public function index(Request $request)
    {
        if (!\Gate::allows('hasJobReceipt')) {
            return view('errors.404')->with('page', 'error');;
        }


        $tparms = new TparmView();
        $unit_quantity_format = $tparms->getTparmValue('System', 'decimal_setting');
        $tparm = $tparms->getTparmValue('JobReceipt', 'enable_warehouse');
        $def_loc = $tparms->getTparmValue('JobReceipt', 'def_location');
        $printLabel = $tparms->getTparmValue('JobReceipt', 'print_label');
        $loc = json_decode($def_loc);

        if ($def_loc != '') {
            $loc = json_decode($def_loc);
            foreach ($loc as $k) {
                $loc_whse_num =  $k->whse_num;
                $loc_loc_num =  $k->loc_num;
            }

            if (auth()->user()->getCurrWhse() == $loc_whse_num)
                $def_loc = $loc_loc_num;
            else
                $def_loc = '';
        } else $def_loc = $def_loc;

        $allow_over_receive = $tparms->getTparmValue('JobReceipt', 'allow_over_receive');
        $allow_incomplete_qty = $tparms->getTparmValue('JobReceipt', 'allow_incomplete_qty');
        $disable_create_new_item_location = $tparms->getTparmValue('JobReceipt', 'disable_create_new_item_location');
        $tparm_receipt_location = $tparms->getTparmValue('JobReceipt', 'receipt_location') ? 'JobReceipt' : 0;

        $lpnDef = PalletService::getDefaultLpnTransaction('Job Receipt');
        $batch_id = generateBatchId("Job Receipt");

        // check url parameter
        if ($request->item_num) {
            $item = Item::where('item_num', $request->item_num)->first();
            $job = $request->ref_num;
            $suffix = $request->ref_line;

            $job_order = Job::where('job_num', $job)->where('suffix', $suffix)->first();

            $view = 'production.jobreceipt.process_cw';
            return view($view)
                ->with('tparm', $tparm)
                ->with('def_loc', $def_loc)
                ->with('batch_id', $batch_id)
                ->with('allow_over_receive', $allow_over_receive)
                ->with('allow_incomplete_qty', $allow_incomplete_qty)
                ->with('disable_create_new_item_location', $disable_create_new_item_location)
                ->with('unit_quantity_format', $unit_quantity_format)
                ->with('def_lpn', $lpnDef)
                ->with('tparm_receipt_location', $tparm_receipt_location)
                ->with('ItemList', $item)
                ->with('job_num', $job)
                ->with('suffix', $suffix)
                ->with('printLabel', $printLabel)
                ->with('job_order', $job_order);
        } else {
            $view = 'production.jobreceipt.process';
            return view($view)
                ->with('tparm', $tparm)
                ->with('def_loc', $def_loc)
                ->with('batch_id', $batch_id)
                ->with('allow_over_receive', $allow_over_receive)
                ->with('allow_incomplete_qty', $allow_incomplete_qty)
                ->with('disable_create_new_item_location', $disable_create_new_item_location)
                ->with('unit_quantity_format', $unit_quantity_format)
                ->with('def_lpn', $lpnDef)
                ->with('tparm_receipt_location', $tparm_receipt_location);
        }
    }

    public function checkMatlRequirement($request)
    {
        // dd($request->all());
        $qty_completed = $request->qty_completed;
        $qty_to_complete = $request->qty;
        $tparm = new TparmView;
        $require_full_material_issue_for_job_receipt = $tparm->getTparmValue('JobReceipt', 'require_full_material_issue_for_job_receipt');
        // dd($require_full_material_issue_for_job_receipt);
        if ($require_full_material_issue_for_job_receipt) {
            $job_matls = JobMatl::where('job_num', $request->ref_num)->get();

            foreach ($job_matls as $key => $job_matl) {
                # code...
                // ;
                $qty_issued = ($qty_completed + $qty_to_complete) * $job_matl->qty_per * (1 / (1 - $job_matl->scrap_factor));
                $qty_issued = numberFormatPrecision($qty_issued,8);

                // echo $qty_issued . "_" . $job_matl->qty_issued . "_" . $job_matl->matl_item . "<br>";

                // dd($qty_issued, $job_matl->qty_issued, $job_matl);
                if ($qty_issued > $job_matl->qty_issued) {
                    throw ValidationException::withMessages([__('error.mobile.insufficient_qty_issued')]);

                    return false;
                }
            }
        }
        // dd("a");
    }

    public function JobReceiptProcess(Request $request)
    {



        $batch_id = $request->batch_id;

        if ($batch_id && checkBatchIdExists($batch_id)) {
            throw ValidationException::withMessages([__('error.admin.batch_id_exists')]);
        }

        $this->checkMatlRequirement($request);

        $request = validateSansentiveValue($request);

        $tparm = new TparmView;
        $sap_require_check_online = $tparm->getTparmValue('System', 'sap_check_online_connection');

        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

        if ($sap_require_check_online == 1 && $sap_trans_order_integration == 1) {
            // Checking SAP Server
            $site_id = auth()->user()->site_id;
            $checkConnection = SapApiCallService::getSQPServerConnection($site_id, null, 'Job Receipt', 1);

            if ($checkConnection > 2) {
                Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_server_down'))->persistent('Dismiss');
                return redirect()->back();
            }
        }
        DB::beginTransaction();
        try {
            if (isset($request->qty_rcv)) {
                $qty = 0;
                foreach ($request->qty_rcv as $qty_rcv) {
                    $qty += $qty_rcv;
                }
                $request['qty'] = $qty;
            }
            //            dd($request->qty);
            $request->validate([
                'item_num' => 'required|exists:items,item_num,item_status,1,site_id,' . auth()->user()->site_id,
                'whse_num' => 'required|exists:warehouses,whse_num,whse_status,1,site_id,' . auth()->user()->site_id,
            ], [
                'item_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.items')]),
                'whse_num.exists' =>  __('error.mobile.processinactive', ['resource' => __('mobile.list.warehouses')]),
            ]);

            // Send error if job_num's job_status is not released
            $checkJobNum = Job::where('job_num', $request->ref_num)->where('suffix', $request->suffix)->first(); //->where('job_status', '!=', "R")->exists();

            // Verifying Job exist
            if (!$checkJobNum) {
                throw ValidationException::withMessages([__('error.mobile.notexist', ['resource' => '[' . $request->ref_num . '-' . $request->suffix . ']'])]);
            }
            if ($checkJobNum->job_status != "R") {
                throw ValidationException::withMessages(['job_num' => 'Job Order-' . $request->ref_num . ' cannot be proceed due to status is not released.']);
            }

            // Rewrite the base_uom in case of tampering
            $request['base_uom'] = $checkJobNum->uom;

            // Store in Location
            $loc = new Loc;
            $checkLoc = $loc->where('loc_num', $request->loc_num)->where('whse_num', $request->whse_num)->first();
            // If not exist, store it
            if (!$checkLoc) {
                $loc->whse_num = $request->whse_num;
                $loc->loc_num = $request->loc_num;
                $loc->loc_type = "S";
                $loc->loc_status = 1;
                $loc->save();
            } else {
                if ($checkLoc->loc_status == 0) {
                    throw ValidationException::withMessages([__('error.mobile.processinactive', ['resource' => __('mobile.list.locations')])]);
                }
            }
            $itemToTrack = new Item();
            // Is item lot tracked?
            $lot_tracked = $itemToTrack->select('lot_tracked', 'item_desc')
                ->where('item_num', $request->item_num)
                ->first();

            $transDate = Carbon::now()->toDateTimeString();
            Session::put('timestamp', $transDate);

            // check item loc freeze
            $itemLoc = new ItemLoc();
            $itemLoc = $itemLoc->where('whse_num', $request->whse_num)->where('loc_num', $request->loc_num)->where('item_num', $request->item_num)->first();
            if ($itemLoc && $itemLoc->freeze == 'Y') {
                throw ValidationException::withMessages([__('error.mobile.loc_freeze', ['resource' => $request->item_num, 'resource2' => $request->loc_num])]);
            }

            if ($request->lpn_num != "") {
                $convertUom = UomConv::convertUOM($request->base_uom, $request->uom, $request->uom, $request->qty, $request->item_num, '', '', '');

                $request['qty_conv'] = $convertUom['conv_qty_to_base']['qty'];
                $request['uom_conv'] = $convertUom['conv_qty_to_base']['uom'];

                $toArr = [
                    'whse_num' => $request->whse_num,
                    'loc_num' => $request->loc_num,
                    'lot_num' => $request->lot_num,
                    'item_num' => $request->item_num,
                    'qty_conv' => $request->qty_conv,
                    'uom_conv' => $request->uom_conv,
                ];

                $fromArr = [];

                // $updateItemLocation = GeneralService::updateItemLocationQty($request->whse_num, $request->loc_num, $request->item_num, $uom_conv['qty'], $request->lot_num, $request->uom, 0, null);
                $updateJobReceipt = GeneralService::updateJobReceipt($request->ref_num, $convertUom['conv_qty_to_base']['qty'], $request);
                $updateLotLoc = PalletService::updateItemLocLotQty($fromArr, $toArr, 0, 'To Job Receipt');
                $insertPalletData = PalletService::updatePalletItemQty($request);


                $line = ContainerItem::select('lpn_line')->where('lpn_num', $request->lpn_num)->where('site_id', auth()->user()->site_id)->where('created_by', auth()->user()->name)->orderByRaw('CAST(lpn_line as UNSIGNED) DESC')->first();
                $request->merge([
                    'lpn_line' => $line->lpn_line
                ]);
                $insertMatl = GeneralService::newMatlTrans('Pallet Job Receipt', $request);
            } else {
                $executeJobReceipt = GeneralService::executeJobReceipt($request);
            }


            LotService::updateLot("Job Receipt", $request);

            // Future webhook here
            Alert::success('Success', __('success.processed', ['process' => __('Job Receipt')]));

            if ($request->lpn_num != null) {
                Alert::success('Success', __('success.added', ['resource' => __('Pallet(s)')]));
            }

            $item = new Item();
            $itemdesc = $item->getItemDesc($request->item_num);

            Session::put('modulename', 'JobReceipt');


            // SAP Intergration
            if ($sap_trans_order_integration == 1) {
                //$res =SapCallService::postJobReceipt($request);
                $tparm = new TparmView;
                $sap_single_bin = $tparm->getTparmValue('System', 'sap_single_bin');



                if (config('icapt.enable_sap_ap_readfrom_maltrans')) {
                    if ($sap_single_bin == 1) {
                        $result = SiteConnectionService::postIntergrationTrans("Job Receipt", $request);
                    } else {
                        $result = SapCallService::postInventoryGenEntries('Job Receipt', $request);
                    }
                } else {
                    $request->merge([
                        'sap_single_bin' => $sap_single_bin
                    ]);

                    if (config('icapt.enable_sap_resync')) {
                        $result = SapCallService::postJobReceiptResync($request);
                    } else {
                        $result = SapCallService::postJobReceipt($request);
                    }
                }
                if ($result != 200) {
                    Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result)->persistent('Dismiss');
                }
            }

            // update preassign lots
            $uom_conv = UomConv::convert($request->uom, $request->qty, $request->item_num, null, null, null);
            PreassignLotsService::updatePreassignLot('job', $request->ref_num, $request->suffix, $request->item_num, $request->lot_num, auth()->user()->site_id, $uom_conv['qty']);

            DB::commit();

            $whse_num = $request->whse_num;
            $job_num = $request->ref_num;
            $suffix = $request->suffix;
            $item_num = $request->item_num;
            $item_desc = $request->item_desc;


            $qty = $request->qty;

            $uom = $request->uom;
            $loc_num = $request->loc_num;
            $lot_num = $request->lot_num;
            $document_num = $request->document_num;

            Session::put('modulename', 'JobReceipt');

            if ($request->lot_num != null) {
                $check_expiry_date = LotService::getExpiryDate($request);

                // Generate barcode
                $input = BarcodeController::GetJobReceiptLabelData($job_num, $suffix, $item_num, $item_desc, $qty, $uom, $loc_num, $lot_num, $check_expiry_date, $transDate, $document_num, 'JobReceipt');
            } else {
                // Generate barcode
                $input = BarcodeController::GetJobReceiptLabelData($job_num, $suffix, $item_num, $item_desc, $qty, $uom, $loc_num, $lot_num, null, $transDate, $document_num, 'JobReceipt');
            }

            $tparm = new TparmView;
            $print_label = $tparm->getTparmValue('JobReceipt', 'print_label');

            if ($print_label == 1) {
                return BarcodeController::showLabelDefinition($input);
            } else {
                return app('App\Http\Controllers\RouteController')->BackButton();
            }
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    public function JobReceiptCWProcess(Request $request)
    {
        $batch_id = $request->batch_id;

        if ($batch_id && checkBatchIdExists($batch_id)) {
            throw ValidationException::withMessages([__('error.admin.batch_id_exists')]);
        }

        // Validate JO
        $job_num = $request->ref_num;
        $suffix = $request->ref_line;
        if (empty($job_num) || empty($suffix))
        {
            throw ValidationException::withMessages([ __('error.mobile.does_not_exists', ['resource' => __('admin.label.job_order') ]) ]);
        }

        $job_order = Job::where('job_num', $job_num)->where('suffix', $suffix)->first();
        if (!$job_order)
        {
            throw ValidationException::withMessages([ __('error.mobile.does_not_exists', ['resource' => __('admin.label.job_order') ]) ]);
        }

        // Get Tolerance and UOM
        $tolerance = $job_order->qty_receivable;
        $tolerance_uom = $job_order->uom;

        // Validate Catch Weight Data
        CatchWeightService::validateCatchWeightData($request, $tolerance, $tolerance_uom);

        $request->merge([
            'qty' => array_sum($request->arr_qty ?? []),
            'base_uom' => $job_order->uom
        ]);

        $this->checkMatlRequirement($request);

        $tparm = new TparmView;
        $sap_require_check_online = $tparm->getTparmValue('System', 'sap_check_online_connection');

        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

        if ($sap_require_check_online == 1 && $sap_trans_order_integration == 1) {
            // Checking SAP Server
            $site_id = auth()->user()->site_id;
            $checkConnection = SapApiCallService::getSQPServerConnection($site_id, null, 'Job Receipt', 1);

            if ($checkConnection > 2) {
                Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_server_down'))->persistent('Dismiss');
                return redirect()->back();
            }
        }
        DB::beginTransaction();
        try {
            if ($job_order->job_status != "R") {
                throw ValidationException::withMessages(['job_num' => 'Job Order-' . $request->ref_num . ' cannot be proceed due to status is not released.']);
            }

            // Store in Location
            $loc = new Loc;
            $checkLoc = $loc->where('loc_num', $request->loc_num)->where('whse_num', $request->whse_num)->first();
            // If not exist, store it
            if (!$checkLoc) {
                $loc->whse_num = $request->whse_num;
                $loc->loc_num = $request->loc_num;
                $loc->loc_type = "S";
                $loc->loc_status = 1;
                $loc->save();
            } else {
                if ($checkLoc->loc_status == 0) {
                    throw ValidationException::withMessages([__('error.mobile.processinactive', ['resource' => __('mobile.list.locations')])]);
                }
            }
            $itemToTrack = new Item();
            // Is item lot tracked?
            $lot_tracked = $itemToTrack->select('lot_tracked', 'item_desc')
                ->where('item_num', $request->item_num)
                ->first();

            $transDate = Carbon::now()->toDateTimeString();
            Session::put('timestamp', $transDate);
            Session::put('print_status', 'No');

            // check item loc freeze
            $itemLoc = new ItemLoc();
            $itemLoc = $itemLoc->where('whse_num', $request->whse_num)->where('loc_num', $request->loc_num)->where('item_num', $request->item_num)->first();
            if ($itemLoc && $itemLoc->freeze == 'Y') {
                throw ValidationException::withMessages([__('error.mobile.loc_freeze', ['resource' => $request->item_num, 'resource2' => $request->loc_num])]);
            }

            // $executeJobReceipt = GeneralService::executeJobReceipt($request);
            $updateItemLocation = CatchWeightService::updateItemLocLotNMatlTrans($request, $tolerance_uom, config('icapt.transtype.job_receipt'), "Job Receipt");

            $uom_conv = UomConv::convert($request->uom, $request->qty, $request->item_num, null, null, null);
            $updateJobReceipt = GeneralService::updateJobReceipt($request->ref_num, $uom_conv['qty'], $request);

            // Future webhook here
            Alert::success('Success', __('success.processed', ['process' => __('Job Receipt')]));

            if ($request->lpn_num != null) {
                Alert::success('Success', __('success.added', ['resource' => __('Pallet(s)')]));
            }

            $item = new Item();
            $itemdesc = $item->getItemDesc($request->item_num);

            Session::put('modulename', 'JobReceipt');


            // SAP Intergration
            if ($sap_trans_order_integration == 1) {
                //$res =SapCallService::postJobReceipt($request);
                $tparm = new TparmView;
                $sap_single_bin = $tparm->getTparmValue('System', 'sap_single_bin');



                if (config('icapt.enable_sap_ap_readfrom_maltrans')) {
                    if ($sap_single_bin == 1) {
                        $result = SiteConnectionService::postIntergrationTrans("Job Receipt", $request);
                    } else {
                        $result = SapCallService::postInventoryGenEntries('Job Receipt', $request);
                    }
                } else {
                    $request->merge([
                        'sap_single_bin' => $sap_single_bin
                    ]);

                    if (config('icapt.enable_sap_resync')) {
                        $result = SapCallService::postJobReceiptResync($request);
                    } else {
                        $result = SapCallService::postJobReceipt($request);
                    }
                }
                if ($result != 200) {
                    Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result)->persistent('Dismiss');
                }
            }

            DB::commit();

            $whse_num = $request->whse_num;
            $job_num = $request->ref_num;
            $suffix = $request->suffix;
            $item_num = $request->item_num;
            $item_desc = $request->item_desc;


            $qty = $request->qty;

            $uom = $request->uom;
            $loc_num = $request->loc_num;
            $lot_num = $request->lot_num;
            $document_num = $request->document_num;

            Session::put('modulename', 'JobReceipt');

            if ($request->lot_num != null) {
                $check_expiry_date = LotService::getExpiryDate($request);

                // Generate barcode
                $input = BarcodeController::GetJobReceiptLabelData($job_num, $suffix, $item_num, $item_desc, $qty, $uom, $loc_num, $lot_num, $check_expiry_date, $transDate, $document_num, 'JobReceipt');
            } else {
                // Generate barcode
                $input = BarcodeController::GetJobReceiptLabelData($job_num, $suffix, $item_num, $item_desc, $qty, $uom, $loc_num, $lot_num, null, $transDate, $document_num, 'JobReceipt');
            }

            $tparm = new TparmView;
            $print_label = $tparm->getTparmValue('JobReceipt', 'print_label');

            // if ($print_label == 1) {
            //     return BarcodeController::showLabelDefinition($input);
            // } else {
            //     return app('App\Http\Controllers\RouteController')->BackButton();
            // }
            return app('App\Http\Controllers\RouteController')->BackButton();
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    public function JobReceiptValidation(Request $request)
    {
        $result = null;
        $vinput = $request->all();
        foreach ($vinput as $name => $value) {

            if ($name == "whse_num") {
                $model = new Warehouse();
            }
            if ($name == "ref_num" || $name == "job_num") {
                $model = new Job();
                // dd($request->whse_num);
                if ($request->whse_num != "") {
                    $result = $model->where('job_num', $value)->where('whse_num', $request->whse_num)->first();
                    if ($result)
                        $result = true;
                    else
                        $result = false;
                } else {
                    $result = $model->exists($value);
                }
                // $result = $model->exists($value);
                // dd($result);

            }

            if ($name == "suffix") {
                $model = new Job();
                $result = $model->existsSuffix($value);
            }
            if ($name == "loc_num") {
                $model = new Loc();
            }
            if ($name == "lpn_num") {
                $model = new Container();
                $result = $model->exists($value);
            }
            $result = is_null($result) ? $model->exists($value) : $result;

            if ($result == true) {
                return "true";
            } else {
                return "false";
            }
        }
    }
    public static function jobReceiptValidationRules($request)
    {
        $errors = [];
        $result = ValidationController::checkJobNumValidation($request->ref_num, $request->suffix);

        if ($result !== true) {
            $errors['job_num'] = $result;
        }
        $result = ValidationController::checkTransitPickingLocValidtion($request, "loc_num", true, false, false);

        if ($result !== true) {
            $errors['loc_num'] = $result;
        }
        return $errors;
    }
}
