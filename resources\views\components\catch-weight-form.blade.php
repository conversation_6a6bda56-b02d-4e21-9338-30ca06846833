<style>
    .tolerance-unfulfill {
        /* color: #DA4453 !important; */
        border: 3px solid #DA4453;
        border-radius: 5px;
    }
    .tolerance-unfulfill:hover {
        /* color: #DA4453 !important; */
        border: 3px solid #DA4453;
        border-radius: 5px;
    }

    .tolerance-fulfill {
        /* color: #37BC9B !important; */
        border: 3px solid #37BC9B;
        border-radius: 5px;
    }
    .tolerance-fulfill:hover {
        /* color: #37BC9B !important; */
        border: 3px solid #37BC9B;
        border-radius: 5px;
    }

    #scandiv {
        margin-top: 10px;
        margin-bottom: 10px;
    }

    .text-right {
        text-align: right;
    }
</style>

<form id="catch_weight_form" name="catch_weight_form" method="POST" action="{{ $submiturl }}">
    <div class="form-body">

        @csrf

        @if (isset($batchId))
        <input type="hidden" name="batch_id" id="batch_id" value="{{ $batchId }}" />
        @endif
        <input type="hidden" name="whse_num" id="whse_num" value="{{ $whsenum }}" />
        <input type="hidden" name="item_num" id="item_num" value="{{ $itemnum }}" />
        <input type="hidden" name="trans_type" id="trans_type" value="{{ $transtype }}"> <!-- For Pre-assign Lots -->
        <input type="hidden" name="ref_num" id="ref_num" value="{{ $refnum }}" />
        <input type="hidden" name="ref_line" id="ref_line" value="{{ $refline }}" />
        <input type="hidden" name="disableCreateNewItemLoc" id="disableCreateNewItemLoc" value="{{ $disableCreateNewItemLoc }}" />
        <input type="hidden" name="uom" id="uom" value="{{ $lineUom }}" />
        <input type="hidden" name="trans_date" id="trans_date" value="{{ $transDate }}" />

        
        <div id="scandiv">
            @include('components.form.scan_input', ['type' => 'inventory'])
        </div>


        <button type="button" id="button-details" class="btn btn-info btn-block tolerance-unfulfill" onclick="openDetails()" data-target="#CW_detail">
            <p class="text-center m-0 p-3"><strong style="font-size: 20px" id="total_count">0</strong> PCS&emsp;<strong style="font-size: 20px" id="total_weight">0</strong>/{{ $tolerance }} {{ $toleranceuom }}</p>
        </button>

        <div class="form-group row">
            <div class="col-xs-12 col-md-12 col-lg-12">
                <div class="input-group">
                    <p style="word-break: break-all; word-wrap: break-word; margin-bottom:3px;">
                        {{ __('mobile.label.item_num') }} : <strong>{{ $itemnum }}</strong> {{ $itemdesc }}
                </div>
            </div>
        </div>
        <div class="container border-top  border-light col-xs-12 col-md-12 col-lg-12 ">
            <div class="form-group row pt-1">
                <label class="col-xs-3 col-md-3 col-lg-3 label-control-custom required" for="lot_num">{{ __('mobile.label.lot_num') }}</label>
                <div class="col-xs-8 col-md-8 col-lg-7">
                    <div class="input-group">
                        <input type="text" onchange="showNewLot()" class="form-control border-primary" name="lot_num" id="lot_num" placeholder="{{__('mobile.placeholder.lot_num')}}" value="{{old('lot_num')}}" maxlength="50">
                        <span id="loc_info"></span>
                    </div>
                </div>
                <div class="col-xs-1 col-md-1 col-lg-1" style="margin-left:-0.8em; padding:0px;">
                    <button type="button" name="Lot" id="lotbtn"
                        onclick="selectionNull('/getLotpreassign','whse_num,item_num,trans_type,po_num,po_line','lot_num','lot_num');modalheader(this.id, this.name);"
                        class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                        data-target="#myModal"><i class="icon-search"></i></button>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-xs-3 col-md-3 col-lg-3 label-control-custom" for="expiry_date">{{ __('mobile.label.expiry_date') }}</label>
                <div class="col-xs-8 col-md-8 col-lg-7">
                    <div class="input-group">
                        <input type="text" id="expiry_date" name="expiry_date" class="form-control not_autop border-primary date-picker-only min-today-date" placeholder="{{__('admin.label.expiry_date')}}">
                    </div>
                    <span id="expiry_date_info"></span>
                </div>
            </div>
            <div class="form-group row pb-1">
                <label class="col-xs-3 col-md-3 col-lg-3 label-control-custom required" for="qty">{{__('Weight')}}</label>
                <div class="col-xs-6 col-md-6 col-lg-6">
                    <div class="input-group">
                        <input type="text" style="text-align:right" inputmode="numeric" name="qty" id="qty" class="form-control border-primary number-format" placeholder="{{__('Weight')}}" name="qty" value="" maxlength="30">
                    </div>
                </div>
                <div class="col-xs-2 col-md-2 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                    <button type="button" name="{{ __('Add') }}" id="btn-add-lot" onclick="addLot()" class="btn btn-primary">
                        <i class="icon-plus4"></i> {{ __('Add') }}
                    </button>
                </div>
            </div>
        </div>

        <div hidden class="container border-top border-light col-xs-12 col-md-12 col-lg-12">
            <div class="form-group row">
                {{-- initialize empty table to store multiple form data --}}
                <table id="addedLot" style="width:100%; display: hidden;">
                    <tbody>
                        @if(old('arr_lot_num'))
                            @foreach(old('arr_lot_num') as $key => $lot_num)
                                <tr data-lot-num="{{ $lot_num }}">
                                    <td>
                                        <input type="hidden" name="arr_lot_num[]" class="added-lot-num" value="{{ $lot_num }}"/>
                                        <input type="hidden" name="arr_expiry_date[]" value="{{ old('arr_expiry_date')[$key] }}"/>
                                        <input type="hidden" name="arr_qty[]" value="{{ old('arr_qty')[$key] }}"/>
                                    </td>
                                </tr>
                            @endforeach
                        @endif
                    </tbody>
                </table>
            </div>
        </div>
        @include('components.catch_weight.details')

        <div class="container border-top border-bottom col-xs-12 col-md-12 col-lg-12">
            {{ $slot }}

            @if(count($printerOptions) > 0)
                <div class="form-group row">
                    <label class="col-xs-3 col-md-3 col-lg-3 label-control-custom required" for="printer">{{ __('mobile.label.printer') }}</label>
                    <div class="col-xs-8 col-md-8 col-lg-7">
                        <div class="input-group">
                            <select class="form-control border-primary" name="printer" id="printer">
                                <option value=""></option>
                                @foreach ($printerOptions as $id => $printerOption)
                                    <option value="{{ $id }}">{{ $printerOption }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>
            @endif

            {{ $additionalFields }}
        </div>

    </div>

    <div class="form-actions center">
        <button type="button" class="btn btn-warning" onclick="historyBack()">
            <i class="icon-cross2"></i> {{__('mobile.button.cancel')}}
        </button>
        <button type="button" id="process" onclick="processForm()" class="btn btn-primary ml-1 submitloader">
            <i class="icon-check2"></i> {{__('mobile.button.process')}}
        </button>
    </div>
</form>

<script>

    // Global variables for validation
    var errorMessage = "Default Error Message";
    var addedLots = []; // Array to track added lot numbers
    var formState = "process";
    var total_count = 0;
    var total_weight = 0;
    var last_defined_lot = "";
    var default_error_lot = "This field is required.";
    
    var unitQuantityFormat = "{{ $unitQuantityFormat }}";

    // Validation vdata
    var validator = false;

    $(document).ready(function() {
        @if(old('arr_lot_num'))
            @foreach(old('arr_lot_num') as $key => $lot_num)
                addedLots.push("{{ $lot_num }}");
            @endforeach

            reCalculateQty();
        @endif
    });

    function reCalculateQty()
    {
        total_count = 0;
        total_weight = 0;

        $('#addedLot tbody tr').each(function() {
            var qty = parseFloat($(this).find('input[name="arr_qty[]"]').val().replace(/,/g, '')) || 0;
            total_count++;
            total_weight += qty;
        });

        $("#total_count").html(total_count);
        $("#total_weight").html(total_weight.toFixed(2));
    }

    function clearInputField()
    {
        // Clear input fields and reset validation state
        $('#lot_num, #expiry_date, #qty').val('');
        // Clear location info
        $("#loc_info").html('');
    }

    function getAddLotValidation()
    {
        return {
            rules: {
                lot_num: {
                    required: true,
                    @if($disableCreateNewItemLoc)
                        remote: {
                            url: "{{ route('checkLotNum') }}",
                            type: "GET",
                            data: {
                                _token: $('input[name="_token"]').val(),
                                whse_num: function() {
                                    return $("#whse_num").val();
                                },
                                loc_num: function() {
                                    return $("#loc_num").val();
                                },
                                item_num: function() {
                                    return $("#item_num").val();
                                },
                                lot_num: function() {
                                    return $("#lot_num").val();
                                },
                            },
                            dataFilter: function(data) {
                                if(data == 'not exist'){
                                    return false;
                                }
                                else
                                {
                                    return true;
                                }
                            }
                        }
                    @endif
                },
                printer: {
                    required: true,
                },
                qty: {
                    required: true,
                    number: true,
                    min: Number.MIN_VALUE, // the smallest positive (non-zero) float that JS can handle
                },
            },
            messages: {
                lot_num: {
                    required: function() {
                        return default_error_lot;
                    },
                    remote: "{{__('error.mobile.lot_notexists')}}"
                },
                qty: {
                    required: "Weight must be more than 0",
                    min: "Weight must be more than 0",
                },
                printer: {
                    required: "Please select a printer.",
                }
            },
        };
    }

    function getDefaultHeaderValidation()
    {
        return {
            rules: {
                loc_num: {
                    required: true,
                    remote: {
                        url: "{{ route('checkLocNotTransitpickLocs') }}",
                        type: "GET",
                        data: {
                            _token: $('input[name="_token"]').val(),
                            whse_num: function() {
                                return $("#whse_num").val();
                            },
                            loc_num: function() {
                                return $("#loc_num").val();
                            }
                        },
                        dataFilter: function(data) {
                            var data = JSON.parse(data);
                            if(data.length > 0){
                                if(data[0].pick_locs==1 && data[0].pick_locs!==undefined)
                                {
                                    errorMessage = "{{__('error.mobile.validate_picking')}}";
                                    return false;
                                }
                                else if(data[0].loc_type=='T')
                                {
                                    errorMessage = "{{__('error.mobile.validate_transit')}}";
                                    return false;
                                }
                                else{
                                    $("#checkLoc").html("");
                                    $(".submitloader").attr("disabled", false);

                                    return true;
                                }

                            }
                            else
                            {
                                if ({{ $disableCreateNewItemLoc ?? 0 }} == 0) {
                                    $("#checkLoc").html("");
                                    return true;
                                }
                                errorMessage = "{{__('error.mobile.loc_not_exists')}}";
                                return false;
                            }
                        }
                    }
                },
                printer: {
                    required: true,
                },
            },
            messages: {
                loc_num: {
                    remote: function() { return errorMessage; },
                },
                printer: {
                    required: "Please select a printer.",
                }
            },
        }
    }
    
    function getHeaderValidation()
    {
        return {
            rules: {},
            messages: {},
        };
    }

    function reInitFormRules(validationInfo)
    {
        if (validator)
        {
            validator.destroy();
        }

        validator = $('#catch_weight_form').validate({
            onchange: true,
            rules: validationInfo.rules,
            messages: validationInfo.messages,
            submitHandler: function(form) {
                $("#lot_num, #qty").prop('required', false);

                if (formState == 'addLot')
                {
                    addValidatedLot($('#lot_num').val(), $('#expiry_date').val(), $('#qty').val());
                    return false;
                }
                else
                {
                    $(".pageloader").css("display", "block");
                    $(".submitloader").attr("disabled", true);
                    setTimeout( function () {
                        form.submit();
                    }, 300);
                }
            },
        });
    }

    function processForm()
    {
        // if (!checkCatchWeightTolerance())
        // {
        //     Swal.fire({
        //         title: 'Weight Out of Tolerance',
        //         text: "__('error.mobile.total_weight_out_of_tolerance')",
        //         icon: 'warning',
        //         confirmButtonText: 'OK'
        //     });

        //     return false;
        // }

        // Check for total Weight
        if (total_weight <= 0)
        {
            // showAlertMessage('danger', "{{ __('error.admin.total_weight_must_greater_zero') }}");
            Swal.fire({
                title: 'No Lots Added',
                text: "{{ __('error.admin.please_add_lot') }}",
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            return false;
        }

        formState = "process";
        var header_validation = jQuery.extend(getHeaderValidation(), getDefaultHeaderValidation());
        reInitFormRules(header_validation);
        $('#catch_weight_form').submit();
    }

    function addLot() {
        formState = "addLot";
        
        default_error_lot = "This field is required.";
        reInitFormRules(getAddLotValidation());

        if ($('#qty').val())
        {
            var tolerance = {{ $tolerance ?? 0 }};
            @if (!$allowOver)
            if ((total_weight + parseFloat($('#qty').val() || 0)) > tolerance)
            {
                Swal.fire({
                    title: 'Weight Exceeded',
                    text: 'Total weight exceeds the tolerance limit.',
                    icon: 'warning',
                    confirmButtonText: 'OK'
                });

                return false;
            }
            @endif
        }


        @if($disableCreateNewItemLoc)
            if (!$("#loc_num").val())
            {
                Swal.fire({
                    title: 'Location is empty',
                    text: 'Please fill in the Location first.',
                    icon: 'warning',
                    confirmButtonText: 'OK'
                });
                return false;
            }
        @endif

        if (addedLots.includes($('#lot_num').val().trim()))
        {
            // Show confirmation dialog
            Swal.fire({
                title: 'Duplicate Lot',
                text: 'Duplicate Lot detected. Add more weight to this Lot?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes',
                cancelButtonText: 'No'
            }).then((result) => {
                if (result.value) {
                    // Add more weight to the existing lot
                    addMoreWeightToExistingLot($('#lot_num').val(), $('#qty').val(), $('#expiry_date').val());
                    clearInputField();
                }
            });

            return false;
        }

        @if(!$disableCreateNewItemLoc)
            if (!$('#lot_num').val())
            {

                // use last defined lot if it's not added yet
                if (last_defined_lot)
                {
                    if (!addedLots.includes(last_defined_lot.trim()))
                    {
                        $("#lot_num").val(last_defined_lot);
                        showNewLot().then(function() {
                            $('#catch_weight_form').submit();
                        });

                        return true;
                    }
                }

                $.post("{{ route('getLotNumDef') }}", {
                    _token: $('input[name="_token"]').val(),
                    item_num: $("#item_num").val(),
                    last_defined_lot: last_defined_lot,
                },
                function(data){
                    if (data != "false") {
                        last_defined_lot = data;
                        $("#lot_num").val(data);
                        showNewLot().then(function() {
                            $('#catch_weight_form').submit();
                        });
                    }
                    else {
                        default_error_lot = "{{ __('error.admin.lot_definition_missing') }}";
                        $('#catch_weight_form').submit();
                    }
                });

                return false; // Stop the code to prevent double form submission
            }
        @endif
        
        $('#catch_weight_form').submit();
    }

    function addValidatedLot(lot_num, expiry_date, qty) {

        // Add to tracking array
        addedLots.push(lot_num.trim());

        // Ensure the hidden table has a tbody
        if ($('#addedLot tbody').length === 0) {
            $('#addedLot').append('<tbody></tbody>');
        }

        // Create hidden inputs for form submission with unique IDs
        var timestamp = Date.now();
        var lot_num_input = '<input type="hidden" name="arr_lot_num[]" class="added-lot-num" value="' + lot_num.trim() + '"/>';
        var expiry_date_input = '<input type="hidden" name="arr_expiry_date[]" value="' + (expiry_date || '') + '"/>';
        var qty_input = '<input type="hidden" name="arr_qty[]" value="' + qty + '"/>';

        // Create hidden row for form submission
        var hiddenRow = '<tr data-lot-num="' + lot_num.trim() + '" data-timestamp="' + timestamp + '">' +
            '<td>' + lot_num_input + expiry_date_input + qty_input + '</td>' +
            '</tr>';

        // Append to hidden table
        $('#addedLot tbody').append(hiddenRow);

        // Update UI count with proper number parsing
        var qtyValue = parseFloat(qty.replace(/,/g, '')) || 0;
        updateUICount(qtyValue, 1);

        // Clear input fields and reset validation state
        clearInputField();

        // Reset form state
        formState = "process";
        $("#lot_num, #qty").prop('required', false);

        if (checkCatchWeightTolerance())
        {
            $('#button-details').removeClass('tolerance-unfulfill');
            $('#button-details').addClass('tolerance-fulfill');
        }
        else
        {
            $('#button-details').removeClass('tolerance-fulfill');
            $('#button-details').addClass('tolerance-unfulfill');
        }

        setLocsReadOnly();

        @if($printLabel)
            callAjaxPrint(lot_num, expiry_date, qty);
        @endif

        return true;
    }


    function updateUICount(qty, count = 1)
    {
        total_count += parseInt(count);
        total_weight += parseFloat(qty.toString().replace(/,/g, ''));

        // Ensure totals don't go below zero
        total_count = Math.max(0, total_count);
        total_weight = Math.max(0, total_weight);

        $("#total_count").html(total_count);
        $("#total_weight").html(numberFormatPrecision(total_weight, unitQuantityFormat));
    }

    // Enhanced openDetails function
    function openDetails() {
        // Check if there are any added lots
        // if ($('#addedLot tbody tr').length === 0) {
        //     // Show warning if no lots have been added
        //     Swal.fire({
        //         title: 'No Lots Added',
        //         text: 'Please add at least one lot before viewing details.',
        //         icon: 'warning',
        //         confirmButtonText: 'OK'
        //     });
        //     return false;
        // }

        // Replace the title
        $("#CW_detail h4.modalheader b").html("{{ __('mobile.title.weight_detail') }}");

        // Show the modal
        $('#CW_detail').modal('show');

        // Clear the detail table first
        $('#detailtable tbody').empty();

        // Get data from addedLot table and populate detail table
        var modalTotalWeight = 0;
        var modalTotalCount = 0;

        $('#addedLot tbody tr').each(function(index, row) {
            var inputs = $(row).find('input');
            var lot_num = inputs.filter('[name="arr_lot_num[]"]').val() || '';
            var expiry_date = inputs.filter('[name="arr_expiry_date[]"]').val() || '-';
            var qty = inputs.filter('[name="arr_qty[]"]').val() || '0';

            // Parse quantity for totals
            var qtyValue = parseFloat(qty.replace(/,/g, '')) || 0;
            modalTotalWeight += qtyValue;
            modalTotalCount++;

            // Create a new table row (click handler is managed by event delegation in details.blade.php)
            var newRow = $('<tr data-index="' + index + '" style="cursor: pointer;">');
            newRow.append($('<td>').text(lot_num));
            newRow.append($('<td>').text(expiry_date));
            newRow.append($('<td class="text-right">').text(modalTotalCount)); // Box number (sequential)
            newRow.append($('<td class="text-right">').text(numberFormatPrecision(qty, unitQuantityFormat)));

            $('#detailtable tbody').append(newRow);
        });

        if ($('#addedLot tbody tr').length == 0)
        {
            var newRow = $('<tr>');
            newRow.append($('<td colspan="4" class="text-center">').text("No records"));

            $('#detailtable tbody').append(newRow);
        }

        // Update the total weight and count in the modal
        updateModalTotals(modalTotalWeight, modalTotalCount);
    }

    // Function to update modal totals
    function updateModalTotals(totalWeight, totalCount) {
        // Update total weight display
        var weightDisplay = numberFormatPrecision(totalWeight, unitQuantityFormat);
        var lineuom = "{{ $lineUom }}";

        // Find and update the total weight and count spans in the modal
        $('#CW_detail .modal-body .row').each(function() {
            var spans = $(this).find('span');
            if (spans.length >= 2) {
                var labelSpan = spans.first();
                var valueSpan = spans.last();

                // Check for total weight
                if (labelSpan.text().includes('Total Weight') ||
                    labelSpan.text().includes('total_weight') ||
                    labelSpan.text().includes('{{ __("mobile.label.total_weight") }}')) {
                    valueSpan.text(weightDisplay + ' ' + lineuom);
                }

                // Check for count
                if (labelSpan.text().includes('Count') ||
                    labelSpan.text().includes('count') ||
                    labelSpan.text().includes('{{ __("mobile.label.count") }}')) {
                    valueSpan.text(totalCount);
                }
            }
        });
    }

    // Enhanced deleteRow function for the details modal
    function deleteRow() {
        // Get the selected rows from the detail table
        var selectedRows = $('#detailtable tbody tr.active');

        if (selectedRows.length === 0) {
            Swal.fire({
                title: 'No Selection',
                text: 'Please select at least one row to delete.',
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            return false;
        }

        // Show confirmation dialog
        Swal.fire({
            title: 'Confirm Deletion',
            text: 'Delete the selected lot(s)?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.value) {
                var deletedLots = [];
                var indicesToRemove = [];

                // Collect indices to remove
                selectedRows.each(function() {
                    var rowIndex = $(this).data('index');
                    if (rowIndex !== undefined) {
                        indicesToRemove.push(rowIndex);
                    }
                });

                // Sort indices in descending order to remove from end to beginning
                indicesToRemove.sort(function(a, b) { return b - a; });

                // Remove lots from the hidden table and tracking arrays
                indicesToRemove.forEach(function(rowIndex) {
                    var addedLotRow = $('#addedLot tbody tr').eq(rowIndex);

                    if (addedLotRow.length > 0) {
                        // Get lot data before removal
                        var lot_num = addedLotRow.find('input[name="arr_lot_num[]"]').val();
                        var qty = addedLotRow.find('input[name="arr_qty[]"]').val();

                        if (lot_num) {
                            deletedLots.push(lot_num.trim());

                            // Remove from tracking array
                            var index = addedLots.indexOf(lot_num.trim());
                            if (index > -1) {
                                addedLots.splice(index, 1);
                            }

                            // Update UI counts
                            if (qty) {
                                var qtyValue = parseFloat(qty.replace(/,/g, '')) || 0;
                                updateUICount(-qtyValue, -1);
                            }
                        }

                        // Remove the row from addedLot table
                        addedLotRow.remove();
                    }
                });

                showAlertMessage('success', '{{ __("success.deleted", ["resource" => __("admin.label.lot")]) }}', '#CW_detail .modal-content');

                // Refresh the modal content or close if no lots remain
                if ($('#addedLot tbody tr').length === 0) {
                    // Refresh the detail table
                    setTimeout(function() {
                        openDetails();
                    }, 100);
                    // $('#CW_detail').modal('hide');
                    // Reset totals
                    // total_count = 0;
                    // total_weight = 0;
                    // $("#total_count").html(total_count);
                    // $("#total_weight").html(total_weight.toFixed(2));
                } else {
                    // Refresh the detail table
                    setTimeout(function() {
                        openDetails();
                    }, 100);
                }

                setLocsReadOnly();
            }
        });
    }

    function showNewLot() {

        @if($disableCreateNewItemLoc)
            return true;
        @endif

        $("#loc_info").html('');
        $("#expiry_date_info").html("");
         $("#mfg_date_info").html("");

        $('#UOMs').show();
        if($("#lot_num").val() || $("#loc_num").val()) {
            ajaxurl ="{{ route('lotitemv', ['lot_num','item_num','whse_num'])}}";
            url = ajaxurl.replace('lot_num', btoa($("#lot_num").val()));
            url = url.replace('item_num', btoa($("#item_num").val()));
            url = url.replace('whse_num', btoa($("#whse_num").val()));

            if (addedLots.includes($('#lot_num').val().trim()))
            {
                return false;
            }

            return $.get(url, function(data){
                //console.log(data);
                if(data == 'not exist') {
                    $("#loc_info").html('<i class="icon-info"></i> <small>{{ __('mobile.message.new_lot_location') }}</small>');
                }
            });
        }

        return false;
        // $("#lot_num").focus();
        // $("#iconCalender").hide();
    }

    // Function to add more weight to an existing lot
    function addMoreWeightToExistingLot(lot_num, additional_weight, expiry_date) {
        if (!lot_num || !lot_num.trim()) {
            console.error('Lot number is required');
            return false;
        }

        if (!additional_weight || isNaN(additional_weight) || parseFloat(additional_weight) <= 0) {
            console.error('Valid additional weight is required');
            return false;
        }

        // Find the existing lot in the hidden table
        var existingLotRow = $('#addedLot tbody tr[data-lot-num="' + lot_num.trim() + '"]');

        if (existingLotRow.length === 0) {
            console.error('Lot not found: ' + lot_num);
            return false;
        }

        // Get current weight
        var currentWeightInput = existingLotRow.find('input[name="arr_qty[]"]');
        var currentWeight = parseFloat(currentWeightInput.val().replace(/,/g, '')) || 0;

        // Calculate new weight
        var newWeight = currentWeight + parseFloat(additional_weight);

        // Update the weight in the hidden table
        currentWeightInput.val(newWeight.toFixed(2));

        // Update UI counts
        updateUICount(parseFloat(additional_weight), 0); // Add weight but don't add count

        if (checkCatchWeightTolerance())
        {
            $('#button-details').removeClass('tolerance-unfulfill');
            $('#button-details').addClass('tolerance-fulfill');
        }
        else
        {
            $('#button-details').removeClass('tolerance-fulfill');
            $('#button-details').addClass('tolerance-unfulfill');
        }

        @if($printLabel)
            callAjaxPrint(lot_num, expiry_date, additional_weight);
        @endif

        return true;
    }

    function checkCatchWeightTolerance()
    {
        var tolerance = {{ $tolerance ?? 0 }};
        var toleranceRate = {{ $catchWeightTolerance ?? 0 }};
        var totalWeight = parseFloat($("#total_weight").text()) || 0;
        var expectedWeight = tolerance;
        var minTolerance = expectedWeight - (toleranceRate / 100 * expectedWeight);
        var maxTolerance = expectedWeight + (toleranceRate / 100 * expectedWeight);

        if (totalWeight >= minTolerance && totalWeight <= maxTolerance)
        {
            return true;
        }
        else
        {
            return false;
        }
    }

    function setLocsReadOnly()
    {
        @if($disableCreateNewItemLoc)
            if (addedLots.length > 0)
            {
                $("#loc_num").prop('readonly', true);
            }
            else
            {
                $("#loc_num").prop('readonly', false);
            }
        @endif

        return true;
    }

    function callAjaxPrint(lot_num, expiry_date, qty)
    {
        // current time
        const now = new Date();
        const hours = now.getHours();
        const minutes = now.getMinutes();
        const seconds = now.getSeconds();

        var transType = "{{ $transType ?? '' }}";
        var tempTransType = $("#trans_type").val();

        // Change trans type to follow Barcode Controller
        $("#trans_type").val(transType);
        
        let formData = $('#catch_weight_form').serializeArray();
        let data = {};
        $.each(formData, function () {
            data[this.name] = this.value || '';
        });

        // remove array and add lot_num, expiry_date, qty
        delete data['arr_lot_num[]'];
        delete data['arr_expiry_date[]'];
        delete data['arr_qty[]'];
        data['lot_num'] = lot_num;
        data['expiry_date'] = expiry_date;
        data['qty'] = qty;

        // add time to trans date
        data['trans_date'] = data['trans_date'] + " " + hours + ":" + minutes + ":" + seconds;

        // Change trans type back to original
        $("#trans_type").val(tempTransType);

        return $.ajax({
            url: '{{ route('printCatchWeight') }}',
            type: 'POST',
            data: data,
            success: function (data)
            {
                if (!data.status)
                {
                    // show error with timeout
                    showAlertMessage('danger', data.message);

                    // Swal.fire({
                    //     title: 'ERROR',
                    //     text: data.message,
                    //     icon: 'warning',
                    //     confirmButtonText: 'OK'
                    // });
                }
                else
                {
                    // show success
                    showAlertMessage('success', data.message);
                }
            }
        });
    }

    function showAlertMessage(type, message, element = '.card-header')
    {
        $('#custom-error').remove();

        let successMessage = '<div class="alert alert-' + type + '" id="custom-error">' + message + '</div>';
        $(element).prepend(successMessage);

        // setTimeout(function() {
        //     $('#custom-error').remove();
        // }, 3000);
    }

</script>

@include('util.selection')
@include('util.datepicker')
@include('errors.maxchar')