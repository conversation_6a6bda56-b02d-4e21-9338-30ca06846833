<?php

namespace App;

use App\Scopes\siteScope;
use Illuminate\Database\Eloquent\Model;
use App\Services\OverrideQtyService;
use App\TransParm;

class TparmSite extends Model
{
    protected $fillable = [
        'site_id',
        'tparm_id',
        'tparm_value',
        'created_by',
        'modified_by',
    ];
    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    protected static function boot()
    {
        parent::boot();
        static::updating(function ($model) {
            if ($model->isDirty('tparm_value')) {
                $tparm = TransParm::find($model->tparm_id);
                if ($tparm->module_label == 'decimal_setting') {
                    OverrideQtyService::newOverRideHistory(__('admin.title.trans_parm'), $tparm->tparm_label, $model->getOriginal('tparm_value'), $model->tparm_value, null, null, null);
                }
            }
        });
        static::withoutGlobalScopes();
    }
}
