<?php

namespace App\View\Components;

use Illuminate\View\Component;
use App\SiteSetting;
use App\Http\Controllers\BarcodeController;
use App\View\TparmView;

class CatchWeightForm extends Component
{

    public $batchId;
    public $whsenum;
    public $itemnum;
    public $itemdesc;
    public $refnum;
    public $refline;
    public $tolerance;
    public $toleranceuom;
    public $submiturl;
    public $transtype;
    public $transType;
    public $catchWeightTolerance;
    public $disableCreateNewItemLoc;
    public $allowOver;
    public $printerOptions;
    public $printLabel;
    public $lineUom;
    public $transDate;
    public $unitQuantityFormat;

    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct(
        $batchId = "", $whsenum = "", $itemnum = "", $itemdesc = "", $refnum = "", $refline = "", $tolerance = "", $toleranceuom = "", 
        $submiturl = "", $transtype = "", $transType = "", $catchWeightTolerance = 0, $disableCreateNewItemLoc = true, 
        $allowOver = false, $printerOptions = [], $printLabel = false, $lineUom = "", $transDate = "", $unitQuantityFormat = ""
    )
    {
        $this->batchId = $batchId;
        $this->whsenum = $whsenum;
        $this->itemnum = $itemnum;
        $this->itemdesc = $itemdesc;
        $this->refnum = $refnum;
        $this->refline = $refline;
        $this->tolerance = $tolerance;
        $this->toleranceuom = $toleranceuom;
        $this->submiturl = $submiturl;
        $this->transtype = $transtype;
        $this->transType = $transType;
        $this->catchWeightTolerance = $catchWeightTolerance;
        $this->disableCreateNewItemLoc = $disableCreateNewItemLoc;
        $this->allowOver = $allowOver;
        $this->printLabel = $printLabel;
        $this->lineUom = $lineUom;

        if (empty($transDate))
        {
            SiteSetting::getOutputDateFormat();
            $transDate = date(SiteSetting::getOutputDateFormat());
        }
        $this->transDate = $transDate;
        
        if (empty($printerOptions))
        {
            $printerOptions = BarcodeController::getPrintersData();
        }
        $this->printerOptions = $printerOptions;

        if (empty($unitQuantityFormat))
        {
            $tparm = new TparmView();
            $unitQuantityFormat = $tparm->getTparmValue('System', 'decimal_setting');
        }
        $this->unitQuantityFormat = $unitQuantityFormat;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\Contracts\View\View|\Closure|string
     */
    public function render()
    {
        return view('components.catch-weight-form');
    }
}
