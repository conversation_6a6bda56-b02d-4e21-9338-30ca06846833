<?php

namespace App\Http\Controllers;

use App\NewTempRegister;
use App\Plan;
use App\User;
use App\Group;
use Carbon\Carbon;
use App\SiteSetting;
use App\Jobs\SendEmailJob;
use Illuminate\Http\Request;
use App\Rules\StrongPassword;
use Illuminate\Validation\Rule;
use App\Mail\RegisterZohoEmail;
use App\Mail\InternalNewSubscription;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Alert;
use App\Services\CallHttpService;
use Illuminate\Validation\ValidationException;
use LangleyFoxall\LaravelNISTPasswordRules\PasswordRules;
use Illuminate\Support\Facades\Session;
use Auth;
use Illuminate\Auth\Events\Registered;
use App\helpers;
use App\DefaultLabel;
use App\Http\Controllers\Super\SuperController;
use Camroncade\Timezone\Facades\Timezone;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class WebHookController extends Controller
{

    public function readjson()
    {
        $path = public_path('resp.txt');
        //$path = public_path('data.json');
        // Data comes from zoho site
        if (!file_exists($path)) {
            return view('errors.invalidurl');
            exit;
        }

        $data = file_get_contents($path);
        $r = json_decode($data);
        $arrFinal = array();
        echo $r->DocumentEntry;
        foreach ($r->InventoryCountingLines as $key => $value) {
            $arrFinal[$value->ItemCode . $value->BinEntry] =  $value->LineNumber;
        }

        echo "<pre>";
        print_R(json_encode($arrFinal));
    }

    public function testAPI()
    {

        // $urlApi = 'subscriptions.zoho.com/api';
        // $urlApi = 'www.zohoapis.com/billing';
        $urlApi = config('icapt.zoho_apisetting.subscriptionBillingUrl');
        $open_subscription = config('icapt.zoho_apisetting.open_subscription');

        // dd($open_subscription,$urlApi);

        $client_id = config('icapt.zoho_apisetting.client_id');
        $client_secret = config('icapt.zoho_apisetting.client_secret');
        $scope = config('icapt.zoho_apisetting.scope');
        $state = config('icapt.zoho_apisetting.state');
        $response_type = config('icapt.zoho_apisetting.response_type');
        $authorization_code = config('icapt.zoho_apisetting.authorization_code');
        $refresh_token = config('icapt.zoho_apisetting.refresh_token');
        $redirect_uri = "http://localhost/zohoapi/sub.php";
        $access_type = config('icapt.zoho_apisetting.access_type');
        $organizationId = config('icapt.zoho_apisetting.organizationId');
        $refeshtoken = config('icapt.zoho_apisetting.refeshtoken');
        $apiUrl = config('icapt.zoho_apisetting.apiUrl');
        //$zoho_company_id =  config('icapt.zoho_apisetting.organizationId');

        // $urlApi = config('icapt.zoho_apisetting.subscriptionBillingUrl');
        $urlSubscriptsApi = config('icapt.zoho_apisetting.subscriptionApiUrl');

        $subscriptionUrl = 'https://' . $urlApi . '/v1/subscriptions/';


        //dd($urlBillingApi, $urlSubscriptsApi, $subscriptionUrl);
        // Refresh Token and Get the API
        $postParameter = array(
            'refresh_token' => $refeshtoken,
            'client_id' => $client_id,
            'client_secret' => $client_secret,
            'redirect_uri' => $redirect_uri,
            'grant_type' => 'refresh_token',
        );
        $result = CallHttpService::postZohoApi($apiUrl, $postParameter);
        $AuthorizationCode = "Zoho-oauthtoken " . $result->access_token;
        // $urlPrice = "https://invoice.zoho.com/api/v3/organizations";
        // $urlPrice ="https://subscriptions.zoho.com/api/v1/subscriptions";
        //dd($result);
        $plan_code = "AX-MT-FREE-M";
        // $urlPrice = 'https://subscriptions.zoho.com/api/v1/plans/';
        $urlPrice = 'https://' . $urlApi . '/v1/plans/' . $plan_code;
        // 724131921
        // @$resp = CallHttpService::getZohoApi($urlPrice, null, '724131921', $AuthorizationCode);
        // @$resp = CallHttpService::getZohoApi($urlPrice, null, '737839505', $AuthorizationCode);
        @$resp = CallHttpService::getZohoApi($urlPrice, null, $organizationId, $AuthorizationCode);
        $res = json_decode($resp);


        //dd($urlPrice, $res, $subscriptionUrls);



        $gprice = $res->plan->recurring_price;

        $paymentfailed = 'https://' . $urlApi . '/v1/payments?filter_by=PaymentStatus.OnlineFailure';
        @$resp = CallHttpService::getZohoApi($paymentfailed, null, $organizationId, $AuthorizationCode);
        $res = json_decode($resp);

        $status = 'healthy';
        //dd( $res->message);
        if($res->message =="success")
        {

             echo $subscriptionUrl;
             echo "<br>";
             echo "<pre>";
             print_R($res);
             exit;

            // die();
            // return view('admin.sucessfulconnect');
            //return redirect('https://test.axacute.com/login');
            // return response()->json([
            //     "name" => "Zoho Application",
            //     "status" => "ok",
            //     "notificationMessage"=>"",
            //     "shortSummary" => "Reachable",
            //     "meta" => [],
            // ], 200);
        }
        else{
            return response()->json([
                "name" => "Zoho Application",
                "status" => "failed",
                "notificationMessage"=>"Failed to connect to Zoho API",
                "shortSummary" => "failed",
                "meta" => [],
            ], 400);
        }

        //echo $subscriptionUrl;
        // echo "<br>";
        // echo "<pre>";
        // print_R($res);
        //return "successful";



        // echo "XXXXX::" . $gprice;
    }

    public function create(Request $request)
    {
        // Need to put super admin permission here

        /*
          if(!Gate::allows('hasSuperAdmin')){
          return view('errors.404')->with('page','error'); ;
          }
         */
        $path = public_path('zoho_application/new/' . $request->subscription_id . '.json');
        //$path = public_path('data.json');
        // Data comes from zoho site
        if (!file_exists($path)) {
            return view('errors.invalidurl');
            exit;
        }

        $data = file_get_contents($path);
        $r = json_decode($data);

        $plan_id = $r->data->subscription->plan->plan_id;
        $plan_code = $r->data->subscription->plan->plan_code;
        $max_user = $r->data->subscription->plan->quantity;
        $zoho_customer_id = $r->data->subscription->customer_id;
        $zoho_event_type = $r->event_type;
        $zoho_product_name = $r->data->subscription->product_name;
        $event_id = $r->event_id;
        $subscription_id = $request->subscription_id;
        $mobile = $r->data->subscription->contactpersons[0]->mobile;
        $phone = $r->data->subscription->contactpersons[0]->phone;
        $first_name = $r->data->subscription->customer->first_name;
        $last_name = $r->data->subscription->customer->last_name;

        $dataInfo = [
            'plan_id' => $plan_id,
            'plan_code' => $plan_code,
            'max_user' => $max_user,
            'zoho_customer_id' => $zoho_customer_id,
            'zoho_event_type' => $zoho_event_type,
            'zoho_product_name' => $zoho_product_name,
            'event_id' => $event_id,
            'subscription_id' => $subscription_id,
            'mobile' => $mobile,
            'phone' => $phone,
            'first_name' => $first_name,
            'last_name' => $last_name
        ];

        $dataInfo = json_encode($dataInfo);

        return view('admin.register')->with('dataInfo', $dataInfo);
    }

    // For redirect purpose
    public function newsubpurl(Request $request)
    {

        $referral_partner = DB::table('partner_referral')->select('partner_name')->where('urlCode', $request->referral_id)->get();
        $referral_name = "";
        if (isset($referral_partner[0])) {
            $referral_name = $referral_partner[0]->partner_name;
        }
        $plan_name = DB::table('plans')->select('plan_name')->where('plan_code', $request->plan_code)->value('plan_name');

        $information = array(
            'Starter' => array(
                'Header_left' => '',
                'Header_right' => 'Get your Starter Plan 14 days free trial today!',
                'Content' => 'Suitable for businesses that need to manage basic inventory operations at the warehouse only',
                'Content2' => 'Try Axacute to know how easy it is to:',
                'Content3' => '<ul style="list-style-type:none;padding-left:-10px;">
        <li> - Track inventory level in real time</li>
        <li> - Simplify your warehouse activities</li>
        <li> - Auto allocate stock by FEFO, FIFO</li>
        <li> - Optimize picking process via mobile pick list</li>
        <li> - Manage warehouse by zone</li>
        <li> - Integrate with your accounting system</li>

        </ul>',
            ),
            'Professional' => array(
                'Header_left' => '',
                'Header_right' => 'Get your Professional Plan 14 days free trial today!',
                'Content' => 'Suitable for businesses that need to manage basic inventory operations at the warehouse only',
                'Content2' => 'Try Axacute to know how easy it is to:',
                'Content3' => '<ul style="list-style-type:none;padding-left:-10px;">
        <li> - Track inventory level in real time</li>
        <li> - Simplify your warehouse activities</li>
        <li> - Auto allocate stock by FEFO, FIFO</li>
        <li> - Optimize picking process via mobile pick list</li>
        <li> - Manage warehouse by zone</li>
        <li> - Track your shopfloor labor productivity</li>
        <li> - Monitor your job status and progress</li>
        <li> - Maintain bill of materials</li>
        <li> - Integrate with your accounting system</li>


        </ul>',
            ),
            'Enterprise' => array(
                'Header_left' => '',
                'Header_right' => 'Get your Enterprise Plan 14 days free trial today!',
                'Content' => 'Full features of Professional Plan as well as machine utilization tracking for more automated operations',
                'Content2' => 'Try  Axacute to know how easy it is to:',
                'Content3' => '<ul style="list-style-type:none;padding-left:-10px;">
        <li> - Track inventory level in real time</li>
        <li> - Simplify your warehouse activities</li>
        <li> - Auto allocate stock by FEFO, FIFO</li>
        <li> - Optimize picking process via mobile pick list</li>
        <li> - Manage warehouse by zone</li>
        <li> - Track your shopfloor labor and machine productivity</li>
        <li> - Monitor your job status and progress</li>
        <li> - Maintain bill of materials</li>
        <li> - Integrate with your accounting system</li>

        </ul>',
            ),
            'Free' => array(
                'Header_left' => 'Start with two users for FREE now!',
                'Header_right' => 'Get Free 2 Users now',
                'Content' => 'Suitable for small business to manage basic inventory and production operations at no cost.',
                'Content2' => 'Try Axacute to know how easy it is to:',
                'Content3' => '<ul style="list-style-type:none;">
                                 <li> - Track inventory level in real time</li>
                                 <li> - Simplify your warehouse activities</li>
                                 <li> - Auto allocate stock by FEFO, FIFO</li>
                                 <li> - Optimize picking process via mobile pick list</li>
                                 <li> - Track your shopfloor labor productivity</li>
                                 <li> - Monitor your job status and progress</li>
                                 <li> - Maintain bill of materials</li>
                                 <li> - Integrate with your accounting system</li>
                                 </ul>'
            )
        );

        if ($request->plan_code == "" || $plan_name == "") {
            // Alert::error('Warning', 'Invalid plan code!!');
            //Alert::error('Error','Invalid plan code.');
            return redirect()->back();
        }

        return view('admin.newsubscription')
            ->with('plan_code', $request->plan_code)
            ->with('plan_name', $plan_name)
            ->with('information', $information)
            ->with('referral_id', $request->referral_id)
            ->with('referral_name', $referral_name);
    }





    public function newsubscription(Request $request)
    {



        $referral_partner = DB::table('partner_referral')->select('partner_name')->where('urlCode', $request->ref)->get();
        $referral_name = "";
        if (isset($referral_partner[0])) {
            $referral_name = $referral_partner[0]->partner_name;
        }


        // dd($request, $referral_partner[0]->partner_name);

        //$businesstype = config('constants.businesstype');
        //dd($businesstype[3]);

        $plan_name = DB::table('plans')->select('plan_name')->where('plan_code', $request->plan_code)->value('plan_name');


        // return view('admin.newsubscriptionurl')
        // ->with('plan_code', $request->plan_code)
        // ->with('plan_name', $plan_name)
        // ->with('referral_id', $request->ref)
        // ->with('referral_name', $referral_name);










        $information = array(
            'Starter' => array(
                'Header_left' => '',
                'Header_right' => 'Get your Starter Plan 14 days free trial today!',
                'Content' => 'Suitable for businesses that need to manage basic inventory operations at the warehouse only',
                'Content2' => 'Try Axacute to know how easy it is to:',
                'Content3' => '<ul style="list-style-type:none;padding-left:-10px;">
        <li> - Track inventory level in real time</li>
        <li> - Simplify your warehouse activities</li>
        <li> - Auto allocate stock by FEFO, FIFO</li>
        <li> - Optimize picking process via mobile pick list</li>
        <li> - Manage warehouse by zone</li>
        <li> - Integrate with your accounting system</li>

        </ul>',
            ),
            'Professional' => array(
                'Header_left' => '',
                'Header_right' => 'Get your Professional Plan 14 days free trial today!',
                'Content' => 'Suitable for businesses that need to manage basic inventory operations at the warehouse only',
                'Content2' => 'Try Axacute to know how easy it is to:',
                'Content3' => '<ul style="list-style-type:none;padding-left:-10px;">
        <li> - Track inventory level in real time</li>
        <li> - Simplify your warehouse activities</li>
        <li> - Auto allocate stock by FEFO, FIFO</li>
        <li> - Optimize picking process via mobile pick list</li>
        <li> - Manage warehouse by zone</li>
        <li> - Track your shopfloor labor productivity</li>
        <li> - Monitor your job status and progress</li>
        <li> - Maintain bill of materials</li>
        <li> - Integrate with your accounting system</li>


        </ul>',
            ),
            'Enterprise' => array(
                'Header_left' => '',
                'Header_right' => 'Get your Enterprise Plan 14 days free trial today!',
                'Content' => 'Full features of Professional Plan as well as machine utilization tracking for more automated operations',
                'Content2' => 'Try  Axacute to know how easy it is to:',
                'Content3' => '<ul style="list-style-type:none;padding-left:-10px;">
        <li> - Track inventory level in real time</li>
        <li> - Simplify your warehouse activities</li>
        <li> - Auto allocate stock by FEFO, FIFO</li>
        <li> - Optimize picking process via mobile pick list</li>
        <li> - Manage warehouse by zone</li>
        <li> - Track your shopfloor labor and machine productivity</li>
        <li> - Monitor your job status and progress</li>
        <li> - Maintain bill of materials</li>
        <li> - Integrate with your accounting system</li>

        </ul>',
            ),
            'Free' => array(
                'Header_left' => 'Start with two users for FREE now!',
                'Header_right' => 'Get Free 2 Users now',
                'Content' => 'Suitable for small business to manage basic inventory and production operations at no cost.',
                'Content2' => 'Try Axacute to know how easy it is to:',
                'Content3' => '<ul style="list-style-type:none;">
                                 <li> - Track inventory level in real time</li>
                                 <li> - Simplify your warehouse activities</li>
                                 <li> - Auto allocate stock by FEFO, FIFO</li>
                                 <li> - Optimize picking process via mobile pick list</li>
                                 <li> - Track your shopfloor labor productivity</li>
                                 <li> - Monitor your job status and progress</li>
                                 <li> - Maintain bill of materials</li>
                                 <li> - Integrate with your accounting system</li>
                                 </ul>'
            )
        );

        if ($request->plan_code == "" || $plan_name == "") {
            // Alert::error('Warning', 'Invalid plan code!!');
            //Alert::error('Error','Invalid plan code.');
            return redirect()->back();
        }

        return view('admin.newsubscription')
            ->with('plan_code', $request->plan_code)
            ->with('plan_name', $plan_name)
            ->with('information', $information)
            ->with('referral_id', $request->ref)
            ->with('referral_name', $referral_name);
    }

    public function newstore(Request $request)
    {
        //dd($request);
        $string = strtoupper(chr(rand(65, 90)) . chr(rand(65, 90)) . rand(100, 999));
        $token_code = time() . rand(11000, ********) . $string;

        $createSite = DB::table('new_tmp_registers')->insertOrIgnore(
            [
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'contact' => $request->contact['full'],
                'email' => $request->email,
                'plan_code' => $request->plan_code,
                'uniq_code' => $token_code,
                'referral_name' => $request->referral_name,
                'status' => 0,
                'ip' => $_SERVER['REMOTE_ADDR'],
                'created_at' => now(),
                'updated_at' => now()
            ]
        );
        //dd($request);
        $country =  $request->phonecountry;
        $contact = $request->contact['main'];
        $countrycode = $request->phonecountrycode;
        $countryname = $request->phonecountryname;

        $phoneNumber = $countrycode.$contact;

        $request->merge([
            'phone' => $phoneNumber
        ]);


        $valdiationRules = [
            'email' =>
            [
                'required',
                'max:30',
                'unique:users',
            ],

            // 'contact.main' =>
            // [
            //     'max:12',
            //     'min:10',
            //     'regex:/^([0-9\s\-\+\(\)]*)$/',
            // ],
            'phone' => 'phone:'.strtoupper($country),
        ];







        if (config('icapt.recaptcha.key')) {
            $valdiationRules['g-recaptcha-response'] = 'required|recaptcha';
        }

        $request->validate(
            $valdiationRules,
            [
                'contact.main.regex' => __('error.admin.alpha_dash'),
                'contact.main.max' => __('error.admin.max_phonenum'),
                'contact.main.min' => __('error.admin.min_phonenum'),
                'phone.phone' => 'The phone number must be a valid '.ucfirst($countryname).' phone number.',


            ]
        );

        $this->validate(request(), [
            'email' => 'nullable|email:rfc,dns',
        ]);
        $url = config('app.url') . '/webhook/verifyemailacc/token_code=' . $token_code;
        Session::put('token', $token_code);

        //dd($url);
        //$response = true;
        //$response->status = "success";
        //die(json_encode($response));
        // Email to User with URL
        //echo $url;
        //exit;
        //return view('admin.thanksregister')->with('lastname',$request->last_name)->with('email', $request->email);
        return redirect()->route('thankspage')->with('token_code', $token_code)->with('referral_name', $request->referral_name);
    }

    public function thankspage(Request $request)
    {

        $getSession = Session::get('token');
        if ($getSession == "") {
            return redirect()->back();
        }

        $info = DB::table('new_tmp_registers')->where('uniq_code', $getSession)->first();

        // dd($info->first_name);
        Session::put('token', '');
        $url = config('app.url') . '/webhook/verifyemailacc/token_code=' . $getSession;
        $userEmail = $info->email;
        $zohomail = new \stdClass();
        $zohomail->url = $url;
        $zohomail->name = $info->first_name;

        $zohomail->sender = "<EMAIL>";
        $zohomail->title = "Confirm your email address for Axacute";
        Mail::to($userEmail)->send(new RegisterZohoEmail($zohomail));
        //return view('mail.registerZoho')->with('zohomail',$zohomail);






        return view('admin.thanksregister')->with('firstname', $info->first_name)->with('email', $info->email)->with('url', $url)->with('referral_name', $info->referral_name);
    }

    public function verifyemailacc(Request $request)
    {
        // Check the DB
        $info = DB::table('new_tmp_registers')->where('uniq_code', $request->token_code)->first();

        //dd($info);
        if ($info == null) {

           // throw ValidationException::withMessages([__('error.mobile.sys_error_token_missing')]);
            Alert::error(__('error.mobile.system_error'), __('error.mobile.sys_error_token_missing'))->persistent('Dismiss');
            //return redirect()->route('/');
            //Alert::error('Error', 'Invalid plan code.');
            return redirect()->back();

           // exit;
           // return redirect()->back();
        } else {
            if ($info->plan_code == "AX-MT-FREE-M") {
                $plan_name = "Free Plan";
            } else if ($info->plan_code == "AX-MT-STR-M" || $info->plan_code == "AX-MT-STR-A") {
                $plan_name = "Starter Plan";
            } else if ($info->plan_code == "AX-MT-PRO-M" || $info->plan_code == "AX-MT-PRO-A") {
                $plan_name = "Professional Plan";
            } else if ($info->plan_code == "AX-MT-ENT-M" || $info->plan_code == "AX-MT-ENT-A") {
                $plan_name = "Enterprise Plan";
            }
            $businesstype = config('constants.businesstype');
            $industry = config('constants.industry');
            $primaryinterest = config('constants.primaryinterest');
            $companysize = config('constants.companysize');


        }
        return view('admin.newsubscription2')
        ->with('businesstype', $businesstype)
        ->with('industry', $industry)
        ->with('primaryinterest', $primaryinterest)
        ->with('companysize', $companysize)
        ->with('firstname', $info->first_name)
        ->with('token_code', $request->token_code)
        ->with('plan_name', $plan_name);
    }

    public function newstorebussinesinfo(Request $request)
    {

        $result = DB::table('new_tmp_registers')->where(['uniq_code' => $request->token])->update([
            'company_name' => $request->company_name,
            'business_type' => $request->businesstype,
            'industry_type' => $request->industry,
            'no_of_emp' => $request->companysize,
            'primary_interest' => $request->primaryinterest,
            'status' => 1,
            'updated_at' => now()
        ]);
        Session::put('token', $request->token);
        return redirect()->route('finalstepssubscription');
    }

    public function creditcardpage()
    {
        return view('admin.creditcardnotice');
    }

    public function finalstepssubscription(Request $request)
    {


        // $getSession = Session::get('token');
        // if ($getSession == "") {
        //     return redirect()->back();
        // }
        if(request('design')=="") {

                $getSession = Session::get('token');
            if ($getSession == "") {
                return redirect()->back();
            }
        }
        else{

             $getSession= "172915807957038753KM714";
        }
        $info = DB::table('new_tmp_registers')->where('uniq_code', $getSession)->first();

        //dd($info);
        if ($info == null) {
            return redirect()->back();
        } else {


            return view('admin.newsubscription3')->with('plan_name', $info->plan_code)->with('token_code', $getSession);
        }
    }

    public function autoLoginSucess(Request $request)
    {
        $getSession = Session::get('tokenautologin');
        $remaining_trial_days = Session::get('remaining_trial_days');
        //if ($getSession == $request->token) {
            //dd($request->site_id,'jsjsjsj',request('site_id'));
            $site_setting_status =   DB::table('site_settings')->where('site_id', $request->site_id)->first();

            //
           // dd($site_setting_status, $request->site_id,"hshshshshxsha");
            if($site_setting_status->plan_code!="AX-MT-FREE-M" && $remaining_trial_days=="" && $site_setting_status->zoho_trial_expiry_date!==null){

            $now = Carbon::now()->toDateString();
            $now_date = new \DateTime($now);
            $trial_expiry_date = new \DateTime($site_setting_status->zoho_trial_expiry_date);
            $remaining_trial_days = $now_date->diff($trial_expiry_date)->invert ? ($now_date->diff($trial_expiry_date)->days * -1) : ($now_date->diff($trial_expiry_date)->days);

        }

            //dd($remaining_trial_days);
            //$remaining_trial_days = @$request->remaining_trial_days;
            $user = DB::table('users')->where('name', $request->adminName)->where('site_id', $request->site_id)->first();
            // dd($user);
            $cookie = cookie('logged_in_with', $user->id);
            auth()->loginUsingId($user->id);
            Session::put('remaining_trial_days', $remaining_trial_days);
            Session::put('zoho_trial_status', 'C');
            Session::put('tokenautologin', '');
            return redirect(route('web'))->withCookie($cookie);
       // } else {
          //  dd('Error', $request, $getSession);
        //}
    }


    public function createnewsite(Request $request)
    {


        //dd($request);

        $token = $request->token;
        if ($token == "") {
            return redirect()->back();
        }
        $info = DB::table('new_tmp_registers')->where('uniq_code', $token)->first();

        $site_name = $request->site_name;
        $site_id = $request->site_id;
        $timezone = $request->timezone;
        $qty = $request->qty;
        $password = $request->password;
        $password_confirmation = $request->password_confirmation;
        $adminUsername = $request->name;

        $first_name = $info->first_name;
        $last_name = $info->last_name;

        $adminEmail = $info->email;
        $contact = $info->contact;
        $plan_code = $info->plan_code;
        $company_name = $info->company_name;
        $businesstype = $info->business_type;
        $industry = $info->industry_type;
        $companysize = $info->no_of_emp;
        $primaryinterest = $info->primary_interest;

        $refer = DB::table('partner_referral')->where('partner_name', $info->referral_name)->first();


        $now = Carbon::now()->toDateTimeString();
        //$timezone = auth()->user()->timezone;
        // $date = Timezone::convertFromUTC($now, $timezone, 'Y-m-d');
        // $time = Timezone::convertFromUTC($now, $timezone, 'H:i:s');
        // $create_date = $date . ' ' . $time;
        $create_date = $now;



        // dd($info, $refer);

        $result1 = DB::table('partner_referral')->insertOrIgnore(
            [
                'partner_name' => $refer->partner_name,
                'partner_desc' => $refer->partner_desc,
                'url' => $refer->url,
                'urlCode' => $refer->urlCode,
                'referral_id' => $request->site_id,
                'created_date' => $create_date,
                'modified_date' => $create_date,

            ]
        );

        //        dd($info);
        $busnisseTypes = [
            1 => "Manufacturing or Assembly",
            2 => "E-commerce",
            3 => "Wholesale or Distribution",
            4 => "Retail",
            5 => "Others",
        ];
        $industryTypes = [
            1 => "Airlines/Aviation",
            2 => "Apparel/Fashion",
            3 => "Automotive",
            4 => "Biotechnology/Greentech",
            5 => "Building Materials",
            6 => "Business Supplies/Equipment",
            7 => "Chemicals",
            8 => "Computer Hardware",
            9 => "Construction",
            10 => "Consumer Electronics",
            11 => "Consumer Goods",
            12 => "Cosmetics",
            13 => "Dairy",
            14 => "Electrical/Electronic Manufacturing",
            15 => "Food/Beverages",
            16 => "Furniture",
            17 => "Glass/Ceramics/Concrete",
            18 => "Industrial Automation",
            19 => "Logistics/Procurement",
            20 => "Luxury Goods/Jewelry",
            21 => "Machinery",
            22 => "Medical Equipment",
            23 => "Other Industry",
            24 => "Packaging/Containers",
            25 => "Paper/Forest Products",
            26 => "Plastics",
            27 => "Semiconductors",
            28 => "Sporting Goods",
            29 => "Telecommunications",
            31 => "Textiles",
            32 => "Tobacco",
            33 => "Warehousing",
        ];

        $intrestTypes = [
            1 => "To solve inventory accuracy issue",
            2 => "To simplify warehouse activities",
            3 => "To optimize picking process",
            4 => "To track production status",
            5 => "To track labor & machine",
            6 => "Others",
        ];
        $companySizes = [
            1 => "< 5",
            2 => "5 - 20",
            3 => "21 - 50",
            4 => "51 - 250",
            5 => "> 250",
        ];

        // Zoho Create New Customer & Subscription the Plan
        /* $client_id = config('icapt.zoho_apisetting.client_id');
        $client_secret = config('icapt.zoho_apisetting.client_secret');
        $scope = config('icapt.zoho_apisetting.scope');
        $state = config('icapt.zoho_apisetting.state');
        $response_type = config('icapt.zoho_apisetting.response_type');
        $authorization_code = config('icapt.zoho_apisetting.authorization_code');
        $refresh_token = config('icapt.zoho_apisetting.refresh_token');
        $redirect_uri = "http://localhost/zohoapi/sub.php";
        $access_type = config('icapt.zoho_apisetting.access_type');
        $organizationId = config('icapt.zoho_apisetting.organizationId');
        $refeshtoken = config('icapt.zoho_apisetting.refeshtoken');
        $apiUrl = config('icapt.zoho_apisetting.apiUrl');

         $subscriptionUrl = 'https://subscriptions.zoho.com/api/v1/subscriptions/';

       // $urlApi = config('icapt.zoho_apisetting.subscriptionBillingUrl');
        $urlSubscriptsApi = config('icapt.zoho_apisetting.subscriptionApiUrl');
         $urlApi = config('icapt.zoho_apisetting.subscriptionUrls');
       // $subscriptionUrl = 'https://' . $urlApi . '/v1/subscriptions/';


        // Refresh Token and Get the API
        $postParameter = array(
            'refresh_token' => $refeshtoken,
            'client_id' => $client_id,
            'client_secret' => $client_secret,
            'redirect_uri' => $redirect_uri,
            'grant_type' => 'refresh_token',
        );
        $resultold = CallHttpService::postZohoApi($subscriptionUrl, $postParameter);
        $AuthorizationCode = "Zoho-oauthtoken " . $resultold->access_token;
        */




        // $urlApi = 'subscriptions.zoho.com/api';
        // $urlApi = 'www.zohoapis.com/billing';
        $client_id = config('icapt.zoho_apisetting.client_id');
        $client_secret = config('icapt.zoho_apisetting.client_secret');
        $scope = config('icapt.zoho_apisetting.scope');
        $state = config('icapt.zoho_apisetting.state');
        $response_type = config('icapt.zoho_apisetting.response_type');
        $authorization_code = config('icapt.zoho_apisetting.authorization_code');
        $refresh_token = config('icapt.zoho_apisetting.refresh_token');
        $redirect_uri = "http://localhost/zohoapi/sub.php";
        $access_type = config('icapt.zoho_apisetting.access_type');
        $organizationId = config('icapt.zoho_apisetting.organizationId');
        $refeshtoken = config('icapt.zoho_apisetting.refeshtoken');
        $apiUrl = config('icapt.zoho_apisetting.apiUrl');

        // $urlApi = config('icapt.zoho_apisetting.subscriptionBillingUrl');
        $urlSubscriptsApi = config('icapt.zoho_apisetting.subscriptionApiUrl');

        // $subscriptionUrl = 'https://' . $urlApi . '/v1/subscriptions/';


        //dd($urlBillingApi, $urlSubscriptsApi, $subscriptionUrl);
        // Refresh Token and Get the API
        $postParameter = array(
            'refresh_token' => $refeshtoken,
            'client_id' => $client_id,
            'client_secret' => $client_secret,
            'redirect_uri' => $redirect_uri,
            'grant_type' => 'refresh_token',
        );
        $result = CallHttpService::postZohoApi($apiUrl, $postParameter);
        if (@$result->status == "failure") {
            Log::channel('zoholog')->error(auth()->user()->site_id . " : " . @$result->error_description);
            Alert::error(__('error.mobile.zoho_error'), __('error.mobile.zoho_error_token_missing'))->persistent('Dismiss');

            return redirect()->back();
        }
        $AuthorizationCode = "Zoho-oauthtoken " . $result->access_token;
        // $urlPrice = "https://invoice.zoho.com/api/v3/organizations";
        // $urlPrice ="https://subscriptions.zoho.com/api/v1/subscriptions";







        // dd($result,$apiUrl);



        // $data = [
        //     'phone' => $phoneNumber, // Replace $phoneNumber with your input
        // ];

        // //dd($data,$phoneNumber,$countrycode,$country);
        // // $res = $request->validate( [
        // //     'contact' => $contact.':'.$country,  // specify countries (e.g., US, CA for Canada)
        // // ]);
        // //dd($contact,$request);
        // $validator = Validator::make($data, [
        //     'phone' => 'phone:'.strtoupper($country),
        // ],
        // [
        //     'phone.phone' => 'The contact number is not valid for the specified countries '.ucfirst($countryname).' .',
        // ]  // specify countries (e.g., US, CA for Canada)
        // );
        // if ($validator->fails()) {
        //     $errors = $validator->errors();
        //     $errorString = implode(",", $errors->messages()['phone']);

        //     return $errorString;
        // }


        // Register Axacute & Auto Login
        $request->validate([
            'site_id' =>
            [
                'required',
                'unique:site_settings',
                'alpha_dash',
                'max:30',
            ],
            'password' => ['confirmed', new StrongPassword, PasswordRules::register(request()->name)],

        ]

    );

        $now = Carbon::now()->toDateTimeString();

        $plan_id = DB::table('plans')->select('id')->where('plan_code', $plan_code)->first();
        $db_plan = DB::table('plans')->select('id', 'plan_code')->get();

        $arrFilterPlans = array('AX-MT-STR-M' => '1', 'AX-MT-STR-A' => '4');
        $arrFilterPlanCode = array();
        foreach ($db_plan as $key) {
            if (array_key_exists($key->plan_code, $arrFilterPlans)) {
                $arrFilterPlanCode[$key->id] = $key->plan_code;
            }
            if ($plan_code == $key->plan_code) {
                $planID = $key->id;
            }
        }

        // Check site id duplication
        $check_site_id = DB::table('site_settings')->select('site_id')->where('site_id', $site_id)->first();

        if ($check_site_id) {
            // insert into log table -> duplicate site id
        }

        // Free package
        if ($plan_code == "AX-MT-FREE-M") {
            $qty_users = 2;
        } else {
            $qty_users = $qty;
        }


        $companyInfo = array("company_name" => $company_name, "reg_num" => "", "contact_num1" => "", "company_email" => $adminEmail, "url" => "");
        $contactInfo = array("emp_lastname" => $last_name, "emp_firstname" => $first_name, "contact_num2" => $contact, "contact_num1" => $contact, "position" => "", "email" => $adminEmail);

        $company_info = json_encode($companyInfo);
        $contact_info = json_encode($contactInfo);

        // Insert into site setting
        $createSite = DB::table('site_settings')->insertOrIgnore(
            [
                'site_name' => $site_name,
                'site_id' => $site_id,
                'timezone' => $timezone,
                // 'zoho_event_id' => $zoho_event_id,
                // 'zoho_event_type' => $zoho_event_type,
                // 'zoho_subscription_id' => $zoho_subscription_id,
                // 'zoho_product_name' => $zoho_product_name,
                // 'zoho_customer_id' => $zoho_customer_id,
                'plan_code' => $plan_code,
                'plan_id' => customCrypt($planID),
                //'plan_id' => $planID,
                'status' => 1,
                'max_user' => customCrypt($qty_users),
                'registered_user' => 1,
                // 'zoho_trial_expiry_date' => ,
                'zoho_trial_status' => 'C',
                'business_type' => $businesstype,
                'industry_type' => $industry,
                'no_of_emp' => $companysize,
                'primary_interest' => $primaryinterest,
                'company_info' => $company_info,
                'contact_info' => $contact_info,
                'created_date' => $create_date,
            ]
        );

        $siteID = DB::table('site_settings')->select('id')->where('site_id', $site_id)->value('id');

        // Add system setting to the newly created site id
        $sysSetArr = ['241', '243', '247']; // System setting id for number format. 241 = Unit Quantity Decimal Points, 243 = Quantity Per Decimal Points, 247 = Total Quantity Decimal Points

        foreach ($sysSetArr as $setId) {
            $addSystemSet = DB::table('tparm_sites')->insert(
                [
                    'site_id' => $site_id,
                    'tparm_id' => $setId,
                    'tparm_value' => 2, // default value
                ]
            );
        }


        // // Adding default value
        // $TransParamsId = DB::table('trans_parms')->select('id')->where('tparm_name', 'item_warehouse_error')->where('tparm_module', 'CustomerOrder')->value('id');
        // if ($TransParamsId > 0) {
        //     $arr_tparm_sites_item_values[] = [
        //         'site_id' => $site_id,
        //         'tparm_id' => $TransParamsId,
        //         'tparm_value' => 1, // default value
        //     ];
        //     DB::table('tparm_sites')->insert($arr_tparm_sites_item_values);
        // }
        // $TransParamsId = DB::table('trans_parms')->select('id')->where('tparm_name', 'item_warehouse_error')->where('tparm_module', 'PurchaseOrder')->value('id');
        // if ($TransParamsId > 0) {
        //     $arr_tparm_sites_item_values[] = [
        //         'site_id' => $site_id,
        //         'tparm_id' => $TransParamsId,
        //         'tparm_value' => 1, // default value
        //     ];
        //     DB::table('tparm_sites')->insert($arr_tparm_sites_item_values);
        // }

        if (!$createSite) {
            DB::rollback();
        }

        // Create Admin User
        $createAdminUser = User::create(
            [
                'name' => $adminUsername,
                // 'description' => 'Admin User',
                'email' => $adminEmail,
                'password' => $request->password,
                'type' => 'site_owner',
                'site_administration' => 'Yes',
                'homepage' => 'web',
                'timezone' => $timezone,
                'site_id' => $site_id,
                'first_name' => $first_name,
                'last_name' => $last_name,
                // 'created_by' => 'system',
                'status' => 'A',
                'created_date' => $create_date,
                'modified_date' => $create_date
            ]
        );

        if (!$createAdminUser) {
            DB::rollback();
        }

        $adminId = $createAdminUser->id;

        // Give Admin Ext stuff
        DB::table('user_ext')->insert([
            ['user_id' => $adminId, 'whse_num' => '']
        ]);


        $arrSkip = ['AX-MT-STR-M', 'AX-MT-STR-A'];
        $planIDs = DB::table('site_settings')->select('site_id', 'plan_id', 'plan_code')->where('site_id', $site_id)->first();

        $group_moduleSKU = DB::table('group_modulesku')
            ->select(DB::raw($adminId . ' AS user_id'), 'group_id', DB::raw('NOW() AS created_date'), DB::raw('NOW() AS modified_date'))
            ->groupBy('group_id');

        if (in_array($planIDs->plan_code, $arrSkip)) {
            $group_moduleSKU->where('module_id', 1);
        }
        $group_moduleSKU = $group_moduleSKU->get()->map(fn($row) => get_object_vars($row))->toArray();

        // Use upsert for mass update or insert
        DB::table('user_groups')->upsert(
            $group_moduleSKU, // insert / update value
            ['user_id', 'group_id'], // unique field
            ['created_date', 'modified_date'] // field to update if matching
        );












        /*  $moduleSKU = DB::table('moduleskus')
            ->selectRaw('id, name')
            ->get();

        $arrGroup = array();

        // Check if plan code is Starter
        if (array_key_exists($planID, $arrFilterPlanCode)) {
            $group_moduleSKU = DB::table('group_modulesku')
                ->selectRaw('group_id')
                ->where('module_id', 1)
                ->get();
        } else {
            $group_moduleSKU = DB::table('group_modulesku')
                ->selectRaw('group_id')
                ->get();
        }

        foreach ($group_moduleSKU as $key => $value) {
            array_push($arrGroup, $value->group_id);
        }

        $group = DB::table('groups')
            ->selectRaw('id')
            ->whereIn('id', $arrGroup)
            ->get();

        foreach ($group as $k => $value) {
            $grpId = $value->id;

            DB::table('user_groups')->insert(
                [
                    'user_id' => $adminId,
                    'group_id' => $grpId,
                    'created_date' => $create_date,
                    'modified_date' => $create_date
                ]
            );
        }
        */

        if (array_key_exists($planID, $arrFilterPlanCode)) {
            //Create Labels
            $label_names = [
                'Inventory Label' => [
                    '[
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>',
                ],

                // 'Pallet Label' => [
                //     '[
                //         [
                //             {"size": 12, "style": "text-align:center; font-weight:bold", "content": "Company %company_name%"}
                //         ],
                //         [
                //             {"size": 12, "style": "text-align:center; font-weight:bold", "content": "LPN : %lpn_num%"}
                //         ],
                //         [
                //             {"size": 12, "style": "text-align:center; font-weight:bold", "content": "Creation Date : %creation_date%"}
                //         ],

                //         [
                //             {"size": 12, "style": "text-align:center; font-weight:bold", "content": "Box : %boxnum% / %totalbox%"}

                //         ],
                //     ]',
                //     '<div class="container">
                //     <div class="row">
                //         <div class="col-xs-12" style="text-align:center; font-weight:bold">Company %company_name%</div>
                //         <div class="col-xs-12" style="text-align:center; font-weight:bold">LPN : %lpn_num%</div>
                //         <div class="col-xs-12" style="text-align:center; font-weight:bold">%lpn_qrcode%</div>
                //         <div class="col-xs-12" style="text-align:center; font-weight:bold">Creation Date : %creation_date%</div>
                //         <div class="col-xs-12" style="text-align:center; font-weight:bold">Box : %boxnum% / %totalbox%</div>
                //     </div>
                //     </div>',

                //     ],

                'CO Shipping Label' => [
                    '[
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "CO"},
                            {"size": 10, "style": "", "content": "%co_num%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Cust"},
                            {"size": 10, "style": "", "content": "%cust_name%"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-2" style="">CO</div><div class="col-xs-10" style="">%co_num%</div></div><div class="row"><div class="col-xs-2" style="">Customer</div><div class="col-xs-10" style="">%cust_name%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                ],
                //                'Misc. Issue Label' => ['[
                //                        [
                //                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                //                        ],
                //                        [
                //                            {"size": 2, "style": "", "content": "Item_Item_Item"},
                //                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                //                        ],
                //                        [
                //                            {"size": 2, "style": "", "content": "Desc"},
                //                            {"size": 10, "style": "", "content": "%item_desc%"}
                //                        ],
                //                        [
                //                            {"size": 2, "style": "", "content": "Loc"},
                //                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                //                            {"size": 1, "style": "", "content": "Lot"},
                //                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                //                        ],
                //                        [
                //                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                //                            {"size": 4, "style": "", "content": "%expiry_date%"}
                //                        ],
                //                        [
                //                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                //                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                //                            {"size": 2, "style": "", "content": "Box"},
                //                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                //                        ],
                //                        [
                //                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                //                        ]
                //                    ]',
                //                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item_Item_Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                //                ],
                'PO Receipt Label' => [
                    '[
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "PO"},
                            {"size": 10, "style": "", "content": "%po_num%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Vendor"},
                            {"size": 10, "style": "", "content": "%vend_name%"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-2" style="">PO</div><div class="col-xs-10" style="">%po_num%</div></div><div class="row"><div class="col-xs-2" style="">Vendor</div><div class="col-xs-10" style="">%vend_name%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                ],
                'TO Shipping Label' => [
                    '[

                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "From Whse"},
                            {"size": 6, "style": "", "content": "%from_whse_qrcode%<br /><small>%from_whse%</small>"},
                            {"size": 1, "style": "", "content": "To Whse"},
                            {"size": 3, "style": "", "content": "%to_whse_qrcode%<br /><small>%to_whse%</small>"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "TO"},
                            {"size": 10, "style": "", "content": "%trn_num%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "TO Line"},
                            {"size": 10, "style": "", "content": "%trn_line%"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-3" style="">Item</div><div class="col-xs-9" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-3" style="">Desc</div><div class="col-xs-9" style="">%item_desc%</div></div><div class="row"><div class="col-xs-3" style="">From Whse</div><div class="col-xs-3" style="">%from_whse_qrcode%<br /><small>%from_whse%</small></div><div class="col-xs-3" style="">To Whse</div><div class="col-xs-3" style="">%to_whse_qrcode%<br /><small>%to_whse%</small></div></div><div class="row"><div class="col-xs-3" style="">Loc</div><div class="col-xs-3" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-3" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br/><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-3" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-3" style="">TO</div><div class="col-xs-9" style="">%trn_num%</div></div><div class="row"><div class="col-xs-3" style="">TO Line</div><div class="col-xs-9" style="">%trn_line%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                ],



                'Customer Return Label' => [
                    '[
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Return Num"},
                            {"size": 10, "style": "", "content": "%return_num%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Cust"},
                            {"size": 10, "style": "", "content": "%cust_name%"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-2" style="">Return Num</div><div class="col-xs-10" style="">%return_num%</div></div><div class="row"><div class="col-xs-2" style="">Customer</div><div class="col-xs-10" style="">%cust_name%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                ],




            ];

            foreach ($label_names as $label => $values) {
                $default_label = DefaultLabel::where('type', $label)->first();
                // dd($default_label);

                DB::table('labels')->insert(
                    [
                        'content_html' => $default_label ? $default_label->content_html : "",
                        'width' => $default_label ? $default_label->width : 100,
                        'height' => $default_label ? $default_label->height : 100,
                        'margin_left' => $default_label ? $default_label->margin_left : 0,
                        'margin_right' => $default_label ? $default_label->margin_right : 0,
                        'margin_top' => $default_label ? $default_label->margin_top : 0,
                        'margin_bottom' => $default_label ? $default_label->margin_bottom : 0,
                        'label_name' => $label,
                        'type' => $label,
                        // 'raw_content' => $values[0],
                        'content' => $values[1],
                        // 'content_html' => $values[1],
                        'is_default' => 1,
                        'site_id' => $site_id,
                        'created_date' => $now,
                        'modified_date' => $now
                    ]
                );
            }

            $label_modules = [
                'MiscReceipt',
                'MiscIssue',
                'Putaway',
                'StockMove',
                'CustOrdShipping',
                'PoReceipt',
                'TranOrderShipping',
                'TransferOrderReceipt',
                'PickNShip',
                'Picklist',
                'CustomerReturn'
            ];
        } else {

            $label_names = [
                'Inventory Label' => [
                    '[
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>',
                ],
                'CO Shipping Label' => [
                    '[
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "CO"},
                            {"size": 10, "style": "", "content": "%co_num%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Cust"},
                            {"size": 10, "style": "", "content": "%cust_name%"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-2" style="">CO</div><div class="col-xs-10" style="">%co_num%</div></div><div class="row"><div class="col-xs-2" style="">Customer</div><div class="col-xs-10" style="">%cust_name%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                ],
                //                'Misc. Issue Label' => ['[
                //                        [
                //                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                //                        ],
                //                        [
                //                            {"size": 2, "style": "", "content": "Item_Item_Item"},
                //                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                //                        ],
                //                        [
                //                            {"size": 2, "style": "", "content": "Desc"},
                //                            {"size": 10, "style": "", "content": "%item_desc%"}
                //                        ],
                //                        [
                //                            {"size": 2, "style": "", "content": "Loc"},
                //                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                //                            {"size": 1, "style": "", "content": "Lot"},
                //                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                //                        ],
                //                        [
                //                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                //                            {"size": 4, "style": "", "content": "%expiry_date%"}
                //                        ],
                //                        [
                //                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                //                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                //                            {"size": 2, "style": "", "content": "Box"},
                //                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                //                        ],
                //                        [
                //                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                //                        ]
                //                    ]',
                //                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item_Item_Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                //                ],
                'PO Receipt Label' => [
                    '[
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "PO"},
                            {"size": 10, "style": "", "content": "%po_num%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Vendor"},
                            {"size": 10, "style": "", "content": "%vend_name%"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-2" style="">PO</div><div class="col-xs-10" style="">%po_num%</div></div><div class="row"><div class="col-xs-2" style="">Vendor</div><div class="col-xs-10" style="">%vend_name%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                ],
                'Job Receipt Label' => [
                    '[
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Job"},
                            {"size": 10, "style": "", "content": "%job_qrcode% <small>%job_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-2" style="">Job</div><div class="col-xs-10" style="">%job_qrcode% <small>%job_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                ],
                'Job Material Issue Label' => [
                    '[
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Job"},
                            {"size": 10, "style": "", "content": "%job_qrcode% <small>%job_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-2" style="">Job</div><div class="col-xs-10" style="">%job_qrcode% <small>%job_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                ],


                // 'Pallet Label' => [
                //     '[
                //         [
                //             {"size": 12, "style": "text-align:center; font-weight:bold", "content": "Company %company_name%"}
                //         ],
                //         [
                //             {"size": 12, "style": "text-align:center; font-weight:bold", "content": "LPN : %lpn_num%"}
                //         ],
                //         [
                //             {"size": 12, "style": "text-align:center; font-weight:bold", "content": "Creation Date : %creation_date%"}
                //         ],

                //         [
                //             {"size": 12, "style": "text-align:center; font-weight:bold", "content": "Box : %boxnum% / %totalbox%"}

                //         ],
                //     ]',
                //     '<div class="container">
                //     <div class="row">
                //         <div class="col-xs-12" style="text-align:center; font-weight:bold">Company %company_name%</div>
                //         <div class="col-xs-12" style="text-align:center; font-weight:bold">LPN : %lpn_num%</div>
                //         <div class="col-xs-12" style="text-align:center; font-weight:bold">%lpn_qrcode%</div>
                //         <div class="col-xs-12" style="text-align:center; font-weight:bold">Creation Date : %creation_date%</div>
                //         <div class="col-xs-12" style="text-align:center; font-weight:bold">Box : %boxnum% / %totalbox%</div>
                //     </div>
                //     </div>',

                //     ],
                'TO Shipping Label' => [
                    '[

                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "From Whse"},
                            {"size": 6, "style": "", "content": "%from_whse_qrcode%<br /><small>%from_whse%</small>"},
                            {"size": 1, "style": "", "content": "To Whse"},
                            {"size": 3, "style": "", "content": "%to_whse_qrcode%<br /><small>%to_whse%</small>"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "TO"},
                            {"size": 10, "style": "", "content": "%trn_num%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "TO Line"},
                            {"size": 10, "style": "", "content": "%trn_line%"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-3" style="">Item</div><div class="col-xs-9" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-3" style="">Desc</div><div class="col-xs-9" style="">%item_desc%</div></div><div class="row"><div class="col-xs-3" style="">From Whse</div><div class="col-xs-3" style="">%from_whse_qrcode%<br /><small>%from_whse%</small></div><div class="col-xs-3" style="">To Whse</div><div class="col-xs-3" style="">%to_whse_qrcode%<br /><small>%to_whse%</small></div></div><div class="row"><div class="col-xs-3" style="">Loc</div><div class="col-xs-3" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-3" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br/><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-3" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-3" style="">TO</div><div class="col-xs-9" style="">%trn_num%</div></div><div class="row"><div class="col-xs-3" style="">TO Line</div><div class="col-xs-9" style="">%trn_line%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                ],

                'Customer Return Label' => [
                    '[
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Return Num"},
                            {"size": 10, "style": "", "content": "%return_num%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Cust"},
                            {"size": 10, "style": "", "content": "%cust_name%"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-2" style="">Return Num</div><div class="col-xs-10" style="">%return_num%</div></div><div class="row"><div class="col-xs-2" style="">Customer</div><div class="col-xs-10" style="">%cust_name%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                ],
            ];
            if ($planID != 7 && $planID != 4 && $planID != 1) {
                $label_names['Pallet Label'] = [
                    '[
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "Company %company_name%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "LPN : %lpn_num%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "Creation Date : %creation_date%"}
                        ],

                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "Box : %boxnum% / %totalbox%"}

                        ],
                    ]',
                    '<div class="container">
                    <div class="row">
                        <div class="col-xs-12" style="text-align:center; font-weight:bold">Company %company_name%</div>
                        <div class="col-xs-12" style="text-align:center; font-weight:bold">LPN : %lpn_num%</div>
                        <div class="col-xs-12" style="text-align:center; font-weight:bold">%lpn_qrcode%</div>
                        <div class="col-xs-12" style="text-align:center; font-weight:bold">Creation Date : %creation_date%</div>
                        <div class="col-xs-12" style="text-align:center; font-weight:bold">Box : %boxnum% / %totalbox%</div>
                    </div>
                    </div>',
                ];
            }

            foreach ($label_names as $label => $values) {
                $default_label = DefaultLabel::where('type', $label)->first();
                // dd($default_label);

                DB::table('labels')->insert(
                    [
                        'content_html' => $default_label ? $default_label->content_html : "",
                        'width' => $default_label ? $default_label->width : 100,
                        'height' => $default_label ? $default_label->height : 100,
                        'margin_left' => $default_label ? $default_label->margin_left : 0,
                        'margin_right' => $default_label ? $default_label->margin_right : 0,
                        'margin_top' => $default_label ? $default_label->margin_top : 0,
                        'margin_bottom' => $default_label ? $default_label->margin_bottom : 0,
                        'label_name' => $label,
                        'type' => $label,
                        'raw_content' => $values[0],
                        // 'content_html' => $values[1],
                        'is_default' => 1,
                        'content' => $values[1],
                        'site_id' => $site_id,
                        'created_date' => $now,
                        'modified_date' => $now
                    ]
                );
            }

            $label_modules = [
                'MiscReceipt',
                'MiscIssue',
                'Putaway',
                'StockMove',
                'CustOrdShipping',
                'PoReceipt',
                'JobReceipt',
                'JobMaterialIssue',
                'TranOrderShipping',
                'TransferOrderReceipt',
                'PickNShip',
                'Picklist',
                'CustomerReturn'
            ];
        }
        SuperController::addLabelObjects($planID, $arrFilterPlanCode, $site_id, $now);


        // $labels = DB::table('labels')->where('site_id', $site_id)->get();

        // // Delete and insert new label_modules data
        // DB::table('label_modules')->where('site_id', $site_id)->delete();
        // if (array_key_exists($planID, $arrFilterPlanCode)) {

        //     DB::table('label_modules')->insert([
        //         ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'Inventory Label', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'MiscReceipt', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'MiscIssue', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'Putaway', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'StockMove', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'CO Shipping Label')->id, 'modulename' => 'CustOrdShipping', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'PO Receipt Label')->id, 'modulename' => 'PoReceipt', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'TO Shipping Label')->id, 'modulename' => 'TranOrderShipping', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         // ['label_id' => $labels->firstWhere('label_name', 'Pallet Label')->id, 'modulename' => 'Pallet Label', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id]
        //     ]);
        //     //added after issue 1, 48
        //     $label_modules = [
        //         ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'CustOrdPicking', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'PickList', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'JobMaterialReturn', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'TransferOrderReceipt', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         //            ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'PickNShip', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'CO Shipping Label')->id, 'modulename' => 'PickNShip', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //     ];
        //     DB::table('label_modules')->insert($label_modules);
        // } else {
        //     DB::table('label_modules')->insert([
        //         ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'Inventory Label', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'MiscReceipt', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'MiscIssue', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'Putaway', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'StockMove', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'CO Shipping Label')->id, 'modulename' => 'CustOrdShipping', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'PO Receipt Label')->id, 'modulename' => 'PoReceipt', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'Job Receipt Label')->id, 'modulename' => 'JobReceipt', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'Job Material Issue Label')->id, 'modulename' => 'JobMaterialIssue', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'TO Shipping Label')->id, 'modulename' => 'TranOrderShipping', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         // ['label_id' => $labels->firstWhere('label_name', 'Pallet Label')->id, 'modulename' => 'Pallet Label', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id]
        //     ]);
        //     if ($planID != 7 && $planID != 4 && $planID != 1) {
        //         DB::table('label_modules')->insert([
        //             ['label_id' => $labels->firstWhere('label_name', 'Pallet Label')->id, 'modulename' => 'Pallet Label', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id]
        //         ]);
        //     }
        //     //added after issue 1, 48
        //     $label_modules = [
        //         ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'CustOrdPicking', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'PickList', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'JobMaterialReturn', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'TransferOrderReceipt', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         //            ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'PickNShip', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'CO Shipping Label')->id, 'modulename' => 'PickNShip', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //     ];
        //     DB::table('label_modules')->insert($label_modules);
        // }


        // Zoho Subscription
        // Customer Data
        /* $display_name           = $first_name;
          $salutation             = '';
          $first_name             = $first_name;
          $last_name              = $last_name;
          $email                  = $adminEmail;
          $tag_id                 = '';
          $tag_option_id          = '';
          $company_name           = $company_name;
          $phone                  = '';
          $mobile                 = '';
          $department             = '';
          $designation            = '';
          $website                = '';
          $attention              = '';
          $street                 = '';
          $city                   = '';
          $state                  = '';
          $zip                    = '';
          $country                = '';
          $country_code           = '';
          $state_code             = '';
          $fax                    = '';
          $payment_terms          = '';
          $payment_terms_label    = '';
          $currency_code          = 'USD';
          $ach_supported          = '';
          $twitter                = '';
          $facebook               = '';
          $skype                  = '';
          $notes                  = '';
          $is_portal_enabled      = '';
          $gst_no                 = '';
          $gst_treatment          = '';
          $place_of_contact       = '';
          $vat_treatment          = '';
          $vat_reg_no             = '';
          $country_code           = '';
          $is_taxable             = '';
          $tax_id                 = '';
          $tax_authority_name     = '';
          $tax_exemption_id       = '';
          $tax_exemption_code     = '';
          $invoice_template_id    = '';
          $creditnote_template_id = '';
          $label                  = '';
          $value                  = '';

          $data = array(
          "display_name"          => $display_name,
          "salutation"            => $salutation,
          "first_name"            => $first_name,
          "last_name"             => $last_name,
          "email"                 => $email,
          // "tags"                  => [array(
          //                         "tag_id"        => $tag_id,
          //                         "tag_option_id" => $tag_option_id,
          // )],
          "company_name"          => $company_name,
          "phone"                 => $phone,
          "mobile"                => $mobile,
          "department"            => $department,
          "designation"           => $designation,
          "website"               => $website,
          "billing_address"       => array(
          "attention"    => $attention,
          "street"       => $street,
          "city"         => $city,
          "state"        => $state,
          "zip"          => $zip,
          "country"      => $country,
          "country_code" => $country_code,
          "state_code"   => $state_code,
          "fax"          => $fax,
          ),
          "shipping_address"      => array(
          "attention"    => $attention,
          "street"       => $street,
          "city"         => $city,
          "state"        => $state,
          "zip"          => $zip,
          "country"      => $country,
          "country_code" => $country_code,
          "state_code"   => $state_code,
          "fax"          => $fax,
          ),
          "payment_terms"         => $payment_terms,
          "payment_terms_label"   => $payment_terms_label,
          "currency_code"         => $currency_code,
          "ach_supported"         => $ach_supported,
          "twitter"               => $twitter,
          "facebook"              => $facebook,
          "skype"                 => $skype,
          "notes"                 => $notes,
          "is_portal_enabled"     => $is_portal_enabled,

          "gst_treatment"         => $gst_treatment,


          "default_templates"     => array(
          "invoice_template_id"    => $invoice_template_id,
          "creditnote_template_id" => $creditnote_template_id,
          ),

          );

          $url = "https://subscriptions.zoho.com/api/v1/customers";
          $postCustomer = CallHttpService::postApiwithHeader($url,$data,$organizationId,$AuthorizationCode); */

        // $customer_id = $postCustomer->customer->customer_id;
        $urlApi = config('icapt.zoho_apisetting.subscriptionBillingUrl');
        //$newurlApi = config('icapt.zoho_apisetting.subscriptionUrls');
        //$newsubscriptionUrl = 'https://' . $newurlApi . '/v1/subscriptions/';

        $urlPrice = 'https://' . $urlApi . '/v1/plans/' . $plan_code;
        @$resp = CallHttpService::getZohoApi($urlPrice, null, $organizationId, $AuthorizationCode);
        //@$zoho_planprice = @$resp->plan->recurring_price;
        $res = json_decode($resp);
        // dd($res->plan->recurring_price,$resp);





        $gprice = $res->plan->recurring_price ?? 0;
        $desc = $res->plan->description ?? null;






        // $newurlApi = config('icapt.zoho_apisetting.subscriptionBillingUrl');
        // Need Subscription
        //$urlSub = "https://'.$urlApi.'/v1/subscriptions";
        $add_to_unbilled_charges = 'false';
        $display_name = $first_name;  /* required */
        $salutation = '';
        $first_name = $first_name;
        $last_name = $last_name;
        $email = $adminEmail;  /* required */

        $attention = $company_name;
        $street = 'ABC Street';
        $city = 'Seoul';
        $state = 'Jeju';
        $country = 'Malaysia';
        $zip = '12345';
        $fax = '12345';
        $attention = 'InJae Prod';
        $street = 'DEF Street';
        $city = 'Seoul';
        $state = 'Gangnam';
        $country = 'Malaysia';
        $zip = '67890';
        $fax = '67890';
        $payment_terms = '3';
        $payment_terms_label = 'Due on receipt';
        $label = 'label';
        $value = '112233';
        $place_of_contact = 'MS';
        $gst_no = '090DA9D0A9';
        $gst_treatment = 'business_gst';
        $vat_treatment = 'overseas';
        $vat_reg_no = '93819023';
        $country_code = 'MY';
        $is_taxable = 'true';
        $tax_id = '00ABC07890';
        $tax_authority_id = 'ABC123';
        $tax_authority_name = 'ABC';
        $tax_exemption_id = '0938428742';
        $tax_exemption_code = 'FREE GST';
        $payment_terms = '3';
        $payment_terms_label = 'Due on receipt';
        $label = 'label';
        $value = '112233';
        $contactpersons_id = 'AA010';
        $card_id = 'AA010';  /* required if auto_collect = true */
        $starts_at = date("Y-m-d");
        $exchange_rate = '2';
        $place_of_supply = 'XZ';
        $plan_code = $plan_code;   /* required */
        $plan_description = $desc;
        $price = $gprice;
        $setup_fee = 0;
        $setup_fee_tax_id = '***********';
        $tag_id = '12000030109310300';
        $tag_option_id = '12000030102737834';
        $label = 'label';  /* required */
        $value = '112233';     /* required */
        $quantity = $qty_users;
        $tax_id = '00ABC07890';   /* required */
        $tax_exemption_id = '0938428742';
        $tax_exemption_code = 'FREE GST';
        $setup_fee_tax_exemption_id = '0292019019';
        $setup_fee_tax_exemption_code = 'FREE GST';
        $exclude_trial = 'false';
        $exclude_setup_date = 'false';
        $billing_cycles = '-1';
        $trial_days = '14';
        $addon_code = 'add-monthly';   /* required */
        $addon_description = 'Monthly add on';

        $tag_id = '12000030109310300';
        $tag_option_id = '12000030102737834';
        $label = 'label';  /* required */
        $value = '112233';     /* required */
        $tax_id = '00ABC07890';   /* required */
        $tax_exemption_id = '0938428742';
        $tax_exemption_code = 'FREE GST';
        $coupon_code = 'Win25';
        $auto_collect = 'true';
        $reference_id = 'samsantech';
        $salesperson_name = 'Vaneessa';
        //$payment_gateway                = 'test_gateway';

        $payment_gateway =  config('icapt.zoho_apisetting.paymentgateway');

        $salesperson_name = 'Vaneessa';
        $create_backdated_invoice = 'true';
        $template_id = '08123918203981';

        $customerdata = array(
            "display_name" => $display_name,
            "salutation" => $salutation,
            "first_name" => $first_name,
            "last_name" => $last_name,
            "email" => $email,
            "company_name" => $company_name,
            //            'is_portal_enabled' => true,
        );
        $newurlApi = config('icapt.zoho_apisetting.subscriptionBillingUrl');
        $url = "https://" . $newurlApi . "/v1/customers";

        $urlSub = "https://" . $newurlApi . "/v1/subscriptions";
        $postCustomer = CallHttpService::postApiwithHeader($url, $customerdata, $organizationId, $AuthorizationCode);
        //dd($newurlApi);
        $customer_id = $postCustomer->customer->customer_id;
        //dd($postCustomer);
        $contact_person_id = $postCustomer->customer->primary_contactperson_id;

        $dataSubscriptioninfor = array(
            "add_to_unbilled_charges" => $add_to_unbilled_charges,
            //            "customer" => array(
            //                "display_name" => $display_name,
            //                "salutation" => $salutation,
            //                "first_name" => $first_name,
            //                "last_name" => $last_name,
            //                "email" => $email,
            //                "company_name" => $company_name,
            //                'is_portal_enabled' => true,
            //            ),
            "customer_id" => $customer_id,
            "starts_at" => $starts_at,
            "plan" => array(
                "plan_code" => $plan_code,
                "plan_description" => $plan_description,
                "price" => $price,
                "setup_fee" => $setup_fee,
                "quantity" => $quantity,
                "trial_days" => $trial_days,
            ),
            "auto_collect" => $auto_collect,
            "salesperson_name" => $salesperson_name,
            //  "custom_fields"             =>  array(
            //                             "label" =>   $label,
            //                             "value" =>   $value,
            // ),
            "payment_gateways" => [array(
                "payment_gateway" => $payment_gateway,
            )],
            "salesperson_name" => $salesperson_name,
        );
        $strTestopen = config('icapt.zoho_apisetting.open_subscription');

        if ($strTestopen == 1) {
            $postSubscription = CallHttpService::postApiwithHeader($urlSub, $dataSubscriptioninfor, $organizationId, $AuthorizationCode);

            //@$resDejson = json_decode($postSubscription);


            $subscription_id = $postSubscription->subscription->subscription_id;
            $product_name = $postSubscription->subscription->product_name . '-' . $postSubscription->subscription->product_id;
            $customer_id = $postSubscription->subscription->customer_id;
            $newurlApi = config('icapt.zoho_apisetting.subscriptionBillingUrl');
            $portalEnableurl = "https://" . $newurlApi . "/v1/customers/" . $customer_id . "/portal/enable";
            $customerdata = array(
                "contact_persons" => [
                    0 => [
                        "contact_person_id" => $contact_person_id
                    ]
                ]
            );
            //
            ////        dd(json_encode($customerdata));
            $url = "https://" . $urlApi . "/v1/customers";
            $postCustomer = CallHttpService::postApiwithHeader($portalEnableurl, $customerdata, $organizationId, $AuthorizationCode);
        }



        //
        //        dd($postCustomer);
        //        $customerdata = array(
        //            "display_name" => $display_name,
        //            'is_portal_enabled' => true,
        //        );
        //        $url = "https://subscriptions.zoho.com/api/v1/customers/$customer_id";
        //        $postCustomer = CallHttpService::putZohoApi($url, $customerdata, $organizationId, $AuthorizationCode);
        //        $res_result = json_decode($postCustomer);
        //        dd($res_result);
        if ($plan_code == "AX-MT-FREE-M") {
            $zoho_trial_expiry_date = "";
        } else {
            if ($strTestopen == 1) {
                $zoho_trial_expiry_date = $postSubscription->subscription->trial_ends_at;
            } else {
                $zoho_trial_expiry_date = "";
            }
        }


        $mcMember = array(
            "email_address" => $adminEmail,
            'status' => "subscribed",
            "merge_fields" => array(
                "FNAME" => $first_name,
                "LNAME" => $last_name,
                "PHONE" => $info->contact,
                "MMERGE6" => "Yes",
                'MMERGE7' => $plan_code,
                'MMERGE15' => $quantity,
                'MMERGE13' => $site_id,
                'MMERGE8' => $company_name,
                'MMERGE9' => isset($busnisseTypes[$businesstype]) ? $busnisseTypes[$businesstype] : "",
                'MMERGE11' => isset($companySizes[$companysize]) ? $companySizes[$companysize] : "",
                'MMERGE12' => isset($intrestTypes[$primaryinterest]) ? $intrestTypes[$primaryinterest] : "",
                'MMERGE17' => "0 Days Ago",
                'MMERGE10' => isset($industryTypes[$industry]) ? $industryTypes[$industry] : "",
                'MMERGE14' => $timezone,
                'MMERGE16' => isset($postSubscription) ? $postSubscription->subscription->status : "",
            ),
        );

        // Email
        $resultMC = \App\Services\MailchimpApiService::addMemberToList($mcMember);


        //        dd($resultMC);
        //        $response = $mailchimp->lists->getAllLists();
        //        dd($resultMC);
        //$plan_code = $request->plan_code;
        /* $url = config('app.url');
          $urlSubscription = "https://subscriptions.zoho.com/api/v1/hostedpages/newsubscription";
          $dataSubscription = array(
          "customer_id"   => $customer_id,
          "plan"          => array(
          "plan_code"    => $plan_code,
          "price"       => $request->price,
          "quantity" => $qty,
          ),
          "redirect_url" => $url.'/webhook/activeNewSite/'.$siteID,
          );

          $postSubscription = CallHttpService::postApiwithHeader($urlSubscription,$dataSubscription,$organizationId,$AuthorizationCode);

          echo'<pre>';
          print_r($postSubscription);
          exit;
          $hostedpage_id = $postSubscription->hostedpage->hostedpage_id;
          $decrypted_hosted_page_id = $postSubscription->hostedpage->decrypted_hosted_page_id;
          $url_payment = $postSubscription->hostedpage->url;

          DB::table('temp_subscription')->insert(
          [
          'hostedpage_id' => $hostedpage_id,
          'decrypted_hosted_page_id' => $decrypted_hosted_page_id,
          'url_payment' => $url_payment,
          'siteID' => $siteID,
          'adminId' => $adminId,
          'plan_id' => $planID,
          'price' => $request->price,
          'create_at' => $now,
          'create_by' => '',
          'update_at' => $now,
          'update_by' => '',
          ]
          );

          $apiurl = 'https://subscriptions.zoho.com/api/v1/hostedpages/'.$hostedpage_id;

          $responsed = CallHttpService::getZohoApi($apiurl,null,$organizationId,$AuthorizationCode);
          $res = json_decode($responsed);
          $subscription_id = $res->data->subscription->subscription_id;
          $product_name = $res->data->subscription->product_name;
          $customer_id = $res->data->subscription->customer_id;
          $quantity = $res->data->subscription->plan->quantity;

          DB::table('site_settings')->where('id', $siteID)->update([
          'status' => 1,
          'zoho_customer_id' => $customer_id,
          'zoho_subscription_id' => $subscription_id,
          'zoho_product_name' => $product_name,
          'max_user' => $quantity,
          ]);
         */
        // Record Details for email purpose
        // $fh = fopen('new/'.$customer_id.'_info.txt', 'a+') or die;
        // flock($fh, LOCK_EX) or die('Error: Fail to lock file');
        // fwrite($fh, join("\t", array(
        // $site_id,
        // $site_name,
        // $adminUsername,
        // $timezone,
        // )) . "\n");
        // flock($fh, LOCK_UN) or die('Error: Fail to unlock file');
        // fclose($fh);
        if ($strTestopen == 1) {
            DB::table('site_settings')->where('id', $siteID)->update([
                'status' => 1,
                'zoho_customer_id' => $customer_id,
                'zoho_subscription_id' => $subscription_id,
                'zoho_product_name' => $product_name,
                'max_user' => customCrypt($quantity),
                'zoho_trial_expiry_date' => $zoho_trial_expiry_date,
            ]);

            $path = public_path('zoho_application/new/' . $customer_id . '_info.txt');
            $fh = fopen($path, 'w+') or die;
            flock($fh, LOCK_EX) or die('Error: Fail to lock file');
            fwrite($fh, join("\t", array(
                $site_id,
                $site_name,
                $adminUsername,
                $timezone,
            )) . "\n");
            flock($fh, LOCK_UN) or die('Error: Fail to unlock file');
            fclose($fh);
        } else {

            DB::table('site_settings')->where('id', $siteID)->update([
                'status' => 1,
                //'zoho_customer_id' => $customer_id,
                //'zoho_subscription_id' => $subscription_id,
                //'zoho_product_name' => $product_name,
                'max_user' => customCrypt($quantity),
                'zoho_trial_expiry_date' => $zoho_trial_expiry_date,
            ]);
        }

        $subs_id = DB::table('site_settings')->select('zoho_subscription_id')->where('site_id', $site_id)->first();

        if ($info->plan_code == "AX-MT-FREE-M") {
            $plan_name = "Free Plan";
        } else if ($info->plan_code == "AX-MT-STR-M") {
            $plan_name = "Starter Plan Monthly";
        } else if ($info->plan_code == "AX-MT-PRO-M") {
            $plan_name = "Professional Plan Monthly";
        } else if ($info->plan_code == "AX-MT-ENT-M") {
            $plan_name = "Enterprise Plan Monthly";
        } else if ($info->plan_code == "AX-MT-STR-A") {
            $plan_name = "Starter Plan Annually";
        } else if ($info->plan_code == "AX-MT-PRO-A") {
            $plan_name = "Professional Plan Annually";
        } else if ($info->plan_code == "AX-MT-ENT-A") {
            $plan_name = "Enterprise Plan Annually";
        }


        $getSession = Session::get('token');
        if ($getSession == "") {
            return redirect()->back();
        }

        Session::put('token', '');
        $url = config('app.url') . '/webhook/verifyemailacc/token_code=' . $getSession;
        $userEmail = "<EMAIL>";
        $userEmail1 = "<EMAIL>";
        $zohomail = new \stdClass();
        $zohomail->subs = $subs_id->zoho_subscription_id;
        $zohomail->plan = $plan_name;
        $zohomail->plan_code = $info->plan_code;
        $zohomail->company = $info->company_name;
        $zohomail->email = $info->email;
        $zohomail->site_id = $site_id;

        Mail::to($userEmail)->send(new InternalNewSubscription($zohomail));
        Mail::to($userEmail1)->send(new InternalNewSubscription($zohomail));

        // Delete temp register user
        DB::table('new_tmp_registers')->where('uniq_code', $token)->delete();
        // Auto Login


        /*  $user = DB::table('users')->where('name', $adminUsername)->where('site_id', $site_id)->first();
        Auth::loginUsingId($user->id, TRUE);*/

        $now = Carbon::now()->toDateString();
        $now_date = new \DateTime($now);

        if ($zoho_trial_expiry_date && $zoho_trial_expiry_date != "") {
            $trial_expiry_date = new \DateTime($zoho_trial_expiry_date);
            $remaining_trial_days = $now_date->diff($trial_expiry_date)->invert ? ($now_date->diff($trial_expiry_date)->days * -1) : ($now_date->diff($trial_expiry_date)->days);

            Session::put('remaining_trial_days', $remaining_trial_days);
        } else {
            Session::put('remaining_trial_days', null);
            $remaining_trial_days = null;
        }

        /*Session::put('zoho_trial_status', 'C');

        return redirect()->intended('/');
        */

        Session::put('tokenautologin', $token);
        $response = new \stdClass;
        $response->status = "success";
        $response->token =  $token;
        $response->site_id = $site_id;
        $response->adminName = $adminUsername;
        $response->remaining_trial_days =  $remaining_trial_days;
        return (json_encode($response));
    }

    public function subscription(Request $request)
    {


        $plan_name = DB::table('plans')->select('plan_name')->where('plan_code', $request->plan_code)->value('plan_name');

        if ($request->plan_code == "" || $plan_name == "") {
            // Alert::error('Warning', 'Invalid plan code!!');
            Alert::error('Error', 'Invalid plan code.');
            return redirect()->back();
        }

        $client_id = config('icapt.zoho_apisetting.client_id');
        $client_secret = config('icapt.zoho_apisetting.client_secret');
        $scope = config('icapt.zoho_apisetting.scope');
        $state = config('icapt.zoho_apisetting.state');
        $response_type = config('icapt.zoho_apisetting.response_type');
        $authorization_code = config('icapt.zoho_apisetting.authorization_code');
        $refresh_token = config('icapt.zoho_apisetting.refresh_token');
        $redirect_uri = "http://localhost/zohoapi/sub.php";
        $access_type = config('icapt.zoho_apisetting.access_type');
        $organizationId = config('icapt.zoho_apisetting.organizationId');
        $refeshtoken = config('icapt.zoho_apisetting.refeshtoken');
        $apiUrl = config('icapt.zoho_apisetting.apiUrl');
        $urlApi = config('icapt.zoho_apisetting.subscriptionBillingUrl');
        $subscriptionUrl = 'https://' . $urlApi . '/v1/subscriptions/';

        // Refresh Token and Get the API
        $postParameter = array(
            'refresh_token' => $refeshtoken,
            'client_id' => $client_id,
            'client_secret' => $client_secret,
            'redirect_uri' => $redirect_uri,
            'grant_type' => 'refresh_token',
        );

        $result = CallHttpService::postZohoApi($apiUrl, $postParameter);

        $AuthorizationCode = "Zoho-oauthtoken " . $result->access_token;

        $urlPrice = 'https://' . $urlApi . '/v1/plans/' . $request->plan_code;
        @$resp = CallHttpService::getZohoApi($urlPrice, null, $organizationId, $AuthorizationCode);
        //@$zoho_planprice = @$resp->plan->recurring_price;
        $res = json_decode($resp);
        $price = $res->plan->recurring_price;

        $sub_period = substr($request->plan_code, -1);
        if ($sub_period == "A") {
            $msg_subscription = 'Every 1 year(s).';
        } else {
            $msg_subscription = 'Every 1 month(s).';
        }
        $currency = "USD";
        return view('admin.subscription')->with('plan_code', $request->plan_code)->with('currency', $currency)->with('plan_name', $plan_name)->with('price', $price)->with('msg_subscription', $msg_subscription);
    }

    public function updateSubscription(Request $request)
    {
        // dd("cdcd");
        // Live
        $customer_id = $request[0]['zoho_customer_id'];
        $subscription_id = $request[0]['zoho_subscription_id'];
        $event_type = $request[0]['zoho_event_type'];

        // Testing
        // $customer_id = 2794855000000262006;//2794855000000088002;//2794855000000262006;
        // $subscription_id =112233;// 2794855000000086010;//112233;
        // $event_type = 'subscription_deleted';

        // DB::enableQuerylog();
        // If customer id and subscription id are the same, update these values
        $zoho = DB::table('site_settings')->where(['zoho_customer_id' => $customer_id, 'zoho_subscription_id' => $subscription_id])->first();
        // $query = DB::getQueryLog();
        // $lastQ = end($query);
        // Update status to 0 for inactive

        $curentTime = Carbon::now();
        if (@$zoho->zoho_suspended_date != "") {
            $strDaySuspended  = $curentTime->diffInDays($zoho->zoho_suspended_date);

            if ($strDaySuspended >= 90) {

                $arrTableStore  = array();
                $arrTableStoreAll = array();
                $arrTotalData = array();
                $arrGetAllTables = getSiteIDTable();
                foreach ($arrGetAllTables as $key => $tbl) {
                    @$arrTableStore[$tbl] = DB::table($tbl)->where('site_id', $zoho->site_id)->count();
                    @$arrTableStoreAll[$tbl] = DB::table($tbl)->count();
                    $arrTotalData[$tbl]['table'] = $tbl;
                    $arrTotalData[$tbl]['original_total']  = @$arrTableStoreAll[$tbl];
                    $arrTotalData[$tbl]['deleted_total']   = @$arrTableStore[$tbl];
                    $arrTotalData[$tbl]['data_deleted']    = json_encode(DB::table($tbl)->where('site_id', $zoho->site_id)->get()->toArray());
                }

                // Log File for deleted Tables of Records and site ID

                $path = public_path('zoho_application/cancel/' . $zoho->site_id . '_' . $customer_id . '_info.txt');
                $fh = fopen($path, 'w+') or die;
                flock($fh, LOCK_EX) or die('Error: Fail to lock file');
                foreach ($arrTotalData as $key => $value) {
                    fwrite($fh, join("\t\t", array(
                        $value['table'],
                        $value['original_total'],
                        $value['deleted_total'],
                        $value['data_deleted'],
                    )) . "\n\n\n\t");
                }
                flock($fh, LOCK_UN) or die('Error: Fail to unlock file');
                fclose($fh);
                //dd($arrTotalData);
                // Delete Record
                foreach ($arrTotalData as $tblkey => $value) {
                    $limitrecord = $value['deleted_total'];
                    if ($limitrecord > 0) {
                        //  DB::table($tblkey)->where('site_id',$zoho->site_id)->take($limitrecord)->delete();
                    }
                }
            }
        } else {
            //dd("Suspend Only");
            if ($zoho->zoho_customer_id == $customer_id && $zoho->zoho_subscription_id == $subscription_id) {
                $result = DB::table('site_settings')->where(['zoho_customer_id' => $customer_id, 'zoho_subscription_id' => $subscription_id])->update([
                    'zoho_event_type' => $event_type,
                    'status' => 0,
                    'modified_by' => 'Zoho Admin',
                    'zoho_suspended_date' => $curentTime
                ]);
            }
        }
    }

    public function updateCreditCard(Request $request)
    {

        $customer_id = $request[0]['zoho_customer_id'];
        //$subscription_id = $request[0]['zoho_subscription_id'];
        $event_type = $request[0]['zoho_event_type'];

        // DB::enableQuerylog();
        // If customer id and subscription id are the same, update these values
        $zoho = DB::table('site_settings')->where(['zoho_customer_id' => $customer_id])->first();
        // $query = DB::getQueryLog();
        // $lastQ = end($query);
        // No more in trial status, so update trial expiry date to null
        // Update status to A for Active
        if ($zoho->zoho_customer_id == $customer_id) {
            $result = DB::table('site_settings')->where(['zoho_customer_id' => $customer_id])->update([
                'zoho_trial_status' => 'A',
                'zoho_event_type' => $event_type
            ]);
        }
    }

    public function retrieveHostPage(Request $request)
    {

        // $plan_name = DB::table('plans')->select('plan_name')->where('plan_code',$request->plan_code)->value('plan_name');
        // if($request->plan_code=="" || $plan_name=="" )
        // {
        //     // Alert::error('Warning', 'Invalid plan code!!');
        //     Alert::error('Error','Invalid plan code.');
        //     return redirect()->back();
        // }

        $client_id = config('icapt.zoho_apisetting.client_id');
        $client_secret = config('icapt.zoho_apisetting.client_secret');
        $scope = "ZohoSubscriptions.hostedpages.READ";
        $state = config('icapt.zoho_apisetting.state');
        $response_type = config('icapt.zoho_apisetting.response_type');
        $authorization_code = config('icapt.zoho_apisetting.authorization_code');
        $refresh_token = config('icapt.zoho_apisetting.refresh_token');
        $redirect_uri = "http://localhost/zohoapi/sub.php";
        $access_type = config('icapt.zoho_apisetting.access_type');
        $organizationId = config('icapt.zoho_apisetting.organizationId');
        $refeshtoken = config('icapt.zoho_apisetting.refeshtoken');
        $apiUrl = config('icapt.zoho_apisetting.apiUrl');
        $urlApi = config('icapt.zoho_apisetting.subscriptionBillingUrl');
        $subscriptionUrl = 'https://' . $urlApi . '/v1/subscriptions/';

        // Refresh Token and Get the API
        $postParameter = array(
            'refresh_token' => $refeshtoken,
            'client_id' => $client_id,
            'client_secret' => $client_secret,
            'redirect_uri' => $redirect_uri,
            'grant_type' => 'refresh_token',
        );

        $result = CallHttpService::postZohoApi($apiUrl, $postParameter);

        $AuthorizationCode = "Zoho-oauthtoken " . $result->access_token;

        $hostedpageId = "2-f26d60d284f8ad30a3a6e8ef33941e6158e7a340c96c03aeddb0e7bfcc4960913bc0c54c6abaa78110b62cefa4f00e5e";
        $urlHosted = "https://" . $urlApi . "/v1/hostedpages/" . $hostedpageId;

        $response = CallHttpService::getZohoApi($urlHosted, null, $organizationId, $AuthorizationCode);

        echo "<pre>";
        print_R($response);
    }

    public function activeNewSite(Request $request)
    {

        $client_id = config('icapt.zoho_apisetting.client_id');
        $client_secret = config('icapt.zoho_apisetting.client_secret');
        $scope = config('icapt.zoho_apisetting.scope');
        $state = config('icapt.zoho_apisetting.state');
        $response_type = config('icapt.zoho_apisetting.response_type');
        $authorization_code = config('icapt.zoho_apisetting.authorization_code');
        $refresh_token = config('icapt.zoho_apisetting.refresh_token');
        $redirect_uri = "http://localhost/zohoapi/sub.php";
        $access_type = config('icapt.zoho_apisetting.access_type');
        $organizationId = config('icapt.zoho_apisetting.organizationId');
        $refeshtoken = config('icapt.zoho_apisetting.refeshtoken');
        $apiUrl = config('icapt.zoho_apisetting.apiUrl');
        $urlApi = config('icapt.zoho_apisetting.subscriptionBillingUrl');
        $subscriptionUrl = 'https://' . $urlApi . '/v1/subscriptions/';

        // Refresh Token and Get the API
        $postParameter = array(
            'refresh_token' => $refeshtoken,
            'client_id' => $client_id,
            'client_secret' => $client_secret,
            'redirect_uri' => $redirect_uri,
            'grant_type' => 'refresh_token',
        );

        $result = CallHttpService::postZohoApi($apiUrl, $postParameter);

        $AuthorizationCode = "Zoho-oauthtoken " . $result->access_token;

        $siteID = $request->segment(3);

        $temp_subpt = DB::table('temp_subscription')->select('*')->where('siteID', $siteID)->first();

        $apiurl = 'https://' . $urlApi . '/v1/hostedpages/' . $temp_subpt->hostedpage_id;

        $response = CallHttpService::getZohoApi($apiurl, null, $organizationId, $AuthorizationCode);
        $res = json_decode($response);
        $subscription_id = $res->data->subscription->subscription_id;
        $product_name = $res->data->subscription->product_name;
        $customer_id = $res->data->subscription->customer_id;
        $quantity = $res->data->subscription->plan->quantity;



        $sitesetting = DB::table('site_settings')->select('*')->where('id', $siteID)->first();
        $userdetails = DB::table('users')->select('*')->where('site_id', $sitesetting->site_id)->where('type', 'site_owner')->first();

        $timezone = $sitesetting->timezone;
        $now = Carbon::now()->toDateTimeString();
        //$timezone = auth()->user()->timezone;
        $date = Timezone::convertFromUTC($now, $timezone, 'Y-m-d');
        $time = Timezone::convertFromUTC($now, $timezone, 'H:i:s');
        $modified_date = $date . ' ' . $time;
        //dd($modified_date);
        DB::table('site_settings')->where('id', $siteID)->update([
            'status' => 1,
            'zoho_customer_id' => $customer_id,
            'zoho_subscription_id' => $subscription_id,
            'zoho_product_name' => $product_name,
            'max_user' => customCrypt($quantity),
            'modified_date' => $modified_date
        ]);

        DB::table('users')->where('id', $temp_subpt->adminId)->update([
            'status' => 'A',
            'modified_date' => $modified_date
        ]);

        /* $url='https://test.icapt.site/login';
          $site_name = $userdetails->site_id;
          $site_id = $userdetails->site_id;
          $adminUsername = $userdetails->name;
          $timezone = $userdetails->timezone;
          $adminEmail = $userdetails->email;

          $zohomail = new \stdClass();
          $zohomail->url = $url;
          $zohomail->site_name = $site_name;
          $zohomail->site_id = $site_id;
          // $zohomail->password = $request->password;
          $zohomail->name = $adminUsername;
          $zohomail->timezone = $timezone;
          // $zohomail->first_name = $first_name;
          // $zohomail->last_name = $last_name;
          $zohomail->sender = "<EMAIL>";
          $zohomail->receiver = $adminEmail;
          // Mail::to($adminEmail)->send(new RegisterZohoEmail($zohomail));
          return view('mail.registerZoho')->with('zohomail',$zohomail);
          exit; */

        return redirect()->route('login')->with('successmsg', 'Thank you. A new site has been successfully created. Please refer to the email for the site and login information. ');
    }

    public function store(Request $request)
    {

        $client_id = config('icapt.zoho_apisetting.client_id');
        $client_secret = config('icapt.zoho_apisetting.client_secret');
        $scope = config('icapt.zoho_apisetting.scope');
        $state = config('icapt.zoho_apisetting.state');
        $response_type = config('icapt.zoho_apisetting.response_type');
        $authorization_code = config('icapt.zoho_apisetting.authorization_code');
        $refresh_token = config('icapt.zoho_apisetting.refresh_token');
        $redirect_uri = "http://localhost/zohoapi/sub.php";
        $access_type = config('icapt.zoho_apisetting.access_type');
        $organizationId = config('icapt.zoho_apisetting.organizationId');
        $refeshtoken = config('icapt.zoho_apisetting.refeshtoken');
        $apiUrl = config('icapt.zoho_apisetting.apiUrl');
        $urlApi = config('icapt.zoho_apisetting.subscriptionBillingUrl');
        $subscriptionUrl = 'https://' . $urlApi . '/v1/subscriptions/';

        // Refresh Token and Get the API
        $postParameter = array(
            'refresh_token' => $refeshtoken,
            'client_id' => $client_id,
            'client_secret' => $client_secret,
            'redirect_uri' => $redirect_uri,
            'grant_type' => 'refresh_token',
        );

        $result = CallHttpService::postZohoApi($apiUrl, $postParameter);

        $AuthorizationCode = "Zoho-oauthtoken " . $result->access_token;

        $request->validate([
            'site_id' =>
            [
                'required',
                'unique:site_settings',
                'alpha_dash',
                'max:30',
            ],
            'name' =>
            [
                'required',
                'max:30',
                'unique:users',
            ],
            'email' =>
            [
                'required',
                'max:30',
                'unique:users',
            ],
            'password' => ['confirmed', new StrongPassword, PasswordRules::register(request()->name)],
        ]);

        $now = Carbon::now()->toDateTimeString();

        $plan_code = $request->plan_code;
        $plan_id = DB::table('plans')->select('id')->where('plan_code', $plan_code)->first();
        $db_plan = DB::table('plans')->select('id', 'plan_code')->get();

        $arrFilterPlans = array('AX-MT-STR-M' => '1', 'AX-MT-STR-A' => '4');
        $arrFilterPlanCode = array();
        foreach ($db_plan as $key) {
            if (array_key_exists($key->plan_code, $arrFilterPlans)) {
                $arrFilterPlanCode[$key->id] = $key->plan_code;
            }
            if ($plan_code == $key->plan_code) {
                $planID = $key->id;
            }
        }


        $site_id = $request->site_id;
        $company_name = $request->company_name;
        $site_name = $request->site_name;
        $timezone = $request->timezone;
        $adminUsername = $request->name;
        $adminEmail = $request->email;
        $first_name = $request->first_name;
        $last_name = $request->last_name;
        $password = $request->password;
        $password_confirmation = $request->password_confirmation;
        $country = $request->country;
        $state = $request->state;
        $city = $request->city;
        $street = $request->street;
        $zip = $request->zip;
        $qty = $request->qty;

        // Check site id duplication
        $check_site_id = DB::table('site_settings')->select('site_id')->where('site_id', $site_id)->first();

        if ($check_site_id) {
            // insert into log table -> duplicate site id
        }

        // Free package
        if ($plan_code == "AX-MT-FREE-M") {
            $qty_users = 2;
        } else {
            $qty_users = 1;
        }



        // Insert into site setting
        $createSite = DB::table('site_settings')->insertOrIgnore(
            [
                'site_name' => $site_name,
                'site_id' => $site_id,
                'timezone' => $timezone,
                // 'zoho_event_id' => $zoho_event_id,
                // 'zoho_event_type' => $zoho_event_type,
                // 'zoho_subscription_id' => $zoho_subscription_id,
                // 'zoho_product_name' => $zoho_product_name,
                // 'zoho_customer_id' => $zoho_customer_id,
                'plan_code' => $plan_code,
                'plan_id' => customCrypt($planID),
                'status' => 0,
                'max_user' => customCrypt($qty_users),
                'registered_user' => 1,
                'created_date' => $now,
            ]
        );

        $siteID = DB::table('site_settings')->select('id')->where('site_id', $site_id)->value('id');

        if (!$createSite) {
            DB::rollback();
        }

        // Create Admin User
        $createAdminUser = User::create(
            [
                'name' => $adminUsername,
                // 'description' => 'Admin User',
                'email' => $adminEmail,
                'password' => $request->password,
                'type' => 'site_owner',
                'site_administration' => 'Yes',
                'homepage' => 'web',
                'timezone' => $timezone,
                'site_id' => $site_id,
                // 'created_by' => 'system',
                'status' => 'P',
                'created_date' => $now,
                'modified_date' => $now
            ]
        );

        if (!$createAdminUser) {
            DB::rollback();
        }

        $adminId = $createAdminUser->id;

        // Give Admin Ext stuff
        DB::table('user_ext')->insert([
            ['user_id' => $adminId, 'whse_num' => '']
        ]);

        $moduleSKU = DB::table('moduleskus')
            ->selectRaw('id, name')
            ->get();

        $arrGroup = array();

        // Check if plan code is Starter
        if (array_key_exists($planID, $arrFilterPlanCode)) {
            $group_moduleSKU = DB::table('group_modulesku')
                ->selectRaw('group_id')
                ->where('module_id', 1)
                ->get();
        } else {
            $group_moduleSKU = DB::table('group_modulesku')
                ->selectRaw('group_id')
                ->get();
        }

        foreach ($group_moduleSKU as $key => $value) {
            array_push($arrGroup, $value->group_id);
        }

        $group = DB::table('groups')
            ->selectRaw('id')
            ->whereIn('id', $arrGroup)
            ->get();

        foreach ($group as $k => $value) {
            $grpId = $value->id;

            DB::table('user_groups')->insert(
                [
                    'user_id' => $adminId,
                    'group_id' => $grpId,
                    'created_date' => $now,
                    'modified_date' => $now
                ]
            );
        }

        if (array_key_exists($planID, $arrFilterPlanCode)) {
            //Create Labels
            $label_names = [
                'Inventory Label' => [
                    '[
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>',
                ],
                'CO Shipping Label' => [
                    '[
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "CO"},
                            {"size": 10, "style": "", "content": "%co_num%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Cust"},
                            {"size": 10, "style": "", "content": "%cust_name%"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-2" style="">CO</div><div class="col-xs-10" style="">%co_num%</div></div><div class="row"><div class="col-xs-2" style="">Customer</div><div class="col-xs-10" style="">%cust_name%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                ],
                //                'Misc. Issue Label' => ['[
                //                        [
                //                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                //                        ],
                //                        [
                //                            {"size": 2, "style": "", "content": "Item_Item_Item"},
                //                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                //                        ],
                //                        [
                //                            {"size": 2, "style": "", "content": "Desc"},
                //                            {"size": 10, "style": "", "content": "%item_desc%"}
                //                        ],
                //                        [
                //                            {"size": 2, "style": "", "content": "Loc"},
                //                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                //                            {"size": 1, "style": "", "content": "Lot"},
                //                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                //                        ],
                //                        [
                //                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                //                            {"size": 4, "style": "", "content": "%expiry_date%"}
                //                        ],
                //                        [
                //                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                //                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                //                            {"size": 2, "style": "", "content": "Box"},
                //                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                //                        ],
                //                        [
                //                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                //                        ]
                //                    ]',
                //                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item_Item_Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                //                ],
                'PO Receipt Label' => [
                    '[
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "PO"},
                            {"size": 10, "style": "", "content": "%po_num%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Vendor"},
                            {"size": 10, "style": "", "content": "%vend_name%"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-2" style="">PO</div><div class="col-xs-10" style="">%po_num%</div></div><div class="row"><div class="col-xs-2" style="">Vendor</div><div class="col-xs-10" style="">%vend_name%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                ],

                'Customer Return Label' => [
                    '[
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Return Num"},
                            {"size": 10, "style": "", "content": "%return_num%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Cust"},
                            {"size": 10, "style": "", "content": "%cust_name%"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-2" style="">Return Num</div><div class="col-xs-10" style="">%return_num%</div></div><div class="row"><div class="col-xs-2" style="">Customer</div><div class="col-xs-10" style="">%cust_name%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                ],


            ];

            foreach ($label_names as $label => $values) {
                $default_label = DefaultLabel::where('type', $label)->first();
                // dd($default_label);

                DB::table('labels')->insert(
                    [
                        'content_html' => $default_label ? $default_label->content_html : "",
                        'width' => $default_label ? $default_label->width : 100,
                        'height' => $default_label ? $default_label->height : 100,
                        'margin_left' => $default_label ? $default_label->margin_left : 0,
                        'margin_right' => $default_label ? $default_label->margin_right : 0,
                        'margin_top' => $default_label ? $default_label->margin_top : 0,
                        'margin_bottom' => $default_label ? $default_label->margin_bottom : 0,
                        'label_name' => $label,
                        'type' => $label,
                        'raw_content' => $values[0],
                        'content' => $values[1],
                        // 'content_html' => $values[1],
                        'is_default' => 1,
                        'site_id' => $site_id,
                        'created_date' => $now,
                        'modified_date' => $now
                    ]
                );
            }

            $label_modules = [
                'MiscReceipt',
                'MiscIssue',
                'Putaway',
                'StockMove',
                'CustOrdShipping',
                'PoReceipt',
                'TranOrderShipping',
                'TransferOrderReceipt',
                'PickNShip',
                'Picklist',
                'CustomerReturn'
            ];
        } else {

            $label_names = [
                'Inventory Label' => [
                    '[
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>',
                ],
                'CO Shipping Label' => [
                    '[
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "CO"},
                            {"size": 10, "style": "", "content": "%co_num%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Cust"},
                            {"size": 10, "style": "", "content": "%cust_name%"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-2" style="">CO</div><div class="col-xs-10" style="">%co_num%</div></div><div class="row"><div class="col-xs-2" style="">Customer</div><div class="col-xs-10" style="">%cust_name%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                ],
                //                'Misc. Issue Label' => ['[
                //                        [
                //                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                //                        ],
                //                        [
                //                            {"size": 2, "style": "", "content": "Item_Item_Item"},
                //                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                //                        ],
                //                        [
                //                            {"size": 2, "style": "", "content": "Desc"},
                //                            {"size": 10, "style": "", "content": "%item_desc%"}
                //                        ],
                //                        [
                //                            {"size": 2, "style": "", "content": "Loc"},
                //                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                //                            {"size": 1, "style": "", "content": "Lot"},
                //                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                //                        ],
                //                        [
                //                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                //                            {"size": 4, "style": "", "content": "%expiry_date%"}
                //                        ],
                //                        [
                //                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                //                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                //                            {"size": 2, "style": "", "content": "Box"},
                //                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                //                        ],
                //                        [
                //                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                //                        ]
                //                    ]',
                //                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item_Item_Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                //                ],
                'PO Receipt Label' => [
                    '[
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "PO"},
                            {"size": 10, "style": "", "content": "%po_num%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Vendor"},
                            {"size": 10, "style": "", "content": "%vend_name%"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-2" style="">PO</div><div class="col-xs-10" style="">%po_num%</div></div><div class="row"><div class="col-xs-2" style="">Vendor</div><div class="col-xs-10" style="">%vend_name%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                ],
                'Job Receipt Label' => [
                    '[
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Job"},
                            {"size": 10, "style": "", "content": "%job_qrcode% <small>%job_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-2" style="">Job</div><div class="col-xs-10" style="">%job_qrcode% <small>%job_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                ],
                'Job Material Issue Label' => [
                    '[
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Job"},
                            {"size": 10, "style": "", "content": "%job_qrcode% <small>%job_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-2" style="">Job</div><div class="col-xs-10" style="">%job_qrcode% <small>%job_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                ],

                'Customer Return Label' => [
                    '[
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Return Num"},
                            {"size": 10, "style": "", "content": "%return_num%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Cust"},
                            {"size": 10, "style": "", "content": "%cust_name%"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-2" style="">Return Num</div><div class="col-xs-10" style="">%return_num%</div></div><div class="row"><div class="col-xs-2" style="">Customer</div><div class="col-xs-10" style="">%cust_name%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                ],


            ];
            if ($planID != 7 && $planID != 4 && $planID != 1) {
                $label_names['TO Shipping Label'] = [
                    '[

                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "From Whse"},
                            {"size": 6, "style": "", "content": "%from_whse_qrcode%<br /><small>%from_whse%</small>"},
                            {"size": 1, "style": "", "content": "To Whse"},
                            {"size": 3, "style": "", "content": "%to_whse_qrcode%<br /><small>%to_whse%</small>"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "TO"},
                            {"size": 10, "style": "", "content": "%trn_num%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "TO Line"},
                            {"size": 10, "style": "", "content": "%trn_line%"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-3" style="">Item</div><div class="col-xs-9" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-3" style="">Desc</div><div class="col-xs-9" style="">%item_desc%</div></div><div class="row"><div class="col-xs-3" style="">From Whse</div><div class="col-xs-3" style="">%from_whse_qrcode%<br /><small>%from_whse%</small></div><div class="col-xs-3" style="">To Whse</div><div class="col-xs-3" style="">%to_whse_qrcode%<br /><small>%to_whse%</small></div></div><div class="row"><div class="col-xs-3" style="">Loc</div><div class="col-xs-3" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-3" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br/><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-3" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-3" style="">TO</div><div class="col-xs-9" style="">%trn_num%</div></div><div class="row"><div class="col-xs-3" style="">TO Line</div><div class="col-xs-9" style="">%trn_line%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                ];

                $label_names['Pallet Label'] = [
                    '[
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "Company %company_name%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "LPN : %lpn_num%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "Creation Date : %creation_date%"}
                        ],

                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "Box : %boxnum% / %totalbox%"}

                        ],
                    ]',
                    '<div class="container">
                    <div class="row">
                        <div class="col-xs-12" style="text-align:center; font-weight:bold">Company %company_name%</div>
                        <div class="col-xs-12" style="text-align:center; font-weight:bold">LPN : %lpn_num%</div>
                        <div class="col-xs-12" style="text-align:center; font-weight:bold">%lpn_qrcode%</div>
                        <div class="col-xs-12" style="text-align:center; font-weight:bold">Creation Date : %creation_date%</div>
                        <div class="col-xs-12" style="text-align:center; font-weight:bold">Box : %boxnum% / %totalbox%</div>
                    </div>
                    </div>',
                ];
            }
            foreach ($label_names as $label => $values) {
                $default_label = DefaultLabel::where('type', $label)->first();
                // dd($default_label);

                DB::table('labels')->insert(
                    [
                        'content_html' => $default_label ? $default_label->content_html : "",
                        'width' => $default_label ? $default_label->width : 100,
                        'height' => $default_label ? $default_label->height : 100,
                        'margin_left' => $default_label ? $default_label->margin_left : 0,
                        'margin_right' => $default_label ? $default_label->margin_right : 0,
                        'margin_top' => $default_label ? $default_label->margin_top : 0,
                        'margin_bottom' => $default_label ? $default_label->margin_bottom : 0,
                        'label_name' => $label,
                        'type' => $label,
                        'raw_content' => $values[0],
                        // 'content_html' => $values[1],
                        'is_default' => 1,
                        'content' => $values[1],
                        'site_id' => $site_id,
                        'created_date' => $now,
                        'modified_date' => $now
                    ]
                );
            }

            $label_modules = [
                'MiscReceipt',
                'MiscIssue',
                'Putaway',
                'StockMove',
                'CustOrdShipping',
                'PoReceipt',
                'JobReceipt',
                'JobMaterialIssue',
                'TranOrderShipping',
                'TransferOrderReceipt',
                'PickNShip',
                'Picklist',
                'CustomerReturn'
            ];
        }
        SuperController::addLabelObjects($planID, $arrFilterPlanCode, $site_id, $now);


        // $labels = DB::table('labels')->where('site_id', $site_id)->get();

        // // Delete and insert new label_modules data
        // DB::table('label_modules')->where('site_id', $site_id)->delete();
        // if (array_key_exists($planID, $arrFilterPlanCode)) {

        //     DB::table('label_modules')->insert([
        //         ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'Inventory Label', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'MiscReceipt', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'MiscIssue', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'Putaway', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'StockMove', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'CO Shipping Label')->id, 'modulename' => 'CustOrdShipping', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'PO Receipt Label')->id, 'modulename' => 'PoReceipt', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         // ['label_id' => $labels->firstWhere('label_name', 'TO Shipping Label')->id, 'modulename' => 'TranOrderShipping', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         // ['label_id' => $labels->firstWhere('label_name', 'Pallet Label')->id, 'modulename' => 'Pallet Label', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id]
        //     ]);
        //     //added after issue 1, 48
        //     $label_modules = [
        //         ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'CustOrdPicking', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'PickList', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'JobMaterialReturn', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'TransferOrderReceipt', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         //            ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'PickNShip', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'CO Shipping Label')->id, 'modulename' => 'PickNShip', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //     ];
        //     //            DB::table('label_modules')->insert($label_modules);
        // } else {
        //     DB::table('label_modules')->insert([
        //         ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'Inventory Label', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'MiscReceipt', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'MiscIssue', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'Putaway', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'StockMove', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'CO Shipping Label')->id, 'modulename' => 'CustOrdShipping', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'PO Receipt Label')->id, 'modulename' => 'PoReceipt', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'Job Receipt Label')->id, 'modulename' => 'JobReceipt', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'Job Material Issue Label')->id, 'modulename' => 'JobMaterialIssue', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         // ['label_id' => $labels->firstWhere('label_name', 'TO Shipping Label')->id, 'modulename' => 'TranOrderShipping', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         // ['label_id' => $labels->firstWhere('label_name', 'Pallet Label')->id, 'modulename' => 'Pallet Label', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id]
        //     ]);
        //     if ($planID != 7 && $planID != 4 && $planID != 1) {
        //         DB::table('label_modules')->insert([
        //                             ['label_id' => $labels->firstWhere('label_name', 'TO Shipping Label')->id, 'modulename' => 'TranOrderShipping', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],

        //         ['label_id' => $labels->firstWhere('label_name', 'Pallet Label')->id, 'modulename' => 'Pallet Label', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id]
        //         ]);
        //     }
        //     //added after issue 1, 48
        //     $label_modules = [
        //         ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'CustOrdPicking', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'PickList', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'JobMaterialReturn', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'TransferOrderReceipt', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         //            ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'PickNShip', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //         ['label_id' => $labels->firstWhere('label_name', 'CO Shipping Label')->id, 'modulename' => 'PickNShip', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        //     ];
        //     DB::table('label_modules')->insert($label_modules);
        // }


        // Customer Data
        $display_name = $request->first_name;  /* required */
        $salutation = '';
        $first_name = $request->first_name;
        $last_name = $request->last_name;
        $email = $request->email;  /* required */
        $tag_id = '';
        $tag_option_id = '';
        $company_name = $request->company_name;
        $phone = '';
        $mobile = '';
        $department = '';
        $designation = '';
        $website = '';
        $attention = '';
        $street = $request->street;
        $city = $request->city;
        $state = $request->state;
        $zip = $request->zip;
        $country = $request->country;
        $country_code = '';
        $state_code = '';
        $fax = '';
        $payment_terms = '';
        $payment_terms_label = '';
        $currency_code = 'USD';
        $ach_supported = '';
        $twitter = '';
        $facebook = '';
        $skype = '';
        $notes = '';
        $is_portal_enabled = true;
        $gst_no = '';
        $gst_treatment = '';
        $place_of_contact = '';
        $vat_treatment = '';
        $vat_reg_no = '';
        $country_code = '';
        $is_taxable = '';
        $tax_id = '';
        $tax_authority_name = '';
        $tax_exemption_id = '';
        $tax_exemption_code = '';
        $invoice_template_id = '';
        $creditnote_template_id = '';
        $label = '';
        $value = '';

        $data = array(
            "display_name" => $display_name,
            "salutation" => $salutation,
            "first_name" => $first_name,
            "last_name" => $last_name,
            "email" => $email,
            // "tags"                  => [array(
            //                         "tag_id"        => $tag_id,
            //                         "tag_option_id" => $tag_option_id,
            // )],
            "company_name" => $company_name,
            "phone" => $phone,
            "mobile" => $mobile,
            "department" => $department,
            "designation" => $designation,
            "website" => $website,
            "billing_address" => array(
                "attention" => $attention,
                "street" => $street,
                "city" => $city,
                "state" => $state,
                "zip" => $zip,
                "country" => $country,
                "country_code" => $country_code,
                "state_code" => $state_code,
                "fax" => $fax,
            ),
            "shipping_address" => array(
                "attention" => $attention,
                "street" => $street,
                "city" => $city,
                "state" => $state,
                "zip" => $zip,
                "country" => $country,
                "country_code" => $country_code,
                "state_code" => $state_code,
                "fax" => $fax,
            ),
            "payment_terms" => $payment_terms,
            "payment_terms_label" => $payment_terms_label,
            "currency_code" => $currency_code,
            "ach_supported" => $ach_supported,
            "twitter" => $twitter,
            "facebook" => $facebook,
            "skype" => $skype,
            "notes" => $notes,
            "is_portal_enabled" => $is_portal_enabled,
            "gst_treatment" => $gst_treatment,
            "default_templates" => array(
                "invoice_template_id" => $invoice_template_id,
                "creditnote_template_id" => $creditnote_template_id,
            ),
        );
        $urlApi = config('icapt.zoho_apisetting.subscriptionBillingUrl');
        $url = "https://" . $urlApi . "/v1/customers";
        $postCustomer = CallHttpService::postApiwithHeader($url, $data, $organizationId, $AuthorizationCode);

        $customer_id = $postCustomer->customer->customer_id;

        $plan_code = $request->plan_code;
        $url = config('app.url');
        $urlSubscription = "https://" . $urlApi . "/v1/hostedpages/newsubscription";
        $dataSubscription = array(
            "customer_id" => $customer_id,
            "plan" => array(
                "plan_code" => $plan_code,
                "price" => $request->price,
                "quantity" => $qty,
            ),
            "redirect_url" => $url . '/webhook/activeNewSite/' . $siteID,
        );

        $postSubscription = CallHttpService::postApiwithHeader($urlSubscription, $dataSubscription, $organizationId, $AuthorizationCode);

        // echo'<pre>';
        // print_r($postSubscription);
        // exit;
        $hostedpage_id = $postSubscription->hostedpage->hostedpage_id;
        $decrypted_hosted_page_id = $postSubscription->hostedpage->decrypted_hosted_page_id;
        $url_payment = $postSubscription->hostedpage->url;

        DB::table('temp_subscription')->insert(
            [
                'hostedpage_id' => $hostedpage_id,
                'decrypted_hosted_page_id' => $decrypted_hosted_page_id,
                'url_payment' => $url_payment,
                'siteID' => $siteID,
                'adminId' => $adminId,
                'plan_id' => $planID,
                'price' => $request->price,
                'create_at' => $now,
                'create_by' => '',
                'update_at' => $now,
                'update_by' => '',
            ]
        );

        // Record Details for email purpose
        // $fh = fopen('new/'.$customer_id.'_info.txt', 'a+') or die;
        // flock($fh, LOCK_EX) or die('Error: Fail to lock file');
        // fwrite($fh, join("\t", array(
        // $site_id,
        // $site_name,
        // $adminUsername,
        // $timezone,
        // )) . "\n");
        // flock($fh, LOCK_UN) or die('Error: Fail to unlock file');
        // fclose($fh);

        $path = public_path('zoho_application/new/' . $customer_id . '_info.txt');
        $fh = fopen($path, 'w+') or die;
        flock($fh, LOCK_EX) or die('Error: Fail to lock file');
        fwrite($fh, join("\t", array(
            $site_id,
            $site_name,
            $adminUsername,
            $timezone,
        )) . "\n");
        flock($fh, LOCK_UN) or die('Error: Fail to unlock file');
        fclose($fh);

        return redirect($url_payment);
    }

    public function existSite(Request $request)
    {

        $data = DB::table('site_settings')->where('site_id', $request->site_id)->exists();

        if ($data) {
            return 'Site ID already taken.';
        }
    }

    public function existUsername(Request $request)
    {

        $data1 = DB::table('users')->where('name', $request->name)->exists();

        if ($data1) {
            return 'Username already taken.';
        }
    }

    public function existEmail(Request $request)
    {
        try {
            if ($request->email) {
                $request->validate([
                    'email' =>
                    [
                        'required',
                        'max:30',
                        'unique:users',
                    ],
                ]);

                $this->validate(request(), [
                    'email' => 'nullable|email:rfc,dns',
                ]);
            }
        } catch (ValidationException $e) {
            $errors = $e->errors();
            $errorString = implode(",", $errors['email']);

            return $errorString;
        }

        // $data2 = DB::table('users')->where('email', $request->email)->exists();

        // if ($data2) {
        //     return 'The email has already been taken.';
        // }
    }

    public function validPassword(Request $request)
    {

        try {
            if ($request->password) {
                $request->validate([
                    'password' => [new StrongPassword, PasswordRules::register($request->name)],
                ]);
            }
        } catch (ValidationException $e) {
            // return $e->errors();
            return "The password must be 8–30 characters, and include a number, a symbol, a lower and an upper case letter.";
        }
    }

    public function validSiteID(Request $request)
    {

        try {
            if ($request->site_id) {
                $request->validate([
                    'site_id' => ['required', 'unique:site_settings', 'alpha_dash', 'max:30',],
                ]);
            }
        } catch (ValidationException $e) {
            $errors = $e->errors();
            //            dd($errors);
            $errorString = implode(",", $errors['site_id']);

            return $errorString;
            //            return "The Site ID may only contain letters, numbers, dashes and underscores.";
        }
    }

    public function validPhoneNum(Request $request)
    {

        $country =  $request->phonecountry;
        $contact = $request->contact;
        $countrycode = $request->phonecountrycode;
        $countryname = $request->phonecountryname;

        $phoneNumber = $countrycode.$contact;

        $data = [
            'phone' => $phoneNumber, // Replace $phoneNumber with your input
        ];

        //dd($data,$phoneNumber,$countrycode,$country);
        // $res = $request->validate( [
        //     'contact' => $contact.':'.$country,  // specify countries (e.g., US, CA for Canada)
        // ]);
        //dd($contact,$request);
        $validator = Validator::make($data, [
            'phone' => 'phone:'.strtoupper($country),
        ],
        [
            'phone.phone' => 'The phone number must be a valid '.ucfirst($countryname).' phone number.',


        ]  // specify countries (e.g., US, CA for Canada)
        );
        if ($validator->fails()) {
            $errors = $validator->errors();
            $errorString = implode(",", $errors->messages()['phone']);

            return $errorString;
        }
        else{
            return 'Valid';
        }

        // $request->merge([
        //     'contact' => $phoneNumber

        // ]);

        // try {
        //     if ($request->contact) {
        //         $request->validate(
        //             [
        //                 'contact' => ['contact:'.strtoupper($country), 'min:10', 'alpha_dash'],
        //             ],
        //             [
        //                // 'contact.max' => __('error.admin.max_phonenum'),
        //                //'contact.min' => __('error.admin.min_phonenum'),
        //                 'contact.alpha_dash' => __('error.admin.alpha_dash'),

        //             ]
        //         );
        //     }
        // } catch (ValidationException $e) {
        //     $errors = $e->errors();
        //     $errorString = implode(",", $errors['contact']);

        //     return $errorString;
        // }
    }

    public function checkURL(Request $request)
    {
        $url = $request->url;
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.0)");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
        curl_setopt($ch, CURLOPT_TIMEOUT, 20);
        $rt = curl_exec($ch);
        $info = curl_getinfo($ch);
       // dd($url,$info);
        return $info["http_code"];
    }
}
