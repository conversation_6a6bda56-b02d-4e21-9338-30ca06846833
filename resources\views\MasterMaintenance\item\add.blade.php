@extends('layout.app')
@section('content')

    <head>
        <style>
            html body .content .content-wrapper {
                margin: 0.6rem 1.2rem;
            }

            .card-header1 {
                margin-bottom: -10px;
            }

            textarea {
                padding: 2px;
            }

            input {
                /* padding-left: 0.75rem !important; */
                height: 25px;
            }

            select#lot_num_def_name.form-control.border-primary,
            select.form-control.border-primary,
            select#issue_by.form-control.border-primary {
                padding: 0rem 0.5rem 1px 0.5rem !important;
                height: 25px !important;
            }

            .select2-container--default .select2-selection--single {
                /* padding-left: 0.75rem !important; */
                padding: 0rem 0.5rem 1px 0.5rem !important;
                height: 25px !important;
            }

            .select2-container--default .select2-selection--single .select2-selection__rendered {
                line-height: 23px;
            }

            .select2-container--default .select2-selection--single .select2-selection__arrow {
                top: 0px !important;
                right: 6px !important;
            }

            .select2-container--default .select2-results__option[aria-disabled=true] {
                display: none;
            }

            .btn-magnify {
                padding: 0.1rem 0.1rem;
                margin-left: 2px;
            }

            .select2-results__option {
                display: block;
                /* overflow-y: auto; */
            }

            fieldset.scheduler-border {
                border: 1px groove #ddd !important;
                padding: 0 0.5em 0em 0.8em !important;
                margin: 0 0 3.5em 0 !important;
                -webkit-box-shadow: 0px 0px 0px 0px #000;
                box-shadow: 0px 0px 0px 0px #000;
            }

            legend.scheduler-border {
                font-size: 1.2em !important;
                font-weight: bold !important;
                text-align: left !important;
                width: auto;
                padding: 0 2px;
                border-bottom: none;
            }
        </style>
    </head>
    <section id="basic-form-layouts">
        <div class="card-header1">
            <h5 class="card-title" id="basic-layout-colored-form-control">{{ __('admin.title.add_item') }}</h5>
        </div>
        <br>
        <form id="add_item_form" class="form" autocomplete="off" action="{{ route('storeitem') }}" method="POST">
            @csrf
            <div class="form-body">
                <div class="form-group">
                    <table>
                        <tr>
                            <td width='200px'><label for="item_num"
                                    class="required">{{ __('admin.label.item_num') }}</label></td>
                            <td width='450px'><input maxlength="30" type="text" id="item_num" tabindex="1"
                                    class="form-control border-primary" placeholder="{{ __('admin.placeholder.item_num') }}"
                                    name="item_num" value="{{ old('item_num') }}" required></td>

                            <td></td>


                            <td><label for="item_status">{{ __('admin.label.active') }}</label></td>
                            <td>
                                <label class="custom-control custom-checkbox">
                                    <input type="hidden" name="item_status" value="0" />
                                    <input type="checkbox" tabindex="12" class="custom-control-input" value="1"
                                        id="item_status" name="item_status" {{ old('item_status', 1) ? 'checked' : '' }}>
                                    <span class="custom-control-indicator"></span>
                                    <span class="custom-control-description"></span>
                                </label>
                            </td>

                        </tr>
                        <tr>
                            <td><label for="item_desc" class="required">{{ __('admin.label.item_desc') }}</label></td>
                            <td>
                                <textarea maxlength="191" type="text" id="item_desc" tabindex="2" class="form-control border-primary"
                                    placeholder="{{ __('admin.placeholder.desc') }}" name="item_desc" required>{{ old('item_desc') }}</textarea>
                            </td>
                            <td></td>


                            <td><label for="item_type">{{ __('admin.label.item_type') }}</label></td>
                            <td>
                                <select tabindex="9" data-placeholder="Select {{ __('admin.label.item_type') }}"
                                    class="form-control select2 border-primary" style="float:left;" name="item_type">
                                    @foreach (App\Item::getItemTypes() as $id => $type_name)
                                        <option value="{{ $id }}">{{ $type_name }}</option>
                                    @endforeach
                                </select>
                            </td>

                        </tr>
                        <tr>
                            <td><label for="uom" class="required">{{ __('admin.label.uom') }}</label></td>
                            <td><input class="form-control border-primary" type="text" tabindex="3"
                                    placeholder="{{ __('admin.placeholder.uom') }}" id="uom" name="uom"
                                    value="{{ old('uom') }}" required></td>
                            <td><button type="button" name="{{ __('admin.list.uoms') }}"
                                    onClick="selection('/getUOM','uom','uom','uom');modalheader(this.id, this.name);"
                                    class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                                    data-target="#myModal"><i class="icon-search"></i></button></td>


                            <td><label for="count_group">{{ __('admin.label.count_group') }}</label></td>
                            <td>
                                <select tabindex="10" data-placeholder="Select {{ __('admin.label.count_group') }}"
                                    class="form-control select2 border-primary" style="float:left;" name="count_group_id">
                                    <option value="" {{ old('count_group_id') == '' ? 'selected' : '' }}></option>
                                    @foreach ($countGroups as $countGroup)
                                        <option value="{{ $countGroup->id }}"
                                            {{ old('count_group_id') == $countGroup->id ? 'selected' : '' }}>
                                            {{ $countGroup->count_group_name }}</option>
                                    @endforeach
                                </select>
                            </td>


                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                            <td><input tabindex="4" class="form-control border-primary" type="text" id="uom_desc"
                                    name="uom_desc" value="{{ old('uom_desc') }}" readonly></td>




                            <td width='100px'></td>
                            <td width='280px'><label for="unit_weight">{{ __('admin.label.unit_weight') }}</label></td>
                            <td width='450px'><input type="text" style="text-align:right" id="unit_weight"
                                    tabindex="8" class="form-control border-primary number-format"
                                    placeholder="{{ __('admin.placeholder.unit_weight') }}" name="unit_weight"
                                    value="{{ numberFormatPrecision(old('unit_weight'), $unit_quantity_format) }}"
                                    inputmode="decimal">
                            </td>
                            <td width='75px'><input type="text" class="form-control border-primary"
                                    name="unit_weight_uom" value="KG" readonly></td>


                        </tr>
                        <tr>
                            <td><label for="product_code" class="required">{{ __('admin.label.product_code') }}</label>
                            </td>
                            <td><input type="text" id="product_code" tabindex="5"
                                    class="form-control border-primary"
                                    placeholder="{{ __('admin.placeholder.product_code') }}" name="product_code"
                                    value="{{ old('product_code') }}" required></td>
                            <td><button type="button" name="Product Code"
                                    onClick="selection('/getProdCode','product_code','product_code','product_code');modalheader(this.id, this.name);"
                                    class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                                    data-target="#myModal"><i class="icon-search"></i></button></td>

                            <td><label for="unit_length">{{ __('admin.label.unit_length') }}</label></td>
                            <td>
                                <input type="text" style="text-align:right" id="unit_length" tabindex="8"
                                    class="form-control border-primary number-format"
                                    placeholder="{{ __('admin.placeholder.unit_length') }}" name="unit_length"
                                    value="{{ numberFormatPrecision(old('unit_length'), $unit_quantity_format) }}"
                                    inputmode="decimal">
                            </td>


                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                            <td><input tabindex="6" class="form-control border-primary" type="text"
                                    id="product_desc" name="product_desc" value="{{ old('product_desc') }}" readonly>
                            </td>
                            <td></td>

                            <td><label for="unit_width">{{ __('admin.label.unit_width') }}</label></td>
                            <td>
                                <input type="text" style="text-align:right" id="unit_width" tabindex="8"
                                    class="form-control border-primary number-format"
                                    placeholder="{{ __('admin.placeholder.unit_width') }}" name="unit_width"
                                    value="{{ numberFormatPrecision(old('unit_width'), $unit_quantity_format) }}"
                                    inputmode="decimal">
                            </td>

                        </tr>
                        <tr>
                            <td><label for="lot_tracked">{{ __('admin.label.lot_tracked') }}</label></td>
                            <td>
                                <label class="custom-control custom-checkbox">
                                    <input type="hidden" name="lot_tracked" value="0" />
                                    <input type="checkbox" class="custom-control-input" value="1" id="lot_tracked"
                                        name="lot_tracked" tabindex="7" {{ old('lot_tracked', 1) ? 'checked' : '' }}>
                                    <span class="custom-control-indicator"></span>
                                    <span class="custom-control-description"></span>
                                </label>
                            </td>
                            <td></td>

                            <td><label for="unit_height">{{ __('admin.label.unit_height') }}</label></td>
                            <td>
                                <input type="text" style="text-align:right" id="unit_height" tabindex="8"
                                    class="form-control border-primary number-format"
                                    placeholder="{{ __('admin.placeholder.unit_height') }}" name="unit_height"
                                    value="{{ numberFormatPrecision(old('unit_height'), $unit_quantity_format) }}"
                                    inputmode="decimal">
                            </td>


                        </tr>
                        <tr>
                            <td><label for="issue_by" class="required">{{ __('admin.label.issue_by') }}</label></td>
                            <td>
                                <select tabindex="11" data-placeholder="Select {{ __('admin.label.issue_by') }}"
                                    class="form-control border-primary select2" id="issue_by" name="issue_by" required>
                                    <option value="Location" {{ $def_lot_item == 'Location' ? 'selected' : '' }}>
                                        {{ __('admin.label.loc_num') }}</option>
                                    <option value="FEFO" {{ $def_lot_item == 'FEFO' ? 'selected' : '' }}>
                                        {{ __('admin.label.fefo') }}</option>
                                    <option value="FIFO" {{ $def_lot_item == 'FIFO' ? 'selected' : '' }}>
                                        {{ __('admin.label.fifo') }}</option>
                                    <option value="Lot" {{ $def_lot_item == 'Lot' ? 'selected' : '' }}>
                                        {{ __('admin.label.lot') }}</option>
                                    <!-- <option selected value="Location">{{ __('admin.label.loc_num') }}</option>
                                                                                                                                                                                                                                                                                                                                                                                                    <option value="FEFO">{{ __('admin.label.fefo') }}</option>
                                                                                                                                                                                                                                                                                                                                                                                                    <option value="FIFO">{{ __('admin.label.fifo') }}</option>
                                                                                                                                                                                                                                                                                                                                                                                                    <option value="Lot">{{ __('admin.label.lot') }}</option> -->
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td><label for="expiry_tracked">{{ __('admin.label.expiry_tracked') }}</label> </td>
                            <td>
                                <label class="custom-control custom-checkbox">
                                    <input type="hidden" name="expiry_tracked" value="0" />
                                    <input id="show_expiry_tracked_checkbox" type="checkbox" class="custom-control-input"
                                        value="1" id="expiry_tracked" name="expiry_tracked">
                                    <span class="custom-control-indicator"></span>
                                    <span class="custom-control-description"></span>
                                </label>
                            </td>
                            <td></td>
                            @if ($issue_by_item_base_uom == 1)
                                <td><label for="product_code">{{ __('admin.label.matl_conv') }}:</label></td>
                            @endif

                        </tr>
                        <tr>
                            <td><label for="default_shelf_life">{{ __('admin.label.default_shelf_life') }}</label></td>
                            <td><input id="show_default_shelf_life" class="form-control border-primary" type="number"
                                    min=1 maxlength="10" id="default_shelf_life" name="default_shelf_life"
                                    oninput="validity.valid||(value=value);"></td>
                            <td>{{ __('admin.label.days') }}</label></td>

                            @if ($issue_by_item_base_uom == 1)
                                <td><label for="issue_by_unit">{{ __('admin.label.issue_by_unit') }}</label></td>
                                <td>
                                    <label class="custom-control custom-checkbox">
                                        <input type="hidden" name="issue_by_unit" value="0" />
                                        <input type="checkbox" tabindex="12" class="custom-control-input"
                                            value="1" id="issue_by_unit" name="issue_by_unit"
                                            {{ old('issue_by_unit', 1) ? 'unchecked' : '' }}>
                                        <span class="custom-control-indicator"></span>
                                        <span class="custom-control-description"></span>
                                    </label>
                                </td>
                            @endif

                        </tr>
                        <tr>
                            <td><label for="lot_number_definition">{{ __('admin.menu.lot_number_definition') }}</label>
                            </td>
                            <td>
                                <select id="show_lot_number_definition"
                                    data-placeholder="Select {{ __('admin.menu.lot_number_definition') }}"
                                    class="form-control border-primary select2" id="lot_num_def_name"
                                    name="lot_num_def_name">
                                    <option selected></option>
                                    @if (count($lotnumberdefinitions) > 0)
                                        @foreach ($lotnumberdefinitions as $lotnumberdefinition)
                                            <option value="{{ $lotnumberdefinition->name }}">
                                                {{ $lotnumberdefinition->display_lot_num_def }}
                                                ({{ $lotnumberdefinition->name }})
                                            </option>
                                        @endforeach
                                    @endif
                                </select>
                            </td>
                            <td></td>
                            @if ($issue_by_item_base_uom == 1)
                                <td><label for="round_up">{{ __('admin.label.round_up_total') }}</label></td>
                                <td>
                                    <label class="custom-control custom-checkbox">
                                        <input type="hidden" name="round_up" value="0" />
                                        <input type="checkbox" tabindex="12" class="custom-control-input"
                                            value="1" id="round_up" name="round_up"
                                            {{ old('round_up', 1) ? 'unchecked' : '' }}>
                                        <span class="custom-control-indicator"></span>
                                        <span class="custom-control-description"></span>
                                    </label>
                                </td>
                            @endif

                        </tr>

                        <tr>
                            <td><label for="lot_prefix">{{ __('admin.label.lot_prefix') }}</label>
                            </td>
                            <td>
                                <input maxlength="15" type="text" id="lot_prefix" tabindex="13"
                                    class="form-control border-primary" placeholder="{{ __('admin.label.lot_prefix') }}"
                                    name="lot_prefix" value="{{ old('lot_prefix') }}">
                            </td>
                        </tr>

                        <tr>
                            {{-- <td class="alignLabel"><label for="issue_by" class="required">{{__('admin.label.issue_by')}}</label></td>
                            <td>
                                <select class="form-control border-primary" id="issue_by" name="issue_by" required>
                                    <option selected value="Location">{{__('admin.label.loc_num')}}</option>
                                    <option value="FEFO">{{__('admin.label.fefo')}}</option>
                                    <option value="FIFO">{{__('admin.label.fifo')}}</option>
                                    <option value="Lot">{{__('admin.label.lot')}}</option>
                                </select>
                            </td> --}}
                        </tr>
                        <tr>
                            {{-- <td class="alignLabel"><label for="item_status">{{__('admin.label.active')}}</label></td>
                            <td>
                                <label class="custom-control custom-checkbox">
                                    <input type="hidden" name="item_status" value="0" />
                                    <input type="checkbox" class="custom-control-input" value="1" id="item_status" name="item_status" {{old('item_status', 1) ? 'checked' : ''}} >
                                    <span class="custom-control-indicator"></span>
                                    <span class="custom-control-description"></span>
                                </label>
                            </td> --}}
                        </tr>
                        @include('util.custom_fields_add')


                        <tr>
                            <td colspan="2">
                                <fieldset class="scheduler-border">
                                    <legend class="scheduler-border">Catch Weight</legend>
                                    <table width="100%" border="0">
                                        <tr>
                                            <td width="28%"><label
                                                    for="catch_weight">{{ __('admin.label.catch_weight') }}</label>
                                            </td>
                                            <td>
                                                <label class="custom-control custom-checkbox">
                                                    <input type="hidden" name="catch_weight" value="0" />
                                                    <input type="checkbox" tabindex="12" class="custom-control-input"
                                                        value="1" id="catch_weight" name="catch_weight"
                                                        {{ old('catch_weight', 1) ? 'unchecked' : '' }}>
                                                    <span class="custom-control-indicator"></span>
                                                    <span class="custom-control-description"></span>
                                                </label>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><label
                                                    for="catch_weight_tolerance">{{ __('admin.label.catch_weight_tolerance') }}</label>
                                            </td>
                                            <td>
                                                <input type="text" style="text-align:right"
                                                    id="catch_weight_tolerance" tabindex="11"
                                                    class="form-control border-primary number-format"
                                                    placeholder="{{ __('admin.label.catch_weight_tolerance') }}"
                                                    name="catch_weight_tolerance"
                                                    value="{{ numberFormatPrecision(old('catch_weight_tolerance'), $unit_quantity_format) }}"
                                                    inputmode="decimal"
                                                    maxlength="30"
                                                    max="100">

                                            </td>
                                            <td>%</label>






                                            </td>
                                        </tr>
                                    </table>
                                </fieldset>

                            </td>
                        </tr>



                    </table>
                </div>
            </div>






            <div class="form-actions center">
                <button type="button" class="btn btn-warning mr-1" onclick="location.href='{{ route('item') }}';">
                    <i class="icon-cross2"></i> {{ __('admin.button.cancel') }}
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="icon-check2"></i> {{ __('admin.button.save') }}
                </button>
            </div>
        </form>

    </section>

    @include('errors.maxchar')
    <script type="text/javascript">
        var must_lot_tracked_value = ["FIFO", "FEFO", "Lot"];
        var default_issued_by = "{{ $def_lot_item }}" || 'Location';

        $(document).ready(function() {

            $(".select2").select2({
                width: "100%"
            });
            checkLotTracked();

            function checkLotTracked() {
                if ($("#lot_tracked").is(':checked')) {

                    $("#show_expiry_tracked_checkbox").prop('disabled', false);
                    $("#show_default_shelf_life").prop('disabled', false);
                    $("#show_lot_number_definition").prop('disabled', false);
                    $("#lot_prefix").prop('disabled', false);

                    $("#catch_weight").prop('disabled', false);
                    $("#catch_weight_tolerance").prop('disabled', false);



                    $.each(must_lot_tracked_value, function(index, value) {
                        $("#issue_by option[value='" + value + "']").prop('disabled', false);
                    });
                } else {
                    $("#show_expiry_tracked_checkbox").prop('checked', false);
                    $("#show_expiry_tracked_checkbox").prop('disabled', true);
                    $("#show_default_shelf_life").prop('disabled', true);
                    $("#show_default_shelf_life").val('');
                    $("#show_lot_number_definition").prop("selectedIndex", 0).trigger("change");
                    $("#show_lot_number_definition").prop('disabled', true);
                    $("#lot_prefix").prop('disabled', true);


                    $("#catch_weight").prop('disabled', true);
                    $("#catch_weight_tolerance").prop('disabled', true);
                    $("#catch_weight_tolerance").val('0.000');


                    $.each(must_lot_tracked_value, function(index, value) {
                        $("#issue_by option[value='" + value + "']").prop('disabled', true);
                    });
                }

                if (must_lot_tracked_value.includes(default_issued_by) && $("#lot_tracked").is(':checked')) {
                    $("#issue_by").val(default_issued_by);
                } else if (!must_lot_tracked_value.includes(default_issued_by)) {
                    $("#issue_by").val(default_issued_by);
                } else {
                    $("#issue_by").val('Location');
                }

                $("#issue_by").select2("destroy");
                $("#issue_by").select2({
                    width: "100%"
                });
            }

            $("#lot_tracked").on("change", checkLotTracked);

            $("#default_shelf_life").on("change", function() {
                if ($("#default_shelf_life").val() != "") {
                    if ($("#default_shelf_life").val() <= 0) {
                        $("#default_shelf_life").val(1);
                    } else {
                        $("#default_shelf_life").val(parseInt($("#default_shelf_life").val()));
                    }
                }
            });


            $("#uom").on("change", function() {
                if ($("#uom").val() != '') {
                    ajaxurl = "{{ route('uomv', 'uom_num') }}";
                    url = ajaxurl.replace('uom_num', btoa($("#uom").val()));

                    $.get(url, function(data) {
                        console.log(url);
                        if (data == 'not exist') {
                            Alert.notexist('{{ __('UOM') }}', $("#uom").val());
                            //$("#uom").val('');
                        } else {
                            display('/displayUOMDesc', 'uom', 'uom_desc');
                        }
                    });
                }
            });

            $("#product_code").on('change', function() {
                if ($("#product_code").val()) {
                    // Send error if manually type object that is not exist or inactive
                    $.ajax({
                        url: '{{ route('prodcodev2') }}',
                        type: "GET",
                        data: {
                            product_code: btoa($("#product_code").val()),
                        },
                        success: function(data) {
                            if (data == "inactive") {
                                Alert.inactive('{{ __('admin.label.product_code') }}', $(
                                    "#product_code").val());
                                // $("#product_code").val('');
                            } else if (data == "not exist") {
                                Alert.notexist('{{ __('admin.label.product_code') }}', $(
                                    "#product_code").val());
                                // $("#product_code").val('');
                            } else if (data == "active") {
                                display('/displayProdCodeDesc', 'product_code', 'product_desc');
                            }
                        }
                    });
                }
            });
        });

        // Create new rule for jquery validation to check lot tracked
        jQuery.validator.addMethod("lotTracked", function(value, element) {
            let lotTrackedValue = !$("#lot_tracked").is(':checked') ? must_lot_tracked_value : [];
            return (this.optional(element) || !lotTrackedValue.includes(value));
        }, "{{ __('error.admin.must_lot_tracked') }}");

        var errMsgProductCode = "";
        var errMsgUOM = "";

        $("#add_item_form").validate({
            onChange: true,
            rules: {
                item_num: {
                    required: true,
                },
                issue_by: {
                    lotTracked: true
                },
                unit_weight: {
                    number: true,
                    number_size: true,
                },
                product_code: {
                    remote: {
                        url: "{{ route('prodcodev2') }}",
                        type: "GET",
                        data: {
                            product_code: () => btoa($("#product_code").val()),
                        },
                        dataFilter: function(data) {
                            if (data == "inactive") {
                                // Alert.inactive('{{ __('admin.label.product_code') }}',$("#product_code").val());
                                // $("#product_code").val('');
                                errMsgProductCode =
                                    "{{ __('error.admin.inactive', ['resource' => __('admin.label.product_code'), 'name' => 'empty']) }}";
                                errMsgProductCode = errMsgProductCode.replace(/\[([^\]]+)\]/g, $(
                                    "#product_code").val());
                            } else if (data == "not exist") {
                                errMsgProductCode =
                                    "{{ __('error.admin.notexist', ['resource' => __('admin.label.product_code')]) }}";
                            } else {
                                return true;
                            }

                            return false;
                        }
                    }
                },
                uom: {
                    remote: {
                        url: "{{ route('uomexists') }}",
                        type: "GET",
                        data: {
                            uom: () => btoa($("#uom").val()),
                        },
                        dataFilter: function(data) {

                            if (data == 'not exist') {
                                errMsgUOM =
                                    "{{ __('error.admin.notexist', ['resource' => __('admin.label.uom')]) }}";
                            } else {
                                return true;
                            }




                            /*if (data == "inactive") {
                                // Alert.inactive('{{ __('admin.label.product_code') }}',$("#product_code").val());
                                // $("#product_code").val('');
                                errMsgProductCode =
                                    "{{ __('error.admin.inactive', ['resource' => __('admin.label.product_code'), 'name' => 'empty']) }}";
                                errMsgProductCode = errMsgProductCode.replace(/\[([^\]]+)\]/g, $(
                                    "#product_code").val());
                            } else if (data == "not exist") {
                                errMsgProductCode =
                                    "{{ __('error.admin.notexist', ['resource' => __('admin.label.product_code')]) }}";
                            } else {
                                return true;
                            }*/

                            return false;
                        }
                    }
                }


            },
            messages: {
                unit_weight: {
                    number: "{{ __('error.mobile.numbersonly', ['resource' => __('admin.label.unit_weight')]) }}",
                    number_size: "{{ __('error.mobile.max_characters') }}"
                },
                product_code: {
                    remote: () => errMsgProductCode
                },
                uom: {
                    remote: () => errMsgUOM
                }
            },

            submitHandler: function(form) {
                $(".pageloader").css("display", "block");
                $(".submitloader").attr("disabled", true);
                setTimeout(function() {
                    form.submit();
                }, 300);
            }
        });
    </script>
    @include('util.selection')
@endsection
